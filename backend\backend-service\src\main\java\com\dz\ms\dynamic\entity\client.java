package com.dz.ms.dynamic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户实体类
 * 由低代码平台自动生成
 */
@Data
@TableName("lc_client")
public class client implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 客户名称 */
    @TableField("name")
    private String name;
    
    /** 执照编号 */
    @TableField("zz_code")
    private String zzCode;
    
    /** 城市 */
    @TableField("city")
    private String city;
    
    /** 创建人 */
    @TableField("creator")
    private String creator;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 更新人 */
    @TableField("updater")
    private String updater;

    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;
} 