# RTM 项目 Sentinel 熔断限流功能

## 🎯 功能概述

本项目已成功集成 Alibaba Sentinel 熔断限流功能，提供以下核心能力：

- **流量控制**：支持 QPS 和并发线程数限流
- **熔断降级**：支持慢调用比例、异常比例和异常数熔断
- **系统负载保护**：支持 CPU 使用率、平均 RT、入口 QPS、线程数等系统指标保护
- **热点参数限流**：支持热点参数的精细化限流
- **网关限流**：支持 Spring Cloud Gateway 的路由级别限流

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │───▶│   网关服务      │───▶│   后端服务      │
│   (Vue.js)      │    │   (Gateway)     │    │ (Backend-Service)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Sentinel 网关   │    │ Sentinel 应用   │
                       │     限流        │    │     限流        │
                       └─────────────────┘    └─────────────────┘
                              │                        │
                              └────────┬───────────────┘
                                       ▼
                              ┌─────────────────┐
                              │ Sentinel 控制台 │
                              │   (Dashboard)   │
                              └─────────────────┘
                                       │
                                       ▼
                              ┌─────────────────┐
                              │ Nacos 配置中心  │
                              │  (规则持久化)   │
                              └─────────────────┘
```

## 📦 已集成的组件

### 1. 依赖组件
- `spring-cloud-starter-alibaba-sentinel` - Sentinel 核心组件
- `spring-cloud-alibaba-sentinel-gateway` - 网关限流组件
- `sentinel-datasource-nacos` - Nacos 数据源
- `sentinel-annotation-aspectj` - 注解支持

### 2. 配置文件
- `backend-service-config.yaml` - 后端服务 Sentinel 配置
- `gateway-config.yaml` - 网关服务 Sentinel 配置

### 3. 配置类
- `SentinelConfig.java` - 后端服务 Sentinel 配置类
- `SentinelGatewayConfig.java` - 网关 Sentinel 配置类

## 🚀 快速开始

### 1. 启动基础服务

```bash
# 1. 启动 Nacos
cd nacos/bin
./startup.sh -m standalone

# 2. 启动 Sentinel 控制台
./scripts/start-sentinel-dashboard.sh
```

### 2. 配置 Nacos 规则

在 Nacos 控制台 (http://localhost:8848/nacos) 中创建以下配置：

| Data ID | Group | 说明 |
|---------|-------|------|
| `backend-service-flow-rules` | `SENTINEL_GROUP` | 后端服务流控规则 |
| `backend-service-degrade-rules` | `SENTINEL_GROUP` | 后端服务熔断规则 |
| `backend-service-system-rules` | `SENTINEL_GROUP` | 后端服务系统规则 |
| `gateway-service-gw-flow-rules` | `SENTINEL_GROUP` | 网关流控规则 |
| `gateway-service-gw-api-group-rules` | `SENTINEL_GROUP` | 网关API分组规则 |

配置内容参考 `nacos-configs/` 目录下的 JSON 文件。

### 3. 启动应用服务

```bash
# 3. 启动网关服务
cd backend/gateway
mvn spring-boot:run

# 4. 启动后端服务
cd backend/backend-service
mvn spring-boot:run
```

### 4. 验证功能

访问 Sentinel 控制台：http://localhost:8080
- 用户名：sentinel
- 密码：sentinel123

## 🔧 限流规则配置

### 1. 接口级别限流

已为以下关键接口配置限流保护：

| 接口 | 资源名 | QPS 限制 | 说明 |
|------|--------|----------|------|
| 用户管理 | `userController` | 10 | 用户增删改查接口 |
| 会员管理 | `memberController` | 20 | 会员增删改查接口 |
| 菜单管理 | `menuController` | 50 | 菜单树查询接口 |

### 2. 网关级别限流

| 路由 | QPS 限制 | 说明 |
|------|----------|------|
| `backend-service` | 100 | 后端服务整体限流 |
| `user_api` | 20 | 用户API限流 |
| `member_api` | 30 | 会员API限流 |

## 🛡️ 熔断规则配置

### 1. 异常比例熔断
- **用户接口**：异常比例超过 50% 时熔断 10 秒
- **最小请求数**：5 个请求

### 2. 响应时间熔断
- **会员接口**：平均响应时间超过 1000ms 时熔断 10 秒
- **最小请求数**：5 个请求

### 3. 异常数熔断
- **菜单接口**：异常数超过 5 个时熔断 10 秒
- **最小请求数**：3 个请求

## 🧪 测试验证

### 1. 运行测试用例

```bash
# 运行限流测试
mvn test -Dtest=SentinelFlowControlTest

# 运行熔断测试
mvn test -Dtest=SentinelDegradeTest
```

### 2. 手动测试限流

```bash
# 快速发送多个请求测试限流
for i in {1..20}; do
  curl -X GET "http://localhost:9090/api/user/page?pageNum=1&pageSize=10" &
done
```

### 3. 查看监控数据

在 Sentinel 控制台中可以查看：
- 实时监控数据
- 流控规则效果
- 熔断状态变化
- 系统负载情况

## 📊 监控指标

### 1. 核心指标
- **QPS**：每秒查询率
- **RT**：平均响应时间
- **异常比例**：异常请求占比
- **线程数**：活跃线程数量

### 2. 系统指标
- **CPU 使用率**：系统 CPU 负载
- **系统负载**：系统平均负载
- **入口 QPS**：系统入口流量

## 🔍 故障排查

### 1. 常见问题

**控制台看不到应用**
- 检查 dashboard 地址配置
- 确认端口连通性
- 查看应用启动日志

**规则不生效**
- 检查 Nacos 配置
- 确认 dataId 和 group
- 查看规则加载日志

**限流不准确**
- 检查时间窗口设置
- 确认资源名称
- 查看统计数据

### 2. 日志查看

```bash
# 查看 Sentinel 相关日志
tail -f logs/backend-service.log | grep Sentinel

# 查看限流记录
tail -f logs/sentinel-record.log
```

## 📚 相关文档

- [Sentinel 部署指南](docs/sentinel-deployment-guide.md)
- [Nacos 配置说明](nacos-configs/)
- [测试用例文档](backend/backend-service/src/test/java/com/dz/ms/sentinel/)

## 🎉 总结

✅ **已完成的功能**：
- Sentinel 依赖集成
- 基础配置设置
- Nacos 配置中心集成
- 关键接口限流保护
- 网关层限流配置
- 自定义配置类
- 测试用例编写
- 部署文档和脚本

🚀 **立即可用**：
- 启动服务后即可享受完整的熔断限流保护
- 通过 Sentinel 控制台实时监控和调整规则
- 支持动态配置，无需重启服务

💡 **建议**：
- 根据实际业务负载调整限流阈值
- 定期查看监控数据，优化规则配置
- 建立告警机制，及时发现异常情况
