<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dz.ms.mapper.RoleMenuMapper">
    <resultMap id="BaseResultMap" type="com.dz.ms.entity.RoleMenu">
        <id property="id" column="id" />
        <result property="roleId" column="role_id" />
        <result property="menuId" column="menu_id" />
        <result property="createTime" column="create_time" />
    </resultMap>

    <!-- 根据角色ID列表获取关联的菜单ID列表 -->
    <select id="selectMenuIdsByRoleIds" resultType="java.lang.Long">
        SELECT DISTINCT menu_id
        FROM role_menu
        WHERE role_id IN
        <foreach item="roleId" collection="roleIds" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>

</mapper> 