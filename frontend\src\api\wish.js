import request from '../utils/request';
export function getWishPage(params) {
    return request.get('/wish/page', { params });
}
export function addWish(data) {
    return request.post('/wish', data);
}
export function updateWish(id, data) {
    return request.put(`/wish/${id}`, data);
}
export function deleteWish(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.delete(`/wish/${id}`);
}
export function getWishDetail(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.get(`/wish/${id}`);
}
