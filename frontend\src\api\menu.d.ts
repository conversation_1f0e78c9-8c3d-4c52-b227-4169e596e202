export interface Menu {
    id?: number;
    parentId?: number;
    name: string;
    path?: string;
    component?: string;
    icon?: string;
    type: string;
    permission?: string;
    orderNum?: number;
    visible?: number;
    status?: number;
    createTime?: string;
    updateTime?: string;
    children?: Menu[];
}
export declare function getMenuTree(): Promise<import("axios").AxiosResponse<any, any>>;
export declare function addMenu(data: Menu): Promise<import("axios").AxiosResponse<any, any>>;
export declare function updateMenu(id: number, data: Menu): Promise<import("axios").AxiosResponse<any, any>>;
export declare function deleteMenu(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getMenuDetail(id: number): Promise<import("axios").AxiosResponse<any, any>>;
