#!/bin/bash

# Sentinel 控制台停止脚本
# 作者: AI
# 日期: 2025-06-29

PID_FILE="sentinel-dashboard.pid"

if [ -f "$PID_FILE" ]; then
    PID=$(cat $PID_FILE)
    
    if ps -p $PID > /dev/null; then
        echo "正在停止 Sentinel 控制台，进程ID: $PID"
        kill $PID
        
        # 等待进程结束
        sleep 3
        
        # 强制杀死进程（如果还在运行）
        if ps -p $PID > /dev/null; then
            echo "强制停止进程..."
            kill -9 $PID
        fi
        
        echo "Sentinel 控制台已停止"
    else
        echo "Sentinel 控制台进程不存在"
    fi
    
    rm -f $PID_FILE
else
    echo "PID 文件不存在，尝试通过端口查找进程..."
    
    # 通过端口查找进程
    PID=$(lsof -ti:8080)
    if [ ! -z "$PID" ]; then
        echo "找到占用端口 8080 的进程: $PID"
        kill $PID
        echo "进程已停止"
    else
        echo "未找到占用端口 8080 的进程"
    fi
fi
