{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:type-check": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "element-plus": "^2.9.11", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "sass": "^1.89.1", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}