/// <reference types="../../../node_modules/.vue-global-types/vue_3.5_0_0_0.d.ts" />
import { reactive, ref, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { useMenuStore } from '../../store/menu';
import { usePermissionStore } from '../../store/permission';
import { getUserMenus } from '../../api/user';
import { getCaptcha, login } from '../../api/auth';
import { loadDynamicRoutes } from '../../router';
import { User, Lock, Key } from '@element-plus/icons-vue';
// 图标已全局注册;
// 登录表单数据
const loginForm = reactive({
    username: '',
    password: '',
    captcha: '',
    remember: false
});
// 验证码相关
const captchaUrl = ref('');
const captchaId = ref('');
// 刷新验证码
/**
 * Refreshes the captcha image by:
 * 1. Generating a new captcha ID
 * 2. Fetching the captcha image blob from server
 * 3. Creating and managing Blob URL for the image
 * 4. Handling image loading states and fallbacks
 * 5. Properly cleaning up previous Blob URLs to prevent memory leaks
 *
 * @async
 * @throws {Error} When captcha fetch fails
 * @emits console.log Debug information about captcha loading process
 * @emits console.error Error information when captcha fails to load
 * @emits ElMessage.error User-facing error message when captcha fetch fails
 */
const refreshCaptcha = async () => {
    try {
        captchaId.value = Date.now().toString();
        const captchaBlob = await getCaptcha(captchaId.value);
        console.log("验证码获取：", captchaBlob);
        // 释放之前的Blob URL，避免内存泄漏
        if (captchaUrl.value && captchaUrl.value.startsWith('blob:')) {
            URL.revokeObjectURL(captchaUrl.value);
        }
        // 创建新的Blob URL
        captchaUrl.value = URL.createObjectURL(captchaBlob);
        // // 强制更新视图
        // await nextTick();
        // console.log('验证码已刷新', {
        //   blobType: captchaBlob.type,
        //   blobSize: captchaBlob.size,
        //   blobUrl: captchaUrl.value
        // });
        // 检查图片加载状态
        const img = new Image();
        img.onload = () => {
            console.log('图片加载成功', {
                naturalWidth: img.naturalWidth,
                naturalHeight: img.naturalHeight
            });
            // 检查DOM中的图片元素
            const domImg = document.querySelector('.captcha-image img');
            console.log('DOM图片状态', {
                src: domImg?.src,
                complete: domImg?.complete,
                naturalWidth: domImg?.naturalWidth,
                naturalHeight: domImg?.naturalHeight
            });
        };
        img.onerror = (e) => {
            console.error('图片加载失败', e);
            // 尝试直接创建Base64 URL作为备用方案
            const reader = new FileReader();
            reader.onload = (e) => {
                console.log('Base64图片数据', e.target?.result);
                captchaUrl.value = e.target?.result;
            };
            reader.readAsDataURL(captchaBlob);
        };
        img.src = captchaUrl.value;
    }
    catch (error) {
        console.error('获取验证码失败:', error);
        ElMessage.error('获取验证码失败，请刷新页面重试');
    }
};
// 表单引用和加载状态
const loginFormRef = ref();
const loading = ref(false);
// 表单验证规则
const loginRules = {
    username: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' }
    ],
    captcha: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { min: 4, max: 6, message: '验证码长度不正确', trigger: 'blur' }
    ]
};
// 路由和状态管理
const router = useRouter();
const menuStore = useMenuStore();
const permissionStore = usePermissionStore();
// 登录处理
const handleLogin = () => {
    loginFormRef.value?.validate(async (valid) => {
        if (valid) {
            loading.value = true;
            try {
                // 发送登录请求
                const res = await login({
                    ...loginForm,
                    captchaId: captchaId.value
                });
                if (res && res.code === 0) {
                    const token = res.data;
                    // 保存token和记住我状态
                    localStorage.setItem('token', token);
                    if (loginForm.remember) {
                        localStorage.setItem('remember', 'true');
                        localStorage.setItem('username', loginForm.username);
                    }
                    else {
                        localStorage.removeItem('remember');
                        localStorage.removeItem('username');
                    }
                    // 清除旧的权限数据
                    permissionStore.clearPermissions();
                    menuStore.setMenuTree([]);
                    try {
                        // 获取用户菜单
                        const menuRes = await getUserMenus();
                        if (menuRes && menuRes.code === 0 && menuRes.data) {
                            // 保存菜单树
                            menuStore.setMenuTree(menuRes.data);
                            // 从菜单中提取权限
                            const permissions = [];
                            const extractPermissions = (menus) => {
                                menus.forEach(menu => {
                                    if (menu.permission) {
                                        permissions.push(menu.permission);
                                    }
                                    if (menu.children && menu.children.length > 0) {
                                        extractPermissions(menu.children);
                                    }
                                });
                            };
                            extractPermissions(menuRes.data);
                            permissionStore.setPermissions(permissions);
                            // 添加动态路由
                            loadDynamicRoutes(menuRes.data);
                            ElMessage.success('登录成功');
                            // 延迟跳转，确保路由已添加完成
                            setTimeout(() => {
                                router.push('/welcome');
                            }, 100);
                        }
                        else {
                            ElMessage.error(menuRes.msg || '获取菜单失败');
                            localStorage.removeItem('token');
                            refreshCaptcha();
                        }
                    }
                    catch (menuError) {
                        console.error('获取菜单错误:', menuError);
                        ElMessage.error('获取用户权限失败');
                        localStorage.removeItem('token');
                        refreshCaptcha();
                    }
                }
                else {
                    ElMessage.error(res.data?.msg || '登录失败');
                    refreshCaptcha();
                }
            }
            catch (error) {
                console.error('登录请求失败:', error);
                ElMessage.error('登录失败，请稍后重试');
                refreshCaptcha();
            }
            finally {
                loading.value = false;
            }
        }
    });
};
// 页面加载时初始化
onMounted(() => {
    // 刷新验证码
    refreshCaptcha();
    // 检查是否记住了用户名
    const remembered = localStorage.getItem('remember');
    if (remembered === 'true') {
        const savedUsername = localStorage.getItem('username');
        if (savedUsername) {
            loginForm.username = savedUsername;
            loginForm.remember = true;
        }
    }
});
// 组件卸载时释放Blob URL，避免内存泄漏
onUnmounted(() => {
    if (captchaUrl.value && captchaUrl.value.startsWith('blob:')) {
        URL.revokeObjectURL(captchaUrl.value);
    }
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['login-box']} */ ;
/** @type {__VLS_StyleScopedClasses['login-left']} */ ;
/** @type {__VLS_StyleScopedClasses['login-right']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "login-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "login-box" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "login-left" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "system-info" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h1, __VLS_intrinsicElements.h1)({
    ...{ class: "system-title" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
    ...{ class: "system-desc" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "system-features" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "feature-item" },
});
const __VLS_0 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({}));
const __VLS_2 = __VLS_1({}, ...__VLS_functionalComponentArgsRest(__VLS_1));
__VLS_3.slots.default;
const __VLS_4 = {}.Lock;
/** @type {[typeof __VLS_components.Lock, ]} */ ;
// @ts-ignore
const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({}));
const __VLS_6 = __VLS_5({}, ...__VLS_functionalComponentArgsRest(__VLS_5));
var __VLS_3;
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "feature-item" },
});
const __VLS_8 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({}));
const __VLS_10 = __VLS_9({}, ...__VLS_functionalComponentArgsRest(__VLS_9));
__VLS_11.slots.default;
const __VLS_12 = {}.DataAnalysis;
/** @type {[typeof __VLS_components.DataAnalysis, ]} */ ;
// @ts-ignore
const __VLS_13 = __VLS_asFunctionalComponent(__VLS_12, new __VLS_12({}));
const __VLS_14 = __VLS_13({}, ...__VLS_functionalComponentArgsRest(__VLS_13));
var __VLS_11;
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "feature-item" },
});
const __VLS_16 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({}));
const __VLS_18 = __VLS_17({}, ...__VLS_functionalComponentArgsRest(__VLS_17));
__VLS_19.slots.default;
const __VLS_20 = {}.Operation;
/** @type {[typeof __VLS_components.Operation, ]} */ ;
// @ts-ignore
const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({}));
const __VLS_22 = __VLS_21({}, ...__VLS_functionalComponentArgsRest(__VLS_21));
var __VLS_19;
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "login-right" },
});
const __VLS_24 = {}.ElCard;
/** @type {[typeof __VLS_components.ElCard, typeof __VLS_components.elCard, typeof __VLS_components.ElCard, typeof __VLS_components.elCard, ]} */ ;
// @ts-ignore
const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({
    ...{ class: "login-card" },
    shadow: "never",
}));
const __VLS_26 = __VLS_25({
    ...{ class: "login-card" },
    shadow: "never",
}, ...__VLS_functionalComponentArgsRest(__VLS_25));
__VLS_27.slots.default;
{
    const { header: __VLS_thisSlot } = __VLS_27.slots;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "card-header" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h2, __VLS_intrinsicElements.h2)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
        ...{ class: "welcome-text" },
    });
}
const __VLS_28 = {}.ElForm;
/** @type {[typeof __VLS_components.ElForm, typeof __VLS_components.elForm, typeof __VLS_components.ElForm, typeof __VLS_components.elForm, ]} */ ;
// @ts-ignore
const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
    ...{ 'onKeyup': {} },
    model: (__VLS_ctx.loginForm),
    rules: (__VLS_ctx.loginRules),
    ref: "loginFormRef",
    labelWidth: "0px",
    ...{ class: "login-form" },
}));
const __VLS_30 = __VLS_29({
    ...{ 'onKeyup': {} },
    model: (__VLS_ctx.loginForm),
    rules: (__VLS_ctx.loginRules),
    ref: "loginFormRef",
    labelWidth: "0px",
    ...{ class: "login-form" },
}, ...__VLS_functionalComponentArgsRest(__VLS_29));
let __VLS_32;
let __VLS_33;
let __VLS_34;
const __VLS_35 = {
    onKeyup: (__VLS_ctx.handleLogin)
};
/** @type {typeof __VLS_ctx.loginFormRef} */ ;
var __VLS_36 = {};
__VLS_31.slots.default;
const __VLS_38 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_39 = __VLS_asFunctionalComponent(__VLS_38, new __VLS_38({
    prop: "username",
}));
const __VLS_40 = __VLS_39({
    prop: "username",
}, ...__VLS_functionalComponentArgsRest(__VLS_39));
__VLS_41.slots.default;
const __VLS_42 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_43 = __VLS_asFunctionalComponent(__VLS_42, new __VLS_42({
    modelValue: (__VLS_ctx.loginForm.username),
    placeholder: "用户名",
    prefixIcon: (__VLS_ctx.User),
    size: "large",
    clearable: true,
}));
const __VLS_44 = __VLS_43({
    modelValue: (__VLS_ctx.loginForm.username),
    placeholder: "用户名",
    prefixIcon: (__VLS_ctx.User),
    size: "large",
    clearable: true,
}, ...__VLS_functionalComponentArgsRest(__VLS_43));
var __VLS_41;
const __VLS_46 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_47 = __VLS_asFunctionalComponent(__VLS_46, new __VLS_46({
    prop: "password",
}));
const __VLS_48 = __VLS_47({
    prop: "password",
}, ...__VLS_functionalComponentArgsRest(__VLS_47));
__VLS_49.slots.default;
const __VLS_50 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_51 = __VLS_asFunctionalComponent(__VLS_50, new __VLS_50({
    type: "password",
    modelValue: (__VLS_ctx.loginForm.password),
    placeholder: "密码",
    prefixIcon: (__VLS_ctx.Lock),
    showPassword: true,
    size: "large",
}));
const __VLS_52 = __VLS_51({
    type: "password",
    modelValue: (__VLS_ctx.loginForm.password),
    placeholder: "密码",
    prefixIcon: (__VLS_ctx.Lock),
    showPassword: true,
    size: "large",
}, ...__VLS_functionalComponentArgsRest(__VLS_51));
var __VLS_49;
const __VLS_54 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_55 = __VLS_asFunctionalComponent(__VLS_54, new __VLS_54({
    prop: "captcha",
}));
const __VLS_56 = __VLS_55({
    prop: "captcha",
}, ...__VLS_functionalComponentArgsRest(__VLS_55));
__VLS_57.slots.default;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "captcha-container" },
});
const __VLS_58 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_59 = __VLS_asFunctionalComponent(__VLS_58, new __VLS_58({
    modelValue: (__VLS_ctx.loginForm.captcha),
    placeholder: "验证码",
    prefixIcon: (__VLS_ctx.Key),
    size: "large",
}));
const __VLS_60 = __VLS_59({
    modelValue: (__VLS_ctx.loginForm.captcha),
    placeholder: "验证码",
    prefixIcon: (__VLS_ctx.Key),
    size: "large",
}, ...__VLS_functionalComponentArgsRest(__VLS_59));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ onClick: (__VLS_ctx.refreshCaptcha) },
    ...{ class: "captcha-image" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
    src: (__VLS_ctx.captchaUrl),
    alt: "验证码",
});
var __VLS_57;
const __VLS_62 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_63 = __VLS_asFunctionalComponent(__VLS_62, new __VLS_62({
    ...{ class: "remember-me" },
}));
const __VLS_64 = __VLS_63({
    ...{ class: "remember-me" },
}, ...__VLS_functionalComponentArgsRest(__VLS_63));
__VLS_65.slots.default;
const __VLS_66 = {}.ElCheckbox;
/** @type {[typeof __VLS_components.ElCheckbox, typeof __VLS_components.elCheckbox, typeof __VLS_components.ElCheckbox, typeof __VLS_components.elCheckbox, ]} */ ;
// @ts-ignore
const __VLS_67 = __VLS_asFunctionalComponent(__VLS_66, new __VLS_66({
    modelValue: (__VLS_ctx.loginForm.remember),
}));
const __VLS_68 = __VLS_67({
    modelValue: (__VLS_ctx.loginForm.remember),
}, ...__VLS_functionalComponentArgsRest(__VLS_67));
__VLS_69.slots.default;
var __VLS_69;
const __VLS_70 = {}.ElLink;
/** @type {[typeof __VLS_components.ElLink, typeof __VLS_components.elLink, typeof __VLS_components.ElLink, typeof __VLS_components.elLink, ]} */ ;
// @ts-ignore
const __VLS_71 = __VLS_asFunctionalComponent(__VLS_70, new __VLS_70({
    type: "primary",
    underline: (false),
}));
const __VLS_72 = __VLS_71({
    type: "primary",
    underline: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_71));
__VLS_73.slots.default;
var __VLS_73;
var __VLS_65;
const __VLS_74 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_75 = __VLS_asFunctionalComponent(__VLS_74, new __VLS_74({}));
const __VLS_76 = __VLS_75({}, ...__VLS_functionalComponentArgsRest(__VLS_75));
__VLS_77.slots.default;
const __VLS_78 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_79 = __VLS_asFunctionalComponent(__VLS_78, new __VLS_78({
    ...{ 'onClick': {} },
    type: "primary",
    ...{ class: "login-button" },
    loading: (__VLS_ctx.loading),
    size: "large",
}));
const __VLS_80 = __VLS_79({
    ...{ 'onClick': {} },
    type: "primary",
    ...{ class: "login-button" },
    loading: (__VLS_ctx.loading),
    size: "large",
}, ...__VLS_functionalComponentArgsRest(__VLS_79));
let __VLS_82;
let __VLS_83;
let __VLS_84;
const __VLS_85 = {
    onClick: (__VLS_ctx.handleLogin)
};
__VLS_81.slots.default;
var __VLS_81;
var __VLS_77;
var __VLS_31;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "login-footer" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
(new Date().getFullYear());
var __VLS_27;
/** @type {__VLS_StyleScopedClasses['login-container']} */ ;
/** @type {__VLS_StyleScopedClasses['login-box']} */ ;
/** @type {__VLS_StyleScopedClasses['login-left']} */ ;
/** @type {__VLS_StyleScopedClasses['system-info']} */ ;
/** @type {__VLS_StyleScopedClasses['system-title']} */ ;
/** @type {__VLS_StyleScopedClasses['system-desc']} */ ;
/** @type {__VLS_StyleScopedClasses['system-features']} */ ;
/** @type {__VLS_StyleScopedClasses['feature-item']} */ ;
/** @type {__VLS_StyleScopedClasses['feature-item']} */ ;
/** @type {__VLS_StyleScopedClasses['feature-item']} */ ;
/** @type {__VLS_StyleScopedClasses['login-right']} */ ;
/** @type {__VLS_StyleScopedClasses['login-card']} */ ;
/** @type {__VLS_StyleScopedClasses['card-header']} */ ;
/** @type {__VLS_StyleScopedClasses['welcome-text']} */ ;
/** @type {__VLS_StyleScopedClasses['login-form']} */ ;
/** @type {__VLS_StyleScopedClasses['captcha-container']} */ ;
/** @type {__VLS_StyleScopedClasses['captcha-image']} */ ;
/** @type {__VLS_StyleScopedClasses['remember-me']} */ ;
/** @type {__VLS_StyleScopedClasses['login-button']} */ ;
/** @type {__VLS_StyleScopedClasses['login-footer']} */ ;
// @ts-ignore
var __VLS_37 = __VLS_36;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            User: User,
            Lock: Lock,
            Key: Key,
            loginForm: loginForm,
            captchaUrl: captchaUrl,
            refreshCaptcha: refreshCaptcha,
            loginFormRef: loginFormRef,
            loading: loading,
            loginRules: loginRules,
            handleLogin: handleLogin,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
