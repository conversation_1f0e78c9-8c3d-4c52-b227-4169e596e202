package com.dz.ms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.entity.Menu;
import java.util.List;

/**
 * 菜单Service接口
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
public interface MenuService extends IService<Menu> {

    /**
     * 获取菜单树形结构
     * @return 菜单列表
     */
    List<Menu> getMenuTree();

    /**
     * 根据用户ID获取用户菜单列表
     * @param userId 用户ID
     * @return 用户菜单列表
     */
    List<Menu> getUserMenus(Long userId);
} 