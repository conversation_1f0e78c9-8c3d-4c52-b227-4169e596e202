package com.dz.ms.dynamic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dz.ms.dynamic.entity.client;
import com.dz.ms.dynamic.service.clientService;
import com.dz.ms.lowcode.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 客户控制器
 * 由低代码平台自动生成
 */
@RestController
@RequestMapping("/api/client")
@Tag(name = "客户管理", description = "客户相关接口")
public class clientController {

    @Autowired
    private clientService clientService;

    /**
     * 分页查询
     * @param page 页码
     * @param size 每页数量
     * @param keyword 关键字
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询客户")
    public Result<IPage<client>> page(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        IPage<client> result = clientService.page(page, size, keyword);
        return Result.success(result);
    }

    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 实体
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询客户")
    public Result<client> getById(@PathVariable Long id) {
        client entity = clientService.getById(id);
        return entity != null ? Result.success(entity) : Result.error("数据不存在");
    }

    /**
     * 新增
     * @param entity 实体
     * @return 是否成功
     */
    @PostMapping
    @Operation(summary = "新增客户")
    public Result<Boolean> save(@RequestBody client entity) {
        boolean result = clientService.save(entity);
        return Result.success(result);
    }

    /**
     * 修改
     * @param entity 实体
     * @return 是否成功
     */
    @PutMapping
    @Operation(summary = "修改客户")
    public Result<Boolean> update(@RequestBody client entity) {
        boolean result = clientService.updateById(entity);
        return Result.success(result);
    }

    /**
     * 删除
     * @param id 主键ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除客户")
    public Result<Boolean> delete(@PathVariable Long id) {
        boolean result = clientService.removeById(id);
        return Result.success(result);
    }
} 