import request from '../utils/request';
// 获取菜单树
export async function getMenuTree() {
    try {
        const response = await request({
            url: '/menu/tree',
            method: 'get',
        });
        return response;
    }
    catch (error) {
        console.error('获取菜单树失败:', error);
        throw error;
    }
}
// 添加菜单
export async function addMenu(data) {
    try {
        const response = await request({
            url: '/menu',
            method: 'post',
            data
        });
        return response;
    }
    catch (error) {
        console.error('添加菜单失败:', error);
        throw error;
    }
}
// 更新菜单
export async function updateMenu(id, data) {
    try {
        const response = await request({
            url: `/menu/${id}`,
            method: 'put',
            data
        });
        return response;
    }
    catch (error) {
        console.error('更新菜单失败:', error);
        throw error;
    }
}
// 删除菜单
export async function deleteMenu(id) {
    try {
        const response = await request({
            url: `/menu/${id}`,
            method: 'delete'
        });
        return response;
    }
    catch (error) {
        console.error('删除菜单失败:', error);
        throw error;
    }
}
// 获取菜单详情
export async function getMenuDetail(id) {
    try {
        const response = await request({
            url: `/menu/${id}`,
            method: 'get'
        });
        return response;
    }
    catch (error) {
        console.error('获取菜单详情失败:', error);
        throw error;
    }
}
