{"path": "d:\\code\\awx\\backend\\gateway\\target", "desc": "这是一个基于Spring Cloud Gateway的API网关服务，用于路由和管理对后端服务的请求。", "id": "4c5122b810a149e2addb31fd77dd8a51", "data": {"spaceKey": "4c5122b810a149e2addb31fd77dd8a51", "connections": {"webIDE": "https://4c5122b810a149e2addb31fd77dd8a51.ap-singapore.cloudstudio.club", "preview": "https://4c5122b810a149e2addb31fd77dd8a51--{port}.ap-singapore.cloudstudio.club", "api": "https://4c5122b810a149e2addb31fd77dd8a51--api.ap-singapore.cloudstudio.club", "pty": "https://4c5122b810a149e2addb31fd77dd8a51--pty.ap-singapore.cloudstudio.club"}}, "config": {"api": "https://4c5122b810a149e2addb31fd77dd8a51--api.ap-singapore.cloudstudio.club", "pty": "https://4c5122b810a149e2addb31fd77dd8a51--pty.ap-singapore.cloudstudio.club", "region": "ap-shanghai", "spaceKey": "4c5122b810a149e2addb31fd77dd8a51"}}