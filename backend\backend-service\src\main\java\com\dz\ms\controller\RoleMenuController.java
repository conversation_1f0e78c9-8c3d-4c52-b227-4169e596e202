package com.dz.ms.controller;

import com.dz.ms.common.Result;
import com.dz.ms.service.RoleMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色菜单关联管理
 * <AUTHOR>
 * @date 2025-05-29
 * @version 1.0.0
 */
@RestController
@RequestMapping("/role-menu")
@Api(tags = "角色菜单关联管理")
public class RoleMenuController {

    @Autowired
    private RoleMenuService roleMenuService;

    @ApiOperation("根据角色ID获取菜单ID列表")
    @GetMapping("/menuIds/{roleId}")
    public Result<List<Long>> getMenuIdsByRoleId(@PathVariable Long roleId) {
        List<Long> menuIds = roleMenuService.getMenuIdsByRoleId(roleId);
        return Result.success(menuIds);
    }

    @ApiOperation("保存角色菜单关联")
    @PostMapping("/save/{roleId}")
    public Result<Boolean> saveRoleMenus(@PathVariable Long roleId, @RequestBody List<Long> menuIds) {
        boolean success = roleMenuService.saveRoleMenus(roleId, menuIds);
        return Result.success(success);
    }
} 