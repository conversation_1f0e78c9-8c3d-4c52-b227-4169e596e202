export interface Member {
    id?: number;
    username: string;
    password?: string;
    nickname?: string;
    phone?: string;
    email?: string;
    status: number;
    createTime?: string;
    updateTime?: string;
}
export interface MemberPageQuery {
    pageNum?: number;
    pageSize?: number;
    username?: string;
}
export declare function getMemberPage(params: MemberPageQuery): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getAllMembers(): Promise<import("axios").AxiosResponse<any, any>>;
export declare function addMember(data: Member): Promise<import("axios").AxiosResponse<any, any>>;
export declare function updateMember(id: number, data: Member): Promise<import("axios").AxiosResponse<any, any>>;
export declare function deleteMember(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getMemberDetail(id: number): Promise<import("axios").AxiosResponse<any, any>>;
