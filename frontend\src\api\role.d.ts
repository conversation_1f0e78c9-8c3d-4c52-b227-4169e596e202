export interface Role {
    id?: number;
    name: string;
    description?: string;
    createTime?: string;
    updateTime?: string;
}
export interface RolePageParams {
    pageNum: number;
    pageSize: number;
    name?: string;
}
export declare function getRolePage(params: RolePageParams): Promise<import("axios").AxiosResponse<any, any>>;
export declare function addRole(data: Role): Promise<import("axios").AxiosResponse<any, any>>;
export declare function updateRole(id: number, data: Role): Promise<import("axios").AxiosResponse<any, any>>;
export declare function deleteRole(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getRoleDetail(id: number): Promise<import("axios").AxiosResponse<any, any>>;
