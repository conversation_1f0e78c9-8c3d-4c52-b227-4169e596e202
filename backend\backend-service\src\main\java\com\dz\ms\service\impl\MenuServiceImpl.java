package com.dz.ms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.ms.entity.Menu;
import com.dz.ms.mapper.MenuMapper;
import com.dz.ms.service.MenuService;
import com.dz.ms.service.RoleMenuService;
import com.dz.ms.service.UserRoleService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.Comparator;
import java.util.stream.Collectors;

/**
 * 菜单Service实现类
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
@Service
@Slf4j
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements MenuService {

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private RoleMenuService roleMenuService;

    @Override
    public List<Menu> getMenuTree() {
        // 获取所有菜单
        List<Menu> menuList = list();

        // 构建菜单树
        List<Menu> menuTree = menuList.stream()
                .filter(menu -> menu.getParentId() == null || menu.getParentId() == 0)
                .map(menu -> buildMenuTree(menu, menuList))
                .collect(Collectors.toList());

        // 对顶层菜单进行排序
        menuTree.sort(Comparator.comparing(Menu::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));

        return menuTree;
    }

    /**
     * 递归构建菜单树
     * @param parent 根节点
     * @param menuList 所有菜单列表
     * @return 带有子节点的菜单
     */
    private Menu buildMenuTree(Menu parent, List<Menu> menuList) {
        List<Menu> children = menuList.stream()
                .filter(menu -> parent.getId() != null && parent.getId().equals(menu.getParentId()))
                .map(menu -> buildMenuTree(menu, menuList))
                .collect(Collectors.toList());
        
        // 对子菜单进行排序
        children.sort(Comparator.comparing(Menu::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));
        
        parent.setChildren(children); // Set children to the parent menu
        return parent;
    }

    @Override
    public List<Menu> getUserMenus(Long userId) {
        // 1. 根据用户ID获取用户角色关联列表
        List<Long> roleIds = userRoleService.getUserRoleByUserId(userId).stream()
                .map(userRole -> userRole.getRoleId())
                .collect(Collectors.toList());

        if (roleIds.isEmpty()) {
            return new java.util.ArrayList<>(); // 用户没有角色，返回空菜单列表
        }

        // 2. 根据角色ID列表获取关联的菜单ID列表
        List<Long> menuIds = roleMenuService.getMenuIdsByRoleIds(roleIds);

        if (menuIds.isEmpty()) {
            return new java.util.ArrayList<>(); // 角色没有关联菜单，返回空菜单列表
        }

        // 3. 根据菜单ID列表查询具体的菜单实体
        // 使用Set去重，确保每个菜单只获取一次
        Set<Long> distinctMenuIds = new java.util.HashSet<>(menuIds);
        List<Menu> userMenuList = listByIds(distinctMenuIds); // 使用MyBatis-Plus的listByIds方法批量查询

        // 4. 获取所有菜单，包括用户没有权限的父级菜单
        List<Menu> allMenus = list();
        
        // 5. 创建一个包含用户有权限的菜单ID的集合，用于快速查找
        Set<Long> userMenuIdSet = userMenuList.stream()
                .map(Menu::getId)
                .collect(Collectors.toSet());
        
        // 6. 为每个用户有权限的菜单找到其所有父级菜单
        Set<Long> requiredMenuIds = new java.util.HashSet<>(userMenuIdSet);
        for (Menu userMenu : userMenuList) {
            // 递归查找并添加所有父级菜单ID
            findAndAddParentMenuIds(userMenu.getParentId(), allMenus, requiredMenuIds);
        }
        
        // 7. 获取用户有权限的菜单以及所有必要的父级菜单
        List<Menu> completeMenuList = allMenus.stream()
                .filter(menu -> requiredMenuIds.contains(menu.getId()))
                .collect(Collectors.toList());
        
        // 8. 构建菜单树
        List<Menu> userMenuTree = completeMenuList.stream()
                .filter(menu -> menu.getParentId() == null || menu.getParentId() == 0)
                .map(menu -> buildUserMenuTreeWithPermissions(menu, completeMenuList, userMenuIdSet))
                .collect(Collectors.toList());

        // 对顶层菜单进行排序
        userMenuTree.sort(Comparator.comparing(Menu::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));
        
        //log.info("返回给前端的菜单树已排序，顶层菜单数量: {}", userMenuTree.size());

        return userMenuTree;
    }

    /**
     * 递归查找并添加所有父级菜单ID
     * @param parentId 父级菜单ID
     * @param allMenus 所有菜单列表
     * @param requiredMenuIds 需要显示的菜单ID集合
     */
    private void findAndAddParentMenuIds(Long parentId, List<Menu> allMenus, Set<Long> requiredMenuIds) {
        if (parentId == null || parentId == 0 || requiredMenuIds.contains(parentId)) {
            return; // 已经是顶级菜单或者父级菜单已经在集合中
        }
        
        // 查找父级菜单
        for (Menu menu : allMenus) {
            if (menu.getId().equals(parentId)) {
                requiredMenuIds.add(parentId); // 添加父级菜单ID到集合
                findAndAddParentMenuIds(menu.getParentId(), allMenus, requiredMenuIds); // 递归查找更上层的父级菜单
                break;
            }
        }
    }

    /**
     * 递归构建用户菜单树，并标记哪些菜单是用户有权限的
     * @param parent 根节点
     * @param completeMenuList 完整的菜单列表(包括用户有权限的和必要的父级菜单)
     * @param userMenuIdSet 用户有权限的菜单ID集合
     * @return 带有子节点的菜单
     */
    private Menu buildUserMenuTreeWithPermissions(Menu parent, List<Menu> completeMenuList, Set<Long> userMenuIdSet) {
        List<Menu> children = completeMenuList.stream()
                .filter(menu -> parent.getId() != null && parent.getId().equals(menu.getParentId()))
                .map(menu -> buildUserMenuTreeWithPermissions(menu, completeMenuList, userMenuIdSet))
                .collect(Collectors.toList());
        
        // 对子菜单进行排序
        children.sort(Comparator.comparing(Menu::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));
        
        parent.setChildren(children); // 设置子菜单
        
        // 如果当前菜单不在用户有权限的菜单ID集合中，则设置permission为null
        // 这样前端可以根据permission是否为null来判断用户是否有权限访问该菜单
        if (!userMenuIdSet.contains(parent.getId())) {
            // 这里可以选择设置一个标志，表示这个菜单只是作为父级显示，用户没有直接权限
            // 例如：parent.setPermission(null);
        }
        
        return parent;
    }
} 