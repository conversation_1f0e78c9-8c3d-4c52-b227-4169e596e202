package com.dz.ms.lowcode.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dz.ms.lowcode.dto.MetaEntityDTO;
import com.dz.ms.lowcode.entity.MetaEntity;
import com.dz.ms.lowcode.service.MetaEntityService;
import com.dz.ms.lowcode.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import com.dz.ms.lowcode.dto.MetaFieldDTO;
import com.dz.ms.lowcode.service.generator.CodeGeneratorService;
import org.springframework.jdbc.core.JdbcTemplate;
import java.util.List;
import com.dz.ms.lowcode.dto.GenerateAlterSqlRequest;
import com.dz.ms.lowcode.dto.ExecuteSqlRequest;

import javax.validation.Valid;

/**
 * 元数据实体控制器
 * <AUTHOR>
 * @date 2023-07-01
 */
@RestController
@RequestMapping("/lowcode/meta/entity")
@Tag(name = "元数据实体管理", description = "元数据实体相关接口")
public class MetaEntityController {

    @Autowired
    private MetaEntityService metaEntityService;

    @Autowired
    private CodeGeneratorService codeGeneratorService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 分页查询元数据实体
     * @param page 页码
     * @param size 每页数量
     * @param keyword 关键字
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询元数据实体")
    @PreAuthorize("hasAuthority('lowcode:entity')")
    public Result<IPage<MetaEntity>> page(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        IPage<MetaEntity> result = metaEntityService.page(page, size, keyword);
        return Result.success(result);
    }

    /**
     * 根据ID查询元数据实体
     * @param id 元数据实体ID
     * @return 元数据实体
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询元数据实体")
    public Result<MetaEntityDTO> getById(@PathVariable Long id) {
        MetaEntityDTO entity = metaEntityService.getEntityById(id);
        return entity != null ? Result.success(entity) : Result.error("数据不存在");
    }

    /**
     * 保存元数据实体
     * @param entityDTO 元数据实体DTO
     * @return 实体ID
     */
    @PostMapping
    @Operation(summary = "保存元数据实体")
    public Result<Long> save(@Valid @RequestBody MetaEntityDTO entityDTO) {
        try {
            Long id = metaEntityService.saveEntity(entityDTO);
            return Result.success(id);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新元数据实体
     * @param entityDTO 元数据实体DTO
     * @return 是否成功
     */
    @PutMapping
    @Operation(summary = "更新元数据实体")
    public Result<Boolean> update(@Valid @RequestBody MetaEntityDTO entityDTO) {
        try {
            boolean result = metaEntityService.updateEntity(entityDTO);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除元数据实体
     * @param id 元数据实体ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除元数据实体")
    public Result<Boolean> delete(@PathVariable Long id) {
        try {
            boolean result = metaEntityService.deleteEntity(id);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 发布元数据实体
     * @param id 元数据实体ID
     * @return 是否成功
     */
    @PostMapping("/{id}/publish")
    @Operation(summary = "发布元数据实体")
    public Result<Boolean> publish(@PathVariable Long id) {
        try {
            boolean result = metaEntityService.publishEntity(id);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 取消发布元数据实体
     * @param id 元数据实体ID
     * @return 是否成功
     */
    @PostMapping("/{id}/unpublish")
    @Operation(summary = "取消发布元数据实体")
    public Result<Boolean> unpublish(@PathVariable Long id) {
        try {
            boolean result = metaEntityService.unpublishEntity(id);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 生成表结构变更SQL
     */
    @PostMapping("/generate-alter-sql")
    @Operation(summary = "生成表结构变更SQL")
    public Result<String> generateAlterSql(@RequestBody GenerateAlterSqlRequest req) {
        String sql = codeGeneratorService.generateAlterTableSql(req.getEntityId(), req.getFields());
        return Result.success(sql);
    }

    /**
     * 执行SQL
     */
    @PostMapping("/execute-sql")
    @Operation(summary = "执行SQL")
    public Result<Boolean> executeSql(@RequestBody ExecuteSqlRequest req) {
        try {
            jdbcTemplate.execute(req.getSql());
            metaEntityService.updateEntityFields(req.getEntityId(), req.getFields());
            return Result.success(true);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
} 