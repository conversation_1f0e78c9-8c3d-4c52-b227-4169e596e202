<template>
  <div class="page-container">
    <el-card>
      <div style="margin-bottom: 16px; display: flex; gap: 8px; align-items: center;">
        <el-input v-model="searchForm.username" placeholder="搜索用户名" style="width: 200px" clearable />
        <el-button type="primary" @click="fetchData">搜索</el-button>
        <el-button type="success" @click="openAdd">新增用户</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="nickname" label="昵称" />
        <el-table-column prop="phone" label="手机号" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="openEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="pageNum"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchData"
        @current-change="fetchData"
        style="margin-top: 16px; text-align: right;"
      />
    </el-card>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="400px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!form.id">
          <el-input v-model="form.password" type="password" autocomplete="off" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status">
            <el-option :value="1" label="启用" />
            <el-option :value="0" label="禁用" />
          </el-select>
        </el-form-item>
        <el-form-item label="分配角色" prop="selectedRoleIds" v-if="allRoles.length > 0">
          <el-select
            v-model="selectedRoleIds"
            multiple
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="role in allRoles"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage,ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus';
import { getUserPage, addUser, updateUser, deleteUser } from '../../api/user';
import type { User } from '../../api/user';;
import { getRolePage } from '../../api/role';
import type { Role } from '../../api/role';;
import { getRoleIdsByUserId, saveUserRoles } from '../../api/user-role';

const searchForm = reactive({ username: '' });
const tableData = ref<User[]>([]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);

const dialogVisible = ref(false);
const dialogTitle = ref('');
const form = reactive<User>({ username: '', password: '', nickname: '', phone: '', email: '', status: 1 });
const formRef = ref<FormInstance>();

const allRoles = ref<Role[]>([]);
const selectedRoleIds = ref<number[]>([]);

const rules: FormRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
};

function fetchData() {
  getUserPage({ pageNum: pageNum.value, pageSize: pageSize.value, username: searchForm.username }).then(res => {
    const { records, total: t } = res.data;
    tableData.value = records;
    total.value = t;
  }).catch(error => {
    console.error("获取用户列表失败:", error);
    tableData.value = [];
    total.value = 0;
  });
}

function openAdd() {
  dialogTitle.value = '新增用户';
  Object.assign(form, { id: undefined, username: '', password: '', nickname: '', phone: '', email: '', status: 1 });
  selectedRoleIds.value = [];
  dialogVisible.value = true;
  fetchAllRoles();
}

async function openEdit(row: User) {
  dialogTitle.value = '编辑用户';
  Object.assign(form, row);

  if (row.id) {
    try {
      const res = await getRoleIdsByUserId(row.id);
      if (res && res.code === 0 && res.data) {
        nextTick(() => {
          selectedRoleIds.value = res.data;
        });
      } else {
        selectedRoleIds.value = [];
        ElMessage.error(res.msg || '获取用户角色失败');
      }
    } catch (error) {
      console.error("获取用户角色失败:", error);
      selectedRoleIds.value = [];
      ElMessage.error('获取用户角色失败');
    }
  } else {
    selectedRoleIds.value = [];
  }

  dialogVisible.value = true;
  fetchAllRoles();
}

async function handleSubmit() {
  formRef.value?.validate(async (valid) => {
    if (!valid) return;
    try {
      let saveSuccess = false;
      let newUserId: number | undefined;

      const dataToSave: any = { ...form }; // Copy form data

      // Remove password if it's empty string, unless adding a new user
      if (form.id && dataToSave.password === '') {
           delete dataToSave.password;
      }

      if (!form.id) {
        // 新增用户
        const res = await addUser(dataToSave);
        if (res && res.code === 0 && res.data) {
          newUserId = res.data; // 获取新增用户的ID
          ElMessage.success('新增用户成功');
          saveSuccess = true;
          
          // 保存用户角色关联
          if (newUserId && selectedRoleIds.value.length > 0) {
            try {
              await saveUserRoles(newUserId, selectedRoleIds.value);
              console.log('用户角色保存成功');
            } catch (error) {
              console.error('保存用户角色失败:', error);
              ElMessage.warning('用户创建成功，但角色分配失败');
            }
          }
        } else {
          ElMessage.error(res?.msg || '新增用户失败');
        }
      } else {
        // 编辑用户
        const res = await updateUser(form.id, dataToSave);
        if (res && res.code === 0) {
          ElMessage.success('编辑用户成功');
          saveSuccess = true;
          
          // 保存用户角色关联
          try {
            await saveUserRoles(form.id, selectedRoleIds.value);
            console.log('用户角色保存成功');
          } catch (error) {
            console.error('保存用户角色失败:', error);
            ElMessage.warning('用户更新成功，但角色分配失败');
          }
        } else {
          ElMessage.error(res?.msg || '编辑用户失败');
        }
      }

      if (saveSuccess) {
        dialogVisible.value = false;
        fetchData();
      }
    } catch (error) {
      console.error("提交用户信息失败:", error);
      ElMessage.error('操作失败');
    }
  });
}

async function handleDelete(id?: number) {
  if (!id) return;
  
  ElMessageBox.confirm(
    '确定要删除该用户吗？此操作不可恢复',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const res = await deleteUser(id);
        if (res && res.code === 0) {
          ElMessage.success('删除成功');
          fetchData();
        } else {
          ElMessage.error(res?.msg || '删除失败');
        }
      } catch (error) {
        console.error("删除用户失败:", error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 用户取消删除操作
      ElMessage.info('已取消删除');
    });
}

onMounted(() => {
  fetchData();
  fetchAllRoles();
});

async function fetchAllRoles() {
  try {
    const res = await getRolePage({ pageNum: 1, pageSize: 1000 });
    if (res && res.code === 0 && res.data && res.data.records) {
      allRoles.value = [...res.data.records];
      console.log('All roles:', allRoles.value);
    } else {
      allRoles.value = [];
      ElMessage.error(res.data.msg || '获取角色列表失败');
    }
  } catch (error) {
    console.error("获取所有角色失败:", error);
    allRoles.value = [];
    ElMessage.error('获取所有角色失败');
  }
}
</script>

