import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 查找Vue文件
console.log('查找Vue文件...');
const findVueFiles = () => {
  try {
    // 使用 dir 命令查找所有 .vue 文件
    const cmd = 'cd ' + __dirname + ' && dir /s /b *.vue';
    const stdout = execSync(cmd, { encoding: 'utf8' });
    return stdout.split('\r\n').filter(Boolean);
  } catch (error) {
    console.error('查找文件时出错:', error);
    return [];
  }
};

// 修复文件中的导入语句
const fixFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否包含脚本部分
    if (!content.includes('<script')) return false;
    
    let modified = false;
    let newContent = content;
    
    // 修复多余的逗号
    const commaRegex = /import\s*{([^}]*,\s*,\s*[^}]*)}\s*from\s*['"][^'"]+['"]/g;
    if (commaRegex.test(newContent)) {
      newContent = newContent.replace(/,\s*,\s*/g, ',');
      modified = true;
    }
    
    // 修复末尾多余的分号
    const semicolonRegex = /;\s*;/g;
    if (semicolonRegex.test(newContent)) {
      newContent = newContent.replace(/;\s*;/g, ';');
      modified = true;
    }
    
    // 修复 Element Plus 类型导入
    const elementPlusImportRegex = /import\s*{([^}]*)}\s*from\s*['"]element-plus['"]/g;
    const elementPlusTypeImportRegex = /import\s*type\s*{([^}]*)}\s*from\s*['"]element-plus['"]/g;
    
    let match;
    while ((match = elementPlusImportRegex.exec(content)) !== null) {
      const importStatement = match[0];
      const importContent = match[1];
      
      // 检查是否包含 FormInstance 或 FormRules
      if (importContent.includes('FormInstance') || importContent.includes('FormRules')) {
        // 提取非类型导入
        const nonTypeImports = importContent
          .split(',')
          .map(item => item.trim())
          .filter(item => item && item !== 'FormInstance' && item !== 'FormRules');
        
        // 提取类型导入
        const typeImports = importContent
          .split(',')
          .map(item => item.trim())
          .filter(item => item && (item === 'FormInstance' || item === 'FormRules'));
        
        if (typeImports.length > 0) {
          let newImportStatement = '';
          
          // 如果有非类型导入，保留原导入语句但移除类型
          if (nonTypeImports.length > 0) {
            newImportStatement += `import { ${nonTypeImports.join(', ')} } from 'element-plus'\n`;
          }
          
          // 添加类型导入语句
          newImportStatement += `import type { ${typeImports.join(', ')} } from 'element-plus'`;
          
          // 替换原导入语句
          newContent = newContent.replace(importStatement, newImportStatement);
          modified = true;
        }
      }
    }
    
    // 如果文件被修改，则写回
    if (modified) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`已修复文件: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`处理文件 ${filePath} 时出错:`, error);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('开始检查和修复导入语句问题...');
  const vueFiles = findVueFiles();
  console.log(`找到 ${vueFiles.length} 个Vue文件`);
  
  let fixedCount = 0;
  for (const file of vueFiles) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`检查完成，共修复了 ${fixedCount} 个文件`);
};

main(); 