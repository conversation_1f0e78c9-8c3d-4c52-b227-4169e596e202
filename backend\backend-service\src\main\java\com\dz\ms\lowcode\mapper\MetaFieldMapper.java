package com.dz.ms.lowcode.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.lowcode.entity.MetaField;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 元数据字段Mapper接口
 * <AUTHOR>
 * @date 2023-07-01
 */
@Mapper
public interface MetaFieldMapper extends BaseMapper<MetaField> {
    
    /**
     * 根据实体ID查询字段列表
     * @param entityId 实体ID
     * @return 字段列表
     */
    List<MetaField> selectByEntityId(@Param("entityId") Long entityId);
} 