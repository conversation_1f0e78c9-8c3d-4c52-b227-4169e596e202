declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    entityData: {
        type: ObjectConstructor;
        default: () => null;
    };
    formType: {
        type: StringConstructor;
        default: string;
    };
}>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    cancel: (...args: any[]) => void;
    success: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    entityData: {
        type: ObjectConstructor;
        default: () => null;
    };
    formType: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{
    onCancel?: ((...args: any[]) => any) | undefined;
    onSuccess?: ((...args: any[]) => any) | undefined;
}>, {
    entityData: Record<string, any>;
    formType: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
