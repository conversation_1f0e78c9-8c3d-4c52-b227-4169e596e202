export interface Wish {
    id?: number;
    memberId?: number;
    memberUsername?: string;
    memberNickname?: string;
    content: string;
    status: number;
    createTime?: string;
    updateTime?: string;
}
export interface WishPageQuery {
    pageNum?: number;
    pageSize?: number;
    memberId?: number;
    keyword?: string;
    status?: number;
}
export declare function getWishPage(params: WishPageQuery): Promise<import("axios").AxiosResponse<any, any>>;
export declare function addWish(data: Wish): Promise<import("axios").AxiosResponse<any, any>>;
export declare function updateWish(id: number, data: Wish): Promise<import("axios").AxiosResponse<any, any>>;
export declare function deleteWish(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getWishDetail(id: number): Promise<import("axios").AxiosResponse<any, any>>;
