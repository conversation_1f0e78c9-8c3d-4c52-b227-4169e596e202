import request from '../utils/request';

export interface Role {
  id?: number;
  name: string;
  description?: string;
  createTime?: string;
  updateTime?: string;
}

export interface RolePageParams {
  pageNum: number;
  pageSize: number;
  name?: string;
}

export function getRolePage(params: RolePageParams) {
  return request({
    url: '/role/page',
    method: 'get',
    params
  });
}

export function addRole(data: Role) {
  return request({
    url: '/role',
    method: 'post',
    data
  });
}

export function updateRole(id: number, data: Role) {
  return request({
    url: `/role/${id}`,
    method: 'put',
    data
  });
}

export function deleteRole(id: number) {
  return request({
    url: `/role/${id}`,
    method: 'delete'
  });
}

export function getRoleDetail(id: number) {
  return request({
    url: `/role/${id}`,
    method: 'get'
  });
}
 