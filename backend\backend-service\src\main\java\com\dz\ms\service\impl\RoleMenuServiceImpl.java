package com.dz.ms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dz.ms.entity.RoleMenu;
import com.dz.ms.mapper.RoleMenuMapper;
import com.dz.ms.service.RoleMenuService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色-菜单关联Service实现类
 * <AUTHOR>
 * @date 2025-05-29
 * @version 1.0.0
 */
@Service
public class RoleMenuServiceImpl extends ServiceImpl<RoleMenuMapper, RoleMenu> implements RoleMenuService {

    @Override
    public List<Long> getMenuIdsByRoleId(Long roleId) {
        LambdaQueryWrapper<RoleMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RoleMenu::getRoleId, roleId);
        return list(queryWrapper).stream()
                .map(RoleMenu::getMenuId)
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getMenuIdsByRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return new java.util.ArrayList<>();
        }
        //System.out.println("====roleIds:"+roleIds);
        return baseMapper.selectMenuIdsByRoleIds(roleIds);
    }

    @Override
    @Transactional
    public boolean saveRoleMenus(Long roleId, List<Long> menuIds) {
        // First, delete existing role-menu associations for this role
        LambdaUpdateWrapper<RoleMenu> deleteWrapper = new LambdaUpdateWrapper<>();
        deleteWrapper.eq(RoleMenu::getRoleId, roleId);
        remove(deleteWrapper);

        // Then, insert new associations
        if (menuIds != null && !menuIds.isEmpty()) {
            List<RoleMenu> roleMenusToSave = menuIds.stream()
                    .map(menuId -> {
                        RoleMenu roleMenu = new RoleMenu();
                        roleMenu.setRoleId(roleId);
                        roleMenu.setMenuId(menuId);
                        return roleMenu;
                    })
                    .collect(Collectors.toList());
            return saveBatch(roleMenusToSave);
        }

        return true; // Return true if no menus to save (effectively deleted all)
    }
} 