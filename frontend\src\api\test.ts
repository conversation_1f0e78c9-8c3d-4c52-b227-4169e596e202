import request from '../utils/request';

// 实体接口定义
export interface test {
  id?: number;
  createTime?: string;
  updateTime?: string;
}

// 分页查询参数接口
export interface testPageQuery {
  pageNum?: number;
  pageSize?: number;
}

// 获取分页列表
export function gettestList(params: testPageQuery) {
  if (!params.pageNum) params.pageNum = 1;
  if (!params.pageSize) params.pageSize = 10;
  return request.get('/api/test/page', { params });
}

// 获取所有数据
export function getAlltests() {
  return request.get('/api/test/list');
}

// 获取详情
export function gettestDetail(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.get(`/api/test/${id}`);
}

// 新增
export function savetest(data: test) {
  return request.post('/api/test', data);
}

// 更新
export function updatetest(id: number, data: test) {
  if (!id) return Promise.reject('ID不能为空');
  return request.put(`/api/test/${id}`, data);
}

// 删除
export function deletetest(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.delete(`/api/test/${id}`);
}

// 批量删除
export function batchDeletetest(ids: number[]) {
  if (!ids || ids.length === 0) return Promise.reject('ID列表不能为空');
  return request.delete('/api/test/batch', { data: { ids } });
}

// 导出数据
export function exporttest(params?: testPageQuery) {
  return request.get('/api/test/export', { 
    params,
    responseType: 'blob'
  });
}