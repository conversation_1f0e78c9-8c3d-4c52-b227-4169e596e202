# Docker部署前端API代理问题修复指南

## 问题描述

前端项目打包为dist部署到Docker中后，前端页面请求无法转发到后端，出现404错误：
```
2025/07/14 10:31:30 [error] 30#30: *1 open() "/usr/share/nginx/html/api/auth/captcha" failed (2: No such file or directory)
```

## 问题原因

1. **Vite代理配置失效**：`vite.config.ts`中的`server.proxy`配置只在开发环境有效，生产环境部署时无效
2. **nginx缺少API代理配置**：Docker中的nginx没有配置反向代理来转发API请求
3. **CORS配置不匹配**：后端CORS配置只允许开发环境的地址

## 解决方案

### 1. 创建nginx配置文件

已创建 `frontend/nginx.conf` 文件，配置了：
- 静态资源服务
- API代理转发到网关服务 (**********:9090)
- SPA路由支持
- 缓存策略

### 2. 更新Dockerfile

已更新 `frontend/Dockerfile`：
- 复制自定义nginx配置
- 修正端口映射为80

### 3. 更新CORS配置

已更新 `backend/backend-service/src/main/java/com/dz/ms/config/CorsConfig.java`：
- 添加Docker部署环境的允许来源

### 4. 创建网关路由配置

已创建 `nacos-configs/rtm-gateway.yaml`，配置了：
- `/api/**` 路径的路由规则
- 去掉`/api`前缀的过滤器
- Sentinel集成配置

## 部署步骤

### 第一步：配置Nacos

1. 登录Nacos控制台：http://**********:8848/nacos
2. 创建配置：
   - **Data ID**: `rtm-gateway.yaml`
   - **Group**: `DEFAULT_GROUP`
   - **配置格式**: YAML
   - **配置内容**: 复制 `nacos-configs/rtm-gateway.yaml` 的内容

### 第二步：重新构建后端服务

```bash
# 重新构建后端服务（包含更新的CORS配置）
cd backend/backend-service
mvn clean package -DskipTests

# 复制jar包到deploy目录
cp target/backend-service-1.0.0.jar deploy/

# 构建Docker镜像
cd deploy
docker buildx build -t image-rtm-backend .

# 重启后端服务容器
docker stop cont-rtm-backend
docker rm cont-rtm-backend
docker run -d --name cont-rtm-backend -p 9091:9091 image-rtm-backend
```

### 第三步：重新构建网关服务

```bash
# 重新构建网关服务
cd backend/gateway
mvn clean package -DskipTests

# 复制jar包到deploy目录
cp target/gateway-1.0.0.jar deploy/

# 构建Docker镜像
cd deploy
docker buildx build -t image-rtm-gateway .

# 重启网关服务容器
docker stop cont-rtm-gateway
docker rm cont-rtm-gateway
docker run -d --name cont-rtm-gateway -p 9090:9090 image-rtm-gateway
```

### 第四步：重新部署前端

```bash
cd frontend

# 使用提供的部署脚本
deploy.bat

# 或者手动执行：
# npm run build
# docker build -t rtm-web .
# docker stop rtm-web && docker rm rtm-web
# docker run -d -p 80:80 --name rtm-web rtm-web
```

## 验证部署

1. **检查容器状态**：
   ```bash
   docker ps | grep rtm
   ```

2. **检查前端访问**：
   - 访问：http://localhost
   - 应该能看到登录页面

3. **检查API转发**：
   - 打开浏览器开发者工具
   - 尝试获取验证码
   - 检查网络请求是否成功

4. **检查容器日志**：
   ```bash
   # 前端nginx日志
   docker logs rtm-web
   
   # 网关服务日志
   docker logs cont-rtm-gateway
   
   # 后端服务日志
   docker logs cont-rtm-backend
   ```

## 故障排除

### 如果API请求仍然404

1. 检查网关服务是否正常启动
2. 检查Nacos中的网关配置是否正确
3. 检查网关服务是否能连接到Nacos

### 如果出现CORS错误

1. 检查后端服务的CORS配置是否包含正确的前端地址
2. 检查请求头是否正确设置

### 如果网关路由不工作

1. 检查Nacos中的`rtm-gateway.yaml`配置
2. 检查后端服务是否在Nacos中正确注册
3. 查看网关服务日志中的路由匹配信息

## 关键配置说明

- **nginx代理**：`/api/` → `http://**********:9090`
- **网关路由**：`/api/**` → `lb://rtm-backend` (去掉/api前缀)
- **后端接口**：`/auth/captcha` (不包含/api前缀)
- **完整请求链路**：前端 → nginx → 网关 → 后端服务
