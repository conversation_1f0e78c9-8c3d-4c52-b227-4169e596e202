package com.dz.ms.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 心愿单VO
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@Data
public class WishVO implements Serializable {
    /** 主键ID */
    private Long id;

    /** 会员ID */
    private Long memberId;
    
    /** 会员用户名 */
    private String memberUsername;
    
    /** 会员昵称 */
    private String memberNickname;

    /** 心愿内容 */
    private String content;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 状态（0-无效 1-有效） */
    private Integer status;
} 