<template>
  <div class="page-container">
    <el-card>
      <div style="margin-bottom: 16px; display: flex; gap: 8px; align-items: center;">
        <el-input v-model="searchForm.name" placeholder="搜索角色名称" style="width: 200px" clearable />
        <el-button type="primary" @click="fetchData">搜索</el-button>
        <el-button type="success" @click="openAdd">新增角色</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="name" label="角色名称" />
        <el-table-column prop="description" label="角色描述" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="updateTime" label="更新时间" />
        <el-table-column label="操作" width="280">
          <template #default="scope">
            <el-button size="small" @click="openEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
            <el-button size="small" @click="handleAssignMenus(scope.row)">分配权限</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="pageNum"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchData"
        @current-change="fetchData"
        style="margin-top: 16px; text-align: right;"
      />
    </el-card>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="400px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name" autocomplete="off" />
        </el-form-item>
        <el-form-item label="角色编码" prop="code">
          <el-input v-model="form.code" autocomplete="off" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input v-model="form.description" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
    <!-- Assign Menu Dialog -->
    <el-dialog 
      v-model="assignMenuDialogVisible" 
      title="分配权限" 
      width="400px"
      @open="handleAssignMenuDialogOpen"
      @close="handleAssignMenuDialogClose">
      <!-- Menu Tree Component -->
      <el-tree
        ref="menuTreeRef"
        :data="menuTree"
        show-checkbox
        node-key="id"
        :default-checked-keys="selectedMenuIds"
        :props="{ children: 'children', label: 'name' }"
      />
      <template #footer>
        <el-button @click="assignMenuDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAssignedMenus">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus';
import {
  getRolePage,
  addRole,
  updateRole,
  deleteRole,
} from '../../api/role';
import type { Role, RolePageParams } from '../../api/role';
import { getRoleMenuIds, saveRoleMenus } from '../../api/role-menu';
import { getMenuTree } from '../../api/menu';

const searchForm = reactive({ name: '' });
const tableData = ref<Role[]>([]); // Replace 'any' with Role type later
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);

const dialogVisible = ref(false);
const dialogTitle = ref('');
const form = reactive<Role>({ name: '', code: '', description: '' }); // Replace 'any' with Role type later
const formRef = ref<FormInstance>();

// New state variables for menu assignment
const assignMenuDialogVisible = ref(false);
const currentRole = ref<Role | null>(null);
const menuTree = ref([]); // To store the menu tree data
const selectedMenuIds = ref<number[]>([]); // To store selected menu IDs
const menuTreeRef = ref(null); // Ref for the ElTree component

const rules: FormRules = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入角色编码', trigger: 'blur' }],
};

// Function to fetch role data
async function fetchData() {
  try {
    const params: RolePageParams = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      name: searchForm.name,
    };
    const res = await getRolePage(params);
    const { records, total: t } = res.data; // Assuming res.data has records and total
    tableData.value = records;
    total.value = t;
  } catch (error) {
    console.error("获取角色列表失败:", error);
    tableData.value = [];
    total.value = 0;
    ElMessage.error('获取角色列表失败');
  }
}

function openAdd() {
  dialogTitle.value = '新增角色';
  Object.assign(form, { id: undefined, name: '', code: '', description: '' });
  dialogVisible.value = true;
}

function openEdit(row: Role) { // Replace 'any' with Role type later
  dialogTitle.value = '编辑角色';
  Object.assign(form, row);
  dialogVisible.value = true;
}

async function handleSubmit() {
  formRef.value?.validate(async (valid) => {
    if (!valid) return;
    try {
      if (!form.id) {
        // Add role
        await addRole(form);
        ElMessage.success('新增成功');
      } else {
        // Update role
        await updateRole(form.id, form);
        ElMessage.success('编辑成功');
      }
      dialogVisible.value = false;
      fetchData();
    } catch (error) {
      console.error("提交角色信息失败:", error);
      ElMessage.error('操作失败');
    }
  });
}

async function handleDelete(id?: number) {
  if (!id) return;
  
  ElMessageBox.confirm(
    '确定要删除该角色吗？此操作不可恢复',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const res = await deleteRole(id);
        if (res && res.code === 0) {
          ElMessage.success('删除成功');
          fetchData();
        } else {
          ElMessage.error(res?.msg || '删除失败');
        }
      } catch (error) {
        console.error("删除角色失败:", error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 用户取消删除操作
      ElMessage.info('已取消删除');
    });
}

// New methods to handle dialog open and close events
function handleAssignMenuDialogOpen() {
  // 弹窗打开时，加载数据
  if (currentRole.value) {
    loadRoleMenuData(currentRole.value);
  }
}

function handleAssignMenuDialogClose() {
  // 弹窗关闭时，重置树形控件状态
  if (menuTreeRef.value) {
    // 清空选中状态
    menuTreeRef.value.setCheckedKeys([]);
  }
  // 清空选中的菜单ID
  selectedMenuIds.value = [];
}

// 将原来handleAssignMenus方法中的数据加载逻辑抽取为单独的方法
async function loadRoleMenuData(role: Role) {
  try {
    // 获取菜单树
    const menuTreeRes = await getMenuTree();
    console.log('menuTreeRes', menuTreeRes);
    if (menuTreeRes && menuTreeRes.code === 0 && menuTreeRes.data) {
      menuTree.value = menuTreeRes.data;
    } else {
      ElMessage.error(menuTreeRes.msg || '获取菜单树失败');
      menuTree.value = [];
    }

    // 获取角色已分配的菜单ID
    if (role.id) {
      const assignedMenuRes = await getRoleMenuIds(role.id);
      console.log('assignedMenuRes', assignedMenuRes);
      if (assignedMenuRes && assignedMenuRes.code === 0 && assignedMenuRes.data) {
        // 确保树形控件已经渲染完成后再设置选中状态
        setTimeout(() => {
          selectedMenuIds.value = assignedMenuRes.data;
          if (menuTreeRef.value) {
            menuTreeRef.value.setCheckedKeys(assignedMenuRes.data);
          }
        }, 100);
      } else {
        ElMessage.error(assignedMenuRes.msg || '获取已分配菜单失败');
        selectedMenuIds.value = [];
      }
    } else {
      selectedMenuIds.value = [];
    }
  } catch (error) {
    console.error("获取菜单数据失败:", error);
    ElMessage.error('获取菜单数据失败');
    menuTree.value = [];
    selectedMenuIds.value = [];
  }
}

// 修改handleAssignMenus方法
async function handleAssignMenus(row: Role) {
  currentRole.value = row;
  assignMenuDialogVisible.value = true;
  // 数据加载逻辑已移至handleAssignMenuDialogOpen方法中
}

// New method to save assigned menus
async function saveAssignedMenus() {
  if (!currentRole.value || !currentRole.value.id) {
    ElMessage.warning('请选择要分配菜单的角色');
    return;
  }

  // Get checked menu keys from the tree (including half-checked nodes if needed)
  const checkedKeys = menuTreeRef.value.getCheckedKeys(); // Gets fully checked nodes
  // If you need half-checked nodes as well:
  // const halfCheckedKeys = menuTreeRef.value.getHalfCheckedKeys();
  // const menuIdsToSave = [...checkedKeys, ...halfCheckedKeys];
  const menuIdsToSave = checkedKeys; // Using only fully checked nodes for simplicity

  try {
    const res = await saveRoleMenus(currentRole.value.id, menuIdsToSave);
    if (res && res.code === 0) {
      ElMessage.success('分配菜单成功');
      assignMenuDialogVisible.value = false;
      // Optionally refresh the role list if needed
      // fetchData();
    } else {
      ElMessage.error(res.msg || '分配菜单失败');
    }
  } catch (error) {
    console.error("保存分配菜单失败:", error);
    ElMessage.error('保存分配菜单失败');
  }
}

onMounted(fetchData);
</script>

<style scoped>
.role-list {
  padding: 24px;
}

.role-list .el-dialog__body {
    padding-top: 0;
    padding-bottom: 10px;
}
</style> 