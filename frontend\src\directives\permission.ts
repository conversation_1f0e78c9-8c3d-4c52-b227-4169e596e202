import type { Directive } from 'vue';
import { usePermissionStore } from '../store/permission';

export const permission: Directive = {
  mounted(el, binding) {
    const permissionStore = usePermissionStore();
    const { value } = binding;
    
    if (value) {
      const hasPermission = permissionStore.hasPermission(value);
      if (!hasPermission) {
        el.parentNode?.removeChild(el);
      }
    }
  }
}; 