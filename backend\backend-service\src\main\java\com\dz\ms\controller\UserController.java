package com.dz.ms.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.dz.ms.dto.UserDTO;
import com.dz.ms.dto.PasswordUpdateDTO;
import com.dz.ms.entity.User;
import com.dz.ms.service.UserService;
import com.dz.ms.vo.UserVO;
import com.dz.ms.common.Result;
import com.dz.ms.service.MenuService;
import com.dz.ms.entity.Menu;
import com.dz.ms.entity.Role;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.groups.Default;
import java.util.List;

/**
 * 用户管理
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@RestController
@RequestMapping("/user")
@Api(tags = "用户管理")
@Validated
public class UserController {
    @Autowired
    private UserService userService;

    @Autowired
    private MenuService menuService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @ApiOperation("分页查询用户")
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('user:view')")
    public Result<IPage<UserVO>> page(@RequestParam(defaultValue = "1") long pageNum,
                                      @RequestParam(defaultValue = "10") long pageSize,
                                      @RequestParam(required = false) String username) {
        Page<User> page = new Page<>(pageNum, pageSize);
        IPage<User> userPage = userService.lambdaQuery()
                .like(username != null && !username.isEmpty(), User::getUsername, username)
                .orderByDesc(User::getCreateTime)
                .page(page);
        IPage<UserVO> voPage = userPage.convert(user -> {
            UserVO vo = new UserVO();
            BeanUtils.copyProperties(user, vo);
            return vo;
        });
        return Result.success(voPage);
    }

    @ApiOperation("新增用户")
    @PostMapping
    @PreAuthorize("hasAuthority('user:add')")
    public Result<Long> add(@Validated({UserDTO.Add.class, Default.class}) @RequestBody UserDTO dto) {
        User user = new User();
        BeanUtils.copyProperties(dto, user);
        boolean saved = userService.addUser(user);
        if (saved) {
            return Result.success(user.getId());
        } else {
            return Result.fail("新增用户失败");
        }
    }

    @ApiOperation("编辑用户")
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('user:edit')")
    public Result<Boolean> update(@PathVariable Long id, @Validated({UserDTO.Update.class, Default.class}) @RequestBody UserDTO dto) {
        User user = new User();
        BeanUtils.copyProperties(dto, user);
        user.setId(id);
        boolean updated = userService.updateById(user);
        return Result.success(updated);
    }

    @ApiOperation("删除用户")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('user:delete')")
    public Result<Boolean> delete(@PathVariable Long id) {
        boolean removed = userService.removeById(id);
        return Result.success(removed);
    }

    @ApiOperation("用户详情")
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('user:view')")
    public Result<UserVO> detail(@PathVariable Long id) {
        User user = userService.getById(id);
        if (user == null) return Result.success(null);
        UserVO vo = new UserVO();
        BeanUtils.copyProperties(user, vo);
        return Result.success(vo);
    }

    @ApiOperation("获取当前登录用户菜单")
    @GetMapping("/menus")
    public Result<List<Menu>> getUserMenus() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return Result.fail(401, "用户未认证");
        }

        Object principal = authentication.getPrincipal();
        String username = null;
        if (principal instanceof UserDetails) {
            username = ((UserDetails) principal).getUsername();
        } else if (principal instanceof String) {
            username = (String) principal;
        }

        if (username == null) {
            return Result.fail(500, "无法获取用户信息");
        }

        User currentUser = userService.getByUsername(username);
        if (currentUser == null) {
            return Result.fail(404, "用户不存在");
        }

        Long userId = currentUser.getId();
        if (userId == null) {
            return Result.fail(500, "无法获取用户ID");
        }

        //System.out.println("====userId:"+userId);
        List<Menu> userMenus = menuService.getUserMenus(userId);
        
        //System.out.println("====userMenus:"+userMenus);

        return Result.success(userMenus);
    }

    @ApiOperation("获取当前登录用户信息")
    @GetMapping("/current")
    public Result<UserVO> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return Result.fail(401, "用户未认证");
        }

        Object principal = authentication.getPrincipal();
        String username = null;
        if (principal instanceof UserDetails) {
            username = ((UserDetails) principal).getUsername();
        } else if (principal instanceof String) {
            username = (String) principal;
        }

        if (username == null) {
            return Result.fail(500, "无法获取用户信息");
        }

        User currentUser = userService.getByUsername(username);
        if (currentUser == null) {
            return Result.fail(404, "用户不存在");
        }

        UserVO vo = new UserVO();
        BeanUtils.copyProperties(currentUser, vo);
        return Result.success(vo);
    }

    @ApiOperation("获取当前用户的角色")
    @GetMapping("/current/roles")
    public Result<List<com.dz.ms.entity.Role>> getCurrentUserRoles() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return Result.fail(401, "用户未认证");
        }

        Object principal = authentication.getPrincipal();
        String username = null;
        if (principal instanceof UserDetails) {
            username = ((UserDetails) principal).getUsername();
        } else if (principal instanceof String) {
            username = (String) principal;
        }

        if (username == null) {
            return Result.fail(500, "无法获取用户信息");
        }

        User currentUser = userService.getByUsername(username);
        if (currentUser == null) {
            return Result.fail(404, "用户不存在");
        }

        Long userId = currentUser.getId();
        List<com.dz.ms.entity.Role> roles = userService.getUserRoles(userId);
        return Result.success(roles);
    }

    @ApiOperation("修改密码")
    @PostMapping("/updatePassword")
    public Result<Boolean> updatePassword(@RequestBody PasswordUpdateDTO passwordUpdateDTO) {
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return Result.fail(401, "用户未认证");
        }

        Object principal = authentication.getPrincipal();
        String username = null;
        if (principal instanceof UserDetails) {
            username = ((UserDetails) principal).getUsername();
        } else if (principal instanceof String) {
            username = (String) principal;
        }

        if (username == null) {
            return Result.fail(500, "无法获取用户信息");
        }

        User currentUser = userService.getByUsername(username);
        if (currentUser == null) {
            return Result.fail(404, "用户不存在");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(passwordUpdateDTO.getOldPassword(), currentUser.getPassword())) {
            return Result.fail(400, "原密码不正确");
        }

        // 更新密码
        currentUser.setPassword(passwordEncoder.encode(passwordUpdateDTO.getNewPassword()));
        boolean updated = userService.updateById(currentUser);
        
        return Result.success(updated);
    }
} 