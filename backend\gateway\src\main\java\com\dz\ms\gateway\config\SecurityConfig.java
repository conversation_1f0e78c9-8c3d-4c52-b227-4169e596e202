package com.dz.ms.gateway.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.context.annotation.Bean;

/**
 * 网关安全配置，禁用Basic认证和表单登录，防止浏览器弹窗
 */
@Configuration
@EnableWebFluxSecurity
public class SecurityConfig {
    @Bean
    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
        http
            .authorizeExchange()
                .anyExchange().permitAll()
                .and()
            .httpBasic().disable()
            .formLogin().disable()
            .csrf().disable();
        return http.build();
    }
} 