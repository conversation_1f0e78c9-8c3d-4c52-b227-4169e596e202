package com.dz.ms.lowcode.service.generator;

import com.dz.ms.lowcode.entity.MetaEntity;
import com.dz.ms.lowcode.entity.MetaField;
import com.dz.ms.lowcode.dto.MetaFieldDTO;

import java.util.List;

/**
 * 代码生成器接口
 * <AUTHOR>
 * @date 2023-07-01
 */
public interface CodeGeneratorService {
    
    /**
     * 生成代码
     * @param entity 元数据实体
     * @param fields 元数据字段列表
     */
    void generateCode(MetaEntity entity, List<MetaField> fields);
    
    /**
     * 生成数据库表
     * @param entity 元数据实体
     * @param fields 元数据字段列表
     * @return SQL语句
     */
    String generateTable(MetaEntity entity, List<MetaField> fields);
    
    /**
     * 生成实体类
     * @param entity 元数据实体
     * @param fields 元数据字段列表
     * @return Java代码
     */
    String generateEntity(MetaEntity entity, List<MetaField> fields);
    
    /**
     * 生成Mapper接口
     * @param entity 元数据实体
     * @param fields 元数据字段列表
     * @return Java代码
     */
    String generateMapper(MetaEntity entity, List<MetaField> fields);
    
    /**
     * 生成Service接口
     * @param entity 元数据实体
     * @param fields 元数据字段列表
     * @return Java代码
     */
    String generateService(MetaEntity entity, List<MetaField> fields);
    
    /**
     * 生成Service实现类
     * @param entity 元数据实体
     * @param fields 元数据字段列表
     * @return Java代码
     */
    String generateServiceImpl(MetaEntity entity, List<MetaField> fields);
    
    /**
     * 生成Controller
     * @param entity 元数据实体
     * @param fields 元数据字段列表
     * @return Java代码
     */
    String generateController(MetaEntity entity, List<MetaField> fields);
    
    /**
     * 生成ALTER TABLE SQL，对比旧字段和新字段
     * @param entityId 实体ID
     * @param newFieldDTOs 新字段DTO列表
     * @return ALTER SQL
     */
    String generateAlterTableSql(Long entityId, List<MetaFieldDTO> newFieldDTOs);
} 