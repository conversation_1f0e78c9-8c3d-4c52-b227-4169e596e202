/// <reference types="../../../node_modules/.vue-global-types/vue_3.5_0_0_0.d.ts" />
import { ref, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { getCurrentUserInfo, getCurrentUserRoles, updateUser, updatePassword as apiUpdatePassword } from '../../api/user';
;
// 用户信息
const userInfo = ref({
    username: '',
    status: 0
});
// 用户角色
const userRoles = ref([]);
// 加载状态
const loading = ref(true);
const basicSubmitting = ref(false);
const passwordSubmitting = ref(false);
// 当前激活的标签页
const activeTab = ref('basic');
// 表单引用
const basicFormRef = ref();
const passwordFormRef = ref();
// 基本信息表单
const basicForm = reactive({
    nickname: '',
    phone: '',
    email: ''
});
// 密码表单
const passwordForm = reactive({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
});
// 基本信息表单验证规则
const basicRules = reactive({
    nickname: [
        { max: 30, message: '昵称长度不能超过30个字符', trigger: 'blur' }
    ],
    phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    email: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ]
});
// 密码表单验证规则
const passwordRules = reactive({
    oldPassword: [
        { required: true, message: '请输入原密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
    ],
    newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
    ],
    confirmPassword: [
        { required: true, message: '请再次输入新密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' },
        {
            validator: (rule, value, callback) => {
                if (value !== passwordForm.newPassword) {
                    callback(new Error('两次输入的密码不一致'));
                }
                else {
                    callback();
                }
            },
            trigger: 'blur'
        }
    ]
});
// 格式化日期
function formatDate(dateStr) {
    if (!dateStr)
        return '-';
    try {
        const date = new Date(dateStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    catch (e) {
        return dateStr;
    }
}
// 获取用户信息
async function fetchUserInfo() {
    try {
        loading.value = true;
        const res = await getCurrentUserInfo();
        if (res && res.code === 0 && res.data) {
            userInfo.value = res.data;
            // 填充基本信息表单
            basicForm.nickname = res.data.nickname || '';
            basicForm.phone = res.data.phone || '';
            basicForm.email = res.data.email || '';
        }
        else {
            ElMessage.error(res?.msg || '获取用户信息失败');
        }
    }
    catch (error) {
        console.error('获取用户信息失败:', error);
        ElMessage.error('获取用户信息失败');
    }
    finally {
        loading.value = false;
    }
}
// 获取用户角色
async function fetchUserRoles() {
    try {
        const res = await getCurrentUserRoles();
        if (res && res.code === 0 && res.data) {
            userRoles.value = res.data;
        }
        else {
            ElMessage.error(res?.msg || '获取用户角色失败');
        }
    }
    catch (error) {
        console.error('获取用户角色失败:', error);
        ElMessage.error('获取用户角色失败');
    }
}
// 更新基本信息
async function updateBasicInfo() {
    if (!basicFormRef.value)
        return;
    await basicFormRef.value.validate(async (valid) => {
        if (!valid)
            return;
        try {
            basicSubmitting.value = true;
            // 准备更新的数据
            const updateData = {
                ...userInfo.value,
                nickname: basicForm.nickname,
                phone: basicForm.phone,
                email: basicForm.email
            };
            // 确保有用户ID
            if (!updateData.id) {
                ElMessage.error('用户ID不存在，无法更新信息');
                return;
            }
            // 调用更新接口
            const res = await updateUser(updateData.id, updateData);
            if (res && res.code === 0) {
                ElMessage.success('基本信息更新成功');
                // 重新获取用户信息
                await fetchUserInfo();
            }
            else {
                ElMessage.error(res?.msg || '更新基本信息失败');
            }
        }
        catch (error) {
            console.error('更新基本信息失败:', error);
            ElMessage.error('更新基本信息失败');
        }
        finally {
            basicSubmitting.value = false;
        }
    });
}
// 更新密码
async function updatePassword() {
    if (!passwordFormRef.value)
        return;
    try {
        // 先进行表单验证
        const valid = await passwordFormRef.value.validate();
        if (!valid)
            return;
        console.log('表单验证通过，准备提交密码修改请求');
        passwordSubmitting.value = true;
        // 调用修改密码接口
        const res = await apiUpdatePassword({
            oldPassword: passwordForm.oldPassword,
            newPassword: passwordForm.newPassword
        });
        console.log('密码修改请求响应:', res);
        if (res && res.code === 0) {
            ElMessage.success('密码修改成功，请重新登录');
            // 清空表单
            passwordForm.oldPassword = '';
            passwordForm.newPassword = '';
            passwordForm.confirmPassword = '';
            // 可以选择跳转到登录页或其他操作
            // router.push('/login');
        }
        else {
            ElMessage.error(res?.msg || '密码修改失败');
        }
    }
    catch (error) {
        console.error('密码修改失败:', error);
        ElMessage.error('密码修改失败: ' + (error instanceof Error ? error.message : String(error)));
    }
    finally {
        passwordSubmitting.value = false;
    }
}
// 页面加载时获取数据
onMounted(async () => {
    await fetchUserInfo();
    await fetchUserRoles();
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "my-account-container" },
});
const __VLS_0 = {}.ElRow;
/** @type {[typeof __VLS_components.ElRow, typeof __VLS_components.elRow, typeof __VLS_components.ElRow, typeof __VLS_components.elRow, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
    gutter: (20),
}));
const __VLS_2 = __VLS_1({
    gutter: (20),
}, ...__VLS_functionalComponentArgsRest(__VLS_1));
__VLS_3.slots.default;
const __VLS_4 = {}.ElCol;
/** @type {[typeof __VLS_components.ElCol, typeof __VLS_components.elCol, typeof __VLS_components.ElCol, typeof __VLS_components.elCol, ]} */ ;
// @ts-ignore
const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
    span: (12),
}));
const __VLS_6 = __VLS_5({
    span: (12),
}, ...__VLS_functionalComponentArgsRest(__VLS_5));
__VLS_7.slots.default;
const __VLS_8 = {}.ElCard;
/** @type {[typeof __VLS_components.ElCard, typeof __VLS_components.elCard, typeof __VLS_components.ElCard, typeof __VLS_components.elCard, ]} */ ;
// @ts-ignore
const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
    ...{ class: "account-card" },
}));
const __VLS_10 = __VLS_9({
    ...{ class: "account-card" },
}, ...__VLS_functionalComponentArgsRest(__VLS_9));
__VLS_11.slots.default;
{
    const { header: __VLS_thisSlot } = __VLS_11.slots;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "card-header" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
}
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "account-info" },
});
const __VLS_12 = {}.ElSkeleton;
/** @type {[typeof __VLS_components.ElSkeleton, typeof __VLS_components.elSkeleton, typeof __VLS_components.ElSkeleton, typeof __VLS_components.elSkeleton, ]} */ ;
// @ts-ignore
const __VLS_13 = __VLS_asFunctionalComponent(__VLS_12, new __VLS_12({
    rows: (4),
    animated: true,
    loading: (__VLS_ctx.loading),
}));
const __VLS_14 = __VLS_13({
    rows: (4),
    animated: true,
    loading: (__VLS_ctx.loading),
}, ...__VLS_functionalComponentArgsRest(__VLS_13));
__VLS_15.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_15.slots;
    const __VLS_16 = {}.ElDescriptions;
    /** @type {[typeof __VLS_components.ElDescriptions, typeof __VLS_components.elDescriptions, typeof __VLS_components.ElDescriptions, typeof __VLS_components.elDescriptions, ]} */ ;
    // @ts-ignore
    const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
        column: (1),
        border: true,
    }));
    const __VLS_18 = __VLS_17({
        column: (1),
        border: true,
    }, ...__VLS_functionalComponentArgsRest(__VLS_17));
    __VLS_19.slots.default;
    const __VLS_20 = {}.ElDescriptionsItem;
    /** @type {[typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, ]} */ ;
    // @ts-ignore
    const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({
        label: "用户名",
    }));
    const __VLS_22 = __VLS_21({
        label: "用户名",
    }, ...__VLS_functionalComponentArgsRest(__VLS_21));
    __VLS_23.slots.default;
    (__VLS_ctx.userInfo.username);
    var __VLS_23;
    const __VLS_24 = {}.ElDescriptionsItem;
    /** @type {[typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, ]} */ ;
    // @ts-ignore
    const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({
        label: "昵称",
    }));
    const __VLS_26 = __VLS_25({
        label: "昵称",
    }, ...__VLS_functionalComponentArgsRest(__VLS_25));
    __VLS_27.slots.default;
    (__VLS_ctx.userInfo.nickname || '-');
    var __VLS_27;
    const __VLS_28 = {}.ElDescriptionsItem;
    /** @type {[typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, ]} */ ;
    // @ts-ignore
    const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
        label: "角色",
    }));
    const __VLS_30 = __VLS_29({
        label: "角色",
    }, ...__VLS_functionalComponentArgsRest(__VLS_29));
    __VLS_31.slots.default;
    for (const [role] of __VLS_getVForSourceType((__VLS_ctx.userRoles))) {
        const __VLS_32 = {}.ElTag;
        /** @type {[typeof __VLS_components.ElTag, typeof __VLS_components.elTag, typeof __VLS_components.ElTag, typeof __VLS_components.elTag, ]} */ ;
        // @ts-ignore
        const __VLS_33 = __VLS_asFunctionalComponent(__VLS_32, new __VLS_32({
            key: (role.id),
            ...{ class: "role-tag" },
        }));
        const __VLS_34 = __VLS_33({
            key: (role.id),
            ...{ class: "role-tag" },
        }, ...__VLS_functionalComponentArgsRest(__VLS_33));
        __VLS_35.slots.default;
        (role.name);
        var __VLS_35;
    }
    if (!__VLS_ctx.userRoles.length) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
    }
    var __VLS_31;
    const __VLS_36 = {}.ElDescriptionsItem;
    /** @type {[typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, ]} */ ;
    // @ts-ignore
    const __VLS_37 = __VLS_asFunctionalComponent(__VLS_36, new __VLS_36({
        label: "状态",
    }));
    const __VLS_38 = __VLS_37({
        label: "状态",
    }, ...__VLS_functionalComponentArgsRest(__VLS_37));
    __VLS_39.slots.default;
    const __VLS_40 = {}.ElTag;
    /** @type {[typeof __VLS_components.ElTag, typeof __VLS_components.elTag, typeof __VLS_components.ElTag, typeof __VLS_components.elTag, ]} */ ;
    // @ts-ignore
    const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({
        type: (__VLS_ctx.userInfo.status === 1 ? 'success' : 'info'),
    }));
    const __VLS_42 = __VLS_41({
        type: (__VLS_ctx.userInfo.status === 1 ? 'success' : 'info'),
    }, ...__VLS_functionalComponentArgsRest(__VLS_41));
    __VLS_43.slots.default;
    (__VLS_ctx.userInfo.status === 1 ? '正常' : '禁用');
    var __VLS_43;
    var __VLS_39;
    const __VLS_44 = {}.ElDescriptionsItem;
    /** @type {[typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, ]} */ ;
    // @ts-ignore
    const __VLS_45 = __VLS_asFunctionalComponent(__VLS_44, new __VLS_44({
        label: "手机号",
    }));
    const __VLS_46 = __VLS_45({
        label: "手机号",
    }, ...__VLS_functionalComponentArgsRest(__VLS_45));
    __VLS_47.slots.default;
    (__VLS_ctx.userInfo.phone || '-');
    var __VLS_47;
    const __VLS_48 = {}.ElDescriptionsItem;
    /** @type {[typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, ]} */ ;
    // @ts-ignore
    const __VLS_49 = __VLS_asFunctionalComponent(__VLS_48, new __VLS_48({
        label: "邮箱",
    }));
    const __VLS_50 = __VLS_49({
        label: "邮箱",
    }, ...__VLS_functionalComponentArgsRest(__VLS_49));
    __VLS_51.slots.default;
    (__VLS_ctx.userInfo.email || '-');
    var __VLS_51;
    const __VLS_52 = {}.ElDescriptionsItem;
    /** @type {[typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, typeof __VLS_components.ElDescriptionsItem, typeof __VLS_components.elDescriptionsItem, ]} */ ;
    // @ts-ignore
    const __VLS_53 = __VLS_asFunctionalComponent(__VLS_52, new __VLS_52({
        label: "创建时间",
    }));
    const __VLS_54 = __VLS_53({
        label: "创建时间",
    }, ...__VLS_functionalComponentArgsRest(__VLS_53));
    __VLS_55.slots.default;
    (__VLS_ctx.formatDate(__VLS_ctx.userInfo.createTime));
    var __VLS_55;
    var __VLS_19;
}
var __VLS_15;
var __VLS_11;
var __VLS_7;
const __VLS_56 = {}.ElCol;
/** @type {[typeof __VLS_components.ElCol, typeof __VLS_components.elCol, typeof __VLS_components.ElCol, typeof __VLS_components.elCol, ]} */ ;
// @ts-ignore
const __VLS_57 = __VLS_asFunctionalComponent(__VLS_56, new __VLS_56({
    span: (12),
}));
const __VLS_58 = __VLS_57({
    span: (12),
}, ...__VLS_functionalComponentArgsRest(__VLS_57));
__VLS_59.slots.default;
const __VLS_60 = {}.ElCard;
/** @type {[typeof __VLS_components.ElCard, typeof __VLS_components.elCard, typeof __VLS_components.ElCard, typeof __VLS_components.elCard, ]} */ ;
// @ts-ignore
const __VLS_61 = __VLS_asFunctionalComponent(__VLS_60, new __VLS_60({
    ...{ class: "edit-card" },
}));
const __VLS_62 = __VLS_61({
    ...{ class: "edit-card" },
}, ...__VLS_functionalComponentArgsRest(__VLS_61));
__VLS_63.slots.default;
{
    const { header: __VLS_thisSlot } = __VLS_63.slots;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "card-header" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
}
const __VLS_64 = {}.ElTabs;
/** @type {[typeof __VLS_components.ElTabs, typeof __VLS_components.elTabs, typeof __VLS_components.ElTabs, typeof __VLS_components.elTabs, ]} */ ;
// @ts-ignore
const __VLS_65 = __VLS_asFunctionalComponent(__VLS_64, new __VLS_64({
    modelValue: (__VLS_ctx.activeTab),
}));
const __VLS_66 = __VLS_65({
    modelValue: (__VLS_ctx.activeTab),
}, ...__VLS_functionalComponentArgsRest(__VLS_65));
__VLS_67.slots.default;
const __VLS_68 = {}.ElTabPane;
/** @type {[typeof __VLS_components.ElTabPane, typeof __VLS_components.elTabPane, typeof __VLS_components.ElTabPane, typeof __VLS_components.elTabPane, ]} */ ;
// @ts-ignore
const __VLS_69 = __VLS_asFunctionalComponent(__VLS_68, new __VLS_68({
    label: "基本信息",
    name: "basic",
}));
const __VLS_70 = __VLS_69({
    label: "基本信息",
    name: "basic",
}, ...__VLS_functionalComponentArgsRest(__VLS_69));
__VLS_71.slots.default;
const __VLS_72 = {}.ElForm;
/** @type {[typeof __VLS_components.ElForm, typeof __VLS_components.elForm, typeof __VLS_components.ElForm, typeof __VLS_components.elForm, ]} */ ;
// @ts-ignore
const __VLS_73 = __VLS_asFunctionalComponent(__VLS_72, new __VLS_72({
    ref: "basicFormRef",
    model: (__VLS_ctx.basicForm),
    rules: (__VLS_ctx.basicRules),
    labelWidth: "80px",
    statusIcon: true,
}));
const __VLS_74 = __VLS_73({
    ref: "basicFormRef",
    model: (__VLS_ctx.basicForm),
    rules: (__VLS_ctx.basicRules),
    labelWidth: "80px",
    statusIcon: true,
}, ...__VLS_functionalComponentArgsRest(__VLS_73));
/** @type {typeof __VLS_ctx.basicFormRef} */ ;
var __VLS_76 = {};
__VLS_75.slots.default;
const __VLS_78 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_79 = __VLS_asFunctionalComponent(__VLS_78, new __VLS_78({
    label: "昵称",
    prop: "nickname",
}));
const __VLS_80 = __VLS_79({
    label: "昵称",
    prop: "nickname",
}, ...__VLS_functionalComponentArgsRest(__VLS_79));
__VLS_81.slots.default;
const __VLS_82 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_83 = __VLS_asFunctionalComponent(__VLS_82, new __VLS_82({
    modelValue: (__VLS_ctx.basicForm.nickname),
    placeholder: "请输入昵称",
}));
const __VLS_84 = __VLS_83({
    modelValue: (__VLS_ctx.basicForm.nickname),
    placeholder: "请输入昵称",
}, ...__VLS_functionalComponentArgsRest(__VLS_83));
var __VLS_81;
const __VLS_86 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_87 = __VLS_asFunctionalComponent(__VLS_86, new __VLS_86({
    label: "手机号",
    prop: "phone",
}));
const __VLS_88 = __VLS_87({
    label: "手机号",
    prop: "phone",
}, ...__VLS_functionalComponentArgsRest(__VLS_87));
__VLS_89.slots.default;
const __VLS_90 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_91 = __VLS_asFunctionalComponent(__VLS_90, new __VLS_90({
    modelValue: (__VLS_ctx.basicForm.phone),
    placeholder: "请输入手机号",
}));
const __VLS_92 = __VLS_91({
    modelValue: (__VLS_ctx.basicForm.phone),
    placeholder: "请输入手机号",
}, ...__VLS_functionalComponentArgsRest(__VLS_91));
var __VLS_89;
const __VLS_94 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_95 = __VLS_asFunctionalComponent(__VLS_94, new __VLS_94({
    label: "邮箱",
    prop: "email",
}));
const __VLS_96 = __VLS_95({
    label: "邮箱",
    prop: "email",
}, ...__VLS_functionalComponentArgsRest(__VLS_95));
__VLS_97.slots.default;
const __VLS_98 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_99 = __VLS_asFunctionalComponent(__VLS_98, new __VLS_98({
    modelValue: (__VLS_ctx.basicForm.email),
    placeholder: "请输入邮箱",
}));
const __VLS_100 = __VLS_99({
    modelValue: (__VLS_ctx.basicForm.email),
    placeholder: "请输入邮箱",
}, ...__VLS_functionalComponentArgsRest(__VLS_99));
var __VLS_97;
const __VLS_102 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_103 = __VLS_asFunctionalComponent(__VLS_102, new __VLS_102({}));
const __VLS_104 = __VLS_103({}, ...__VLS_functionalComponentArgsRest(__VLS_103));
__VLS_105.slots.default;
const __VLS_106 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_107 = __VLS_asFunctionalComponent(__VLS_106, new __VLS_106({
    ...{ 'onClick': {} },
    type: "primary",
    loading: (__VLS_ctx.basicSubmitting),
}));
const __VLS_108 = __VLS_107({
    ...{ 'onClick': {} },
    type: "primary",
    loading: (__VLS_ctx.basicSubmitting),
}, ...__VLS_functionalComponentArgsRest(__VLS_107));
let __VLS_110;
let __VLS_111;
let __VLS_112;
const __VLS_113 = {
    onClick: (__VLS_ctx.updateBasicInfo)
};
__VLS_109.slots.default;
var __VLS_109;
var __VLS_105;
var __VLS_75;
var __VLS_71;
const __VLS_114 = {}.ElTabPane;
/** @type {[typeof __VLS_components.ElTabPane, typeof __VLS_components.elTabPane, typeof __VLS_components.ElTabPane, typeof __VLS_components.elTabPane, ]} */ ;
// @ts-ignore
const __VLS_115 = __VLS_asFunctionalComponent(__VLS_114, new __VLS_114({
    label: "修改密码",
    name: "password",
}));
const __VLS_116 = __VLS_115({
    label: "修改密码",
    name: "password",
}, ...__VLS_functionalComponentArgsRest(__VLS_115));
__VLS_117.slots.default;
const __VLS_118 = {}.ElForm;
/** @type {[typeof __VLS_components.ElForm, typeof __VLS_components.elForm, typeof __VLS_components.ElForm, typeof __VLS_components.elForm, ]} */ ;
// @ts-ignore
const __VLS_119 = __VLS_asFunctionalComponent(__VLS_118, new __VLS_118({
    ref: "passwordFormRef",
    model: (__VLS_ctx.passwordForm),
    rules: (__VLS_ctx.passwordRules),
    labelWidth: "100px",
    statusIcon: true,
}));
const __VLS_120 = __VLS_119({
    ref: "passwordFormRef",
    model: (__VLS_ctx.passwordForm),
    rules: (__VLS_ctx.passwordRules),
    labelWidth: "100px",
    statusIcon: true,
}, ...__VLS_functionalComponentArgsRest(__VLS_119));
/** @type {typeof __VLS_ctx.passwordFormRef} */ ;
var __VLS_122 = {};
__VLS_121.slots.default;
const __VLS_124 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_125 = __VLS_asFunctionalComponent(__VLS_124, new __VLS_124({
    label: "原密码",
    prop: "oldPassword",
}));
const __VLS_126 = __VLS_125({
    label: "原密码",
    prop: "oldPassword",
}, ...__VLS_functionalComponentArgsRest(__VLS_125));
__VLS_127.slots.default;
const __VLS_128 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_129 = __VLS_asFunctionalComponent(__VLS_128, new __VLS_128({
    modelValue: (__VLS_ctx.passwordForm.oldPassword),
    type: "password",
    placeholder: "请输入原密码",
    showPassword: true,
}));
const __VLS_130 = __VLS_129({
    modelValue: (__VLS_ctx.passwordForm.oldPassword),
    type: "password",
    placeholder: "请输入原密码",
    showPassword: true,
}, ...__VLS_functionalComponentArgsRest(__VLS_129));
var __VLS_127;
const __VLS_132 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_133 = __VLS_asFunctionalComponent(__VLS_132, new __VLS_132({
    label: "新密码",
    prop: "newPassword",
}));
const __VLS_134 = __VLS_133({
    label: "新密码",
    prop: "newPassword",
}, ...__VLS_functionalComponentArgsRest(__VLS_133));
__VLS_135.slots.default;
const __VLS_136 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_137 = __VLS_asFunctionalComponent(__VLS_136, new __VLS_136({
    modelValue: (__VLS_ctx.passwordForm.newPassword),
    type: "password",
    placeholder: "请输入新密码",
    showPassword: true,
}));
const __VLS_138 = __VLS_137({
    modelValue: (__VLS_ctx.passwordForm.newPassword),
    type: "password",
    placeholder: "请输入新密码",
    showPassword: true,
}, ...__VLS_functionalComponentArgsRest(__VLS_137));
var __VLS_135;
const __VLS_140 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_141 = __VLS_asFunctionalComponent(__VLS_140, new __VLS_140({
    label: "确认新密码",
    prop: "confirmPassword",
}));
const __VLS_142 = __VLS_141({
    label: "确认新密码",
    prop: "confirmPassword",
}, ...__VLS_functionalComponentArgsRest(__VLS_141));
__VLS_143.slots.default;
const __VLS_144 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_145 = __VLS_asFunctionalComponent(__VLS_144, new __VLS_144({
    modelValue: (__VLS_ctx.passwordForm.confirmPassword),
    type: "password",
    placeholder: "请再次输入新密码",
    showPassword: true,
}));
const __VLS_146 = __VLS_145({
    modelValue: (__VLS_ctx.passwordForm.confirmPassword),
    type: "password",
    placeholder: "请再次输入新密码",
    showPassword: true,
}, ...__VLS_functionalComponentArgsRest(__VLS_145));
var __VLS_143;
const __VLS_148 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_149 = __VLS_asFunctionalComponent(__VLS_148, new __VLS_148({}));
const __VLS_150 = __VLS_149({}, ...__VLS_functionalComponentArgsRest(__VLS_149));
__VLS_151.slots.default;
const __VLS_152 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_153 = __VLS_asFunctionalComponent(__VLS_152, new __VLS_152({
    ...{ 'onClick': {} },
    type: "primary",
    loading: (__VLS_ctx.passwordSubmitting),
}));
const __VLS_154 = __VLS_153({
    ...{ 'onClick': {} },
    type: "primary",
    loading: (__VLS_ctx.passwordSubmitting),
}, ...__VLS_functionalComponentArgsRest(__VLS_153));
let __VLS_156;
let __VLS_157;
let __VLS_158;
const __VLS_159 = {
    onClick: (__VLS_ctx.updatePassword)
};
__VLS_155.slots.default;
var __VLS_155;
var __VLS_151;
var __VLS_121;
var __VLS_117;
var __VLS_67;
var __VLS_63;
var __VLS_59;
var __VLS_3;
/** @type {__VLS_StyleScopedClasses['my-account-container']} */ ;
/** @type {__VLS_StyleScopedClasses['account-card']} */ ;
/** @type {__VLS_StyleScopedClasses['card-header']} */ ;
/** @type {__VLS_StyleScopedClasses['account-info']} */ ;
/** @type {__VLS_StyleScopedClasses['role-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['edit-card']} */ ;
/** @type {__VLS_StyleScopedClasses['card-header']} */ ;
// @ts-ignore
var __VLS_77 = __VLS_76, __VLS_123 = __VLS_122;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            userInfo: userInfo,
            userRoles: userRoles,
            loading: loading,
            basicSubmitting: basicSubmitting,
            passwordSubmitting: passwordSubmitting,
            activeTab: activeTab,
            basicFormRef: basicFormRef,
            passwordFormRef: passwordFormRef,
            basicForm: basicForm,
            passwordForm: passwordForm,
            basicRules: basicRules,
            passwordRules: passwordRules,
            formatDate: formatDate,
            updateBasicInfo: updateBasicInfo,
            updatePassword: updatePassword,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
