package com.dz.ms.lowcode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 元数据实体类，用于存储用户自定义的实体定义
 * <AUTHOR>
 * @date 2023-07-01
 */
@Data
@TableName("lc_meta_entity")
public class MetaEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 实体编码，用于生成表名和类名 */
    @TableField("entity_code")
    private String entityCode;

    /** 实体名称 */
    @TableField("entity_name")
    private String entityName;

    /** 实体描述 */
    @TableField("description")
    private String description;

    /** 数据库表名 */
    @TableField("table_name")
    private String tableName;

    /** 是否已发布 */
    @TableField("is_published")
    private Boolean published;

    /** 是否启用 */
    @TableField("is_enabled")
    private Boolean enabled;

    /** 创建人 */
    @TableField("creator")
    private String creator;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 更新人 */
    @TableField("updater")
    private String updater;

    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;
} 