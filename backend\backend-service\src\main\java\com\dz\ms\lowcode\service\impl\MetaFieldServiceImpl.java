package com.dz.ms.lowcode.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.ms.lowcode.entity.MetaField;
import com.dz.ms.lowcode.mapper.MetaFieldMapper;
import com.dz.ms.lowcode.service.MetaFieldService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 元数据字段Service实现类
 * <AUTHOR>
 * @date 2023-07-01
 */
@Service
public class MetaFieldServiceImpl extends ServiceImpl<MetaFieldMapper, MetaField> implements MetaFieldService {

    @Override
    public List<MetaField> getFieldsByEntityId(Long entityId) {
        return baseMapper.selectByEntityId(entityId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveFields(List<MetaField> fields, Long entityId) {
        if (fields != null && !fields.isEmpty()) {
            Date now = new Date();
            for (MetaField field : fields) {
                field.setEntityId(entityId);
                field.setCreateTime(now);
                field.setUpdateTime(now);
                // 如果没有设置排序号，则默认为0
                if (field.getSortNo() == null) {
                    field.setSortNo(0);
                }
            }
            return saveBatch(fields);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFields(List<MetaField> fields, Long entityId) {
        // 先删除旧的字段
        deleteByEntityId(entityId);
        // 再保存新的字段
        return saveFields(fields, entityId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByEntityId(Long entityId) {
        LambdaQueryWrapper<MetaField> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MetaField::getEntityId, entityId);
        return remove(wrapper);
    }
} 