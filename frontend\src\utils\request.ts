import axios from 'axios';
import { ElMessage } from 'element-plus';
import router from '../router';
import { usePermissionStore } from '../store/permission';
import { useMenuStore } from '../store/menu';

// 创建axios实例
const service = axios.create({
  baseURL: '/api', // 所有请求都以/api开头，通过vite proxy转发
  timeout: 5000, // 请求超时时间
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    const token = localStorage.getItem('token');
    if (token) {
      // 修改token的传递方式，确保正确传递JWT token，添加Bearer前缀
      config.headers['Authorization'] = 'Bearer ' + token;
    }
    return config;
  },
  error => {
    // 对请求错误做些什么
    console.error('Axios请求拦截错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 检查响应类型
    const contentType = response.headers['content-type'];
    
    // 如果是二进制数据（如图片、文件等），直接返回响应数据
    if (contentType && (
        contentType.includes('image') || 
        contentType.includes('application/octet-stream') ||
        contentType.includes('application/pdf') ||
        contentType.includes('application/vnd')
      )) {
      return response.data;
    }
    
    // 对JSON响应数据做处理
    const res = response.data;
    if (res.code !== 0) {
      ElMessage.error(res.msg || '请求失败');
      return Promise.reject(new Error(res.msg || 'Error'));
    } else {
      return res;
    }
  },
  error => {
    // 对响应错误做点什么
    console.error('Axios响应拦截错误:', error.response || error);
    
    if (error.response) {
      const { status } = error.response;
      
      // 处理401未认证
      if (status === 401) {
        // 清除用户信息
        localStorage.removeItem('token');
        const permissionStore = usePermissionStore();
        const menuStore = useMenuStore();
        permissionStore.clearPermissions();
        menuStore.setMenuTree([]);
        
        // 显示消息
        ElMessage.error('登录已过期，请重新登录');
        
        // 如果当前不在登录页，则跳转到登录页
        if (router.currentRoute.value.path !== '/login') {
          router.push('/login');
        }
        return Promise.reject(error);
      }
      
      // 处理403权限不足
      if (status === 403) {
        ElMessage.error('没有权限访问该资源');
        // 跳转到403页面
        router.push('/403');
        return Promise.reject(error);
      }
      
      // 处理其他错误
      const message = error.response.data?.msg || error.response.data?.error || error.message;
      ElMessage.error(message || '请求失败');
    } else if (error.message.includes('timeout')) {
      ElMessage.error('请求超时，请稍后重试');
    } else {
      ElMessage.error('网络错误，请稍后重试');
    }
    
    return Promise.reject(error);
  }
);

// 将axios实例导出，供其他地方使用
export default service; 