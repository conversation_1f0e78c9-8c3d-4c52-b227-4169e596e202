# JMeter Sentinel 压测指导手册

## 🎯 测试目标

验证 Sentinel 限流和熔断功能是否正常工作，包括：
- 流量控制效果
- 熔断降级机制
- 系统保护能力
- 监控数据准确性

## 🚀 测试前准备

### 1. 启动必要服务

```bash
# 1. 启动 Nacos
cd nacos/bin
startup.cmd -m standalone

# 2. 启动 Sentinel Dashboard
java -jar sentinel-dashboard-1.8.6.jar

# 3. 启动网关服务
cd backend/gateway
mvn spring-boot:run

# 4. 启动后端服务
cd backend/backend-service
mvn spring-boot:run
```

### 2. 验证服务状态

- Nacos 控制台：http://localhost:8848/nacos
- Sentinel 控制台：http://localhost:8080 (sentinel/sentinel)
- 网关服务：http://localhost:9090
- 后端服务：http://localhost:9091

### 3. 确认规则配置

在 Nacos 控制台中确认以下配置已创建：
- `backend-service-flow-rules`
- `backend-service-degrade-rules`
- `backend-service-system-rules`
- `gateway-service-gw-flow-rules`
- `gateway-service-gw-api-group-rules`

## 📊 测试脚本说明

### 1. 用户接口限流测试 (user-api-flow-test.jmx)
- **目标**：测试用户接口 10 QPS 限流
- **配置**：20 线程，5秒启动，循环10次
- **预期**：超过 10 QPS 的请求返回 429 状态码

### 2. 会员接口限流测试 (member-api-flow-test.jmx)
- **目标**：测试会员接口 20 QPS 限流
- **配置**：30 线程，5秒启动，循环15次
- **预期**：超过 20 QPS 的请求返回 429 状态码

### 3. 综合压测 (comprehensive-flow-test.jmx)
- **目标**：同时测试所有接口的限流效果
- **配置**：分组串行执行，模拟真实业务场景
- **预期**：各接口按配置限流，不互相影响

## 🔧 JMeter 执行步骤

### ⚠️ 重要：认证配置

**会员接口需要 JWT Token 认证**，测试前必须先获取 Token：

1. **获取验证码**
   ```
   GET http://localhost:9090/auth/captcha
   ```

2. **登录获取 Token**
   ```
   POST http://localhost:9090/auth/login
   Content-Type: application/json

   {
     "username": "admin",
     "password": "123456",
     "captcha": "test",
     "captchaId": "从验证码接口获取"
   }
   ```

3. **在 JMeter 中配置认证**
   - 添加 HTTP Header Manager
   - 设置 Authorization: Bearer {token}

### 方式一：GUI 模式（推荐用于调试）

1. **启动 JMeter**
   ```bash
   jmeter.bat  # Windows
   jmeter.sh   # Linux/Mac
   ```

2. **打开测试脚本**
   - 文件 → 打开 → 选择 `.jmx` 文件

3. **配置认证参数**
   - 先执行登录请求获取 Token
   - 在会员接口测试中添加 Authorization Header
   - 检查线程组设置
   - 确认目标服务器地址

4. **执行测试**
   - 点击绿色播放按钮
   - 观察实时结果

### 方式二：命令行模式（推荐用于正式测试）

```bash
# 执行用户接口测试
jmeter -n -t jmeter-tests/user-api-flow-test.jmx -l results/user-test-results.jtl -e -o results/user-test-report

# 执行会员接口测试
jmeter -n -t jmeter-tests/member-api-flow-test.jmx -l results/member-test-results.jtl -e -o results/member-test-report

# 执行综合测试
jmeter -n -t jmeter-tests/comprehensive-flow-test.jmx -l results/comprehensive-test-results.jtl -e -o results/comprehensive-test-report
```

## 📈 监控和观察

### 1. Sentinel Dashboard 监控

访问 http://localhost:8080，观察：
- **实时监控**：QPS、响应时间、异常比例
- **流控规则**：规则配置和触发情况
- **熔断规则**：熔断状态变化
- **机器列表**：应用接入情况

### 2. JMeter 结果分析

关注以下指标：
- **Throughput (吞吐量)**：实际 QPS
- **Response Time**：响应时间分布
- **Error Rate**：错误率（429 状态码）
- **Active Threads**：并发线程数

### 3. 应用日志

```bash
# 查看限流日志
tail -f logs/backend-service.log | grep -i sentinel

# 查看错误日志
tail -f logs/backend-service.log | grep -i error
```

## 🎯 预期测试结果

### 1. 用户接口 (10 QPS 限制)
- **正常情况**：QPS ≤ 10，响应码 200
- **限流触发**：QPS > 10，部分请求返回 429
- **限流消息**："请求过于频繁，请稍后再试"

### 2. 会员接口 (20 QPS 限制)
- **正常情况**：QPS ≤ 20，响应码 200
- **限流触发**：QPS > 20，部分请求返回 429

### 3. 菜单接口 (50 QPS 限制)
- **正常情况**：QPS ≤ 50，响应码 200
- **限流触发**：QPS > 50，部分请求返回 429

### 4. 熔断测试
- **正常状态**：请求正常处理
- **熔断触发**：异常率/响应时间超阈值时熔断
- **熔断恢复**：熔断时间窗口后自动恢复

## 🔍 故障排查

### 1. 限流不生效
- 检查 Nacos 配置是否正确
- 确认应用是否连接到 Sentinel Dashboard
- 查看应用启动日志中的 Sentinel 初始化信息

### 2. 监控数据不准确
- 确认时钟同步
- 检查网络延迟
- 验证 JMeter 配置

### 3. 性能异常
- 监控系统资源使用情况
- 检查数据库连接池
- 查看 GC 日志

## 📊 测试报告模板

### 测试环境
- JMeter 版本：5.4.1
- Java 版本：1.8
- 操作系统：Windows 10
- 硬件配置：8GB RAM, 4 Core CPU

### 测试结果

| 接口 | 配置限制 | 实际 QPS | 限流触发 | 响应时间 | 错误率 |
|------|----------|----------|----------|----------|--------|
| 用户接口 | 10 QPS | 9.8 | ✅ | 50ms | 15% |
| 会员接口 | 20 QPS | 19.5 | ✅ | 45ms | 12% |
| 菜单接口 | 50 QPS | 48.2 | ✅ | 30ms | 8% |

### 结论
- ✅ 限流功能正常工作
- ✅ 熔断机制有效
- ✅ 监控数据准确
- ⚠️ 建议优化响应时间

## 🎉 测试完成后

1. **保存测试结果**：导出 JMeter 报告
2. **分析监控数据**：截图 Sentinel Dashboard
3. **调整规则配置**：根据测试结果优化
4. **文档记录**：更新测试文档和配置说明
