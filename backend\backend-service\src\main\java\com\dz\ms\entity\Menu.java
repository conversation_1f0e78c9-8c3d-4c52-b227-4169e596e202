package com.dz.ms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 菜单实体类
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
@Data
@TableName("menu")
public class Menu implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 父菜单ID */
    private Long parentId;

    /** 菜单名称 */
    private String name;

    /** 路由路径 */
    private String path;

    /** 组件路径 */
    private String component;

    /** 图标 */
    private String icon;

    /** 菜单类型（M目录 C菜单 F按钮） */
    private String type;

    /** 权限标识 */
    private String permission;

    /** 排序 */
    private Integer orderNum;

    /** 菜单状态（0隐藏 1显示） */
    private Integer visible;

    /** 菜单状态（0正常 1停用） */
    private Integer status;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 子菜单 */
    @TableField(exist = false)
    private List<Menu> children;
} 