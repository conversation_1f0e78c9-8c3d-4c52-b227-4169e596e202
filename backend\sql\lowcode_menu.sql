-- 添加低代码平台相关的菜单和权限
-- 注意：执行前请确认menu表结构，字段可能与下面的不完全一致

-- 1. 添加低代码平台主菜单
INSERT INTO menu (parent_id, name, path, component, icon, type, permission, order_num, visible, status)
VALUES (
    0, -- 父菜单ID，0表示顶级菜单
    '低代码平台', -- 菜单名称
    '/lowcode', -- 路由路径
    'Layout', -- 组件路径，顶级菜单通常使用Layout
    'el-icon-magic-stick', -- 图标
    'M', -- 类型，M表示菜单
    'lowcode', -- 权限标识，与router中一致
    50, -- 排序，放在后面
    1, -- 可见，1表示显示
    1 -- 状态，1表示正常
);

-- 获取刚插入的低代码平台主菜单ID
SET @lowcodeMenuId = LAST_INSERT_ID();

-- 2. 添加实体管理子菜单
INSERT INTO menu (parent_id, name, path, component, icon, type, permission, order_num, visible, status)
VALUES (
    @lowcodeMenuId, -- 父菜单ID，使用低代码平台主菜单ID
    '实体管理', -- 菜单名称
    'entity', -- 路由路径，修改为相对路径
    'LowCode/Entity/index', -- 组件路径
    'el-icon-document', -- 图标
    'C', -- 类型，C表示菜单项
    'lowcode:entity', -- 权限标识，与router中一致
    1, -- 排序
    1, -- 可见，1表示显示
    1 -- 状态，1表示正常
);

-- 3. 为管理员角色分配低代码平台权限
-- 假设管理员角色ID为1，请根据实际情况修改
INSERT INTO role_menu (role_id, menu_id)
SELECT 1, id FROM menu WHERE permission IN ('lowcode', 'lowcode:entity'); 