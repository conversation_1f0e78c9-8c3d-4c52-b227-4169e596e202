package com.dz.ms.util;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.Random;

/**
 * 验证码工具类
 * <AUTHOR>
 * @date 2025-05-30
 * @version 1.0.0
 */
public class CaptchaUtil {
    
    private static final String CAPTCHA_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final Random RANDOM = new Random();
    
    // 验证码图片宽度
    private static final int WIDTH = 120;
    // 验证码图片高度
    private static final int HEIGHT = 40;
    // 验证码长度
    private static final int CODE_LENGTH = 4;
    // 干扰线数量
    private static final int NOISE_LINE_COUNT = 10;
    // 干扰点数量
    private static final int NOISE_POINT_COUNT = 50;
    
    /**
     * 生成随机验证码
     * @return 验证码字符串
     */
    public static String generateCaptchaCode() {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = RANDOM.nextInt(CAPTCHA_CHARS.length());
            code.append(CAPTCHA_CHARS.charAt(index));
        }
        return code.toString();
    }
    
    /**
     * 根据验证码文本生成验证码图片
     * @param code 验证码文本
     * @return 验证码图片
     */
    public static BufferedImage generateCaptchaImage(String code) {
        // 创建图像缓冲区
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // 设置抗锯齿
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 设置背景色
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, WIDTH, HEIGHT);
        
        // 绘制干扰线
        drawNoiseLines(g);
        
        // 绘制干扰点
        drawNoisePoints(g);
        
        // 绘制验证码
        drawCaptchaText(g, code);
        
        // 释放图形上下文
        g.dispose();
        
        return image;
    }
    
    /**
     * 绘制干扰线
     * @param g 图形上下文
     */
    private static void drawNoiseLines(Graphics2D g) {
        g.setColor(getRandomColor());
        for (int i = 0; i < NOISE_LINE_COUNT; i++) {
            int x1 = RANDOM.nextInt(WIDTH);
            int y1 = RANDOM.nextInt(HEIGHT);
            int x2 = RANDOM.nextInt(WIDTH);
            int y2 = RANDOM.nextInt(HEIGHT);
            g.drawLine(x1, y1, x2, y2);
        }
    }
    
    /**
     * 绘制干扰点
     * @param g 图形上下文
     */
    private static void drawNoisePoints(Graphics2D g) {
        for (int i = 0; i < NOISE_POINT_COUNT; i++) {
            int x = RANDOM.nextInt(WIDTH);
            int y = RANDOM.nextInt(HEIGHT);
            g.setColor(getRandomColor());
            g.fillRect(x, y, 1, 1);
        }
    }
    
    /**
     * 绘制验证码文本
     * @param g 图形上下文
     * @param code 验证码文本
     */
    private static void drawCaptchaText(Graphics2D g, String code) {
        // 设置字体
        g.setFont(new Font("Arial", Font.BOLD, 24));
        
        // 计算每个字符的宽度
        int charWidth = WIDTH / (code.length() + 2);
        int charX = (WIDTH - code.length() * charWidth) / 2;
        
        // 绘制每个字符
        for (int i = 0; i < code.length(); i++) {
            g.setColor(getRandomDarkColor());
            
            // 随机旋转角度
            int degree = RANDOM.nextInt(30) - 15;
            
            // 保存当前图形状态
            g.rotate(Math.toRadians(degree), charX + charWidth * i, HEIGHT / 2);
            
            // 绘制字符
            g.drawString(String.valueOf(code.charAt(i)), charX + charWidth * i, HEIGHT / 2 + 8);
            
            // 恢复图形状态
            g.rotate(Math.toRadians(-degree), charX + charWidth * i, HEIGHT / 2);
        }
    }
    
    /**
     * 获取随机颜色
     * @return 随机颜色
     */
    private static Color getRandomColor() {
        int r = RANDOM.nextInt(256);
        int g = RANDOM.nextInt(256);
        int b = RANDOM.nextInt(256);
        return new Color(r, g, b);
    }
    
    /**
     * 获取随机深色（用于文字显示）
     * @return 随机深色
     */
    private static Color getRandomDarkColor() {
        int r = RANDOM.nextInt(100);
        int g = RANDOM.nextInt(100);
        int b = RANDOM.nextInt(100);
        return new Color(r, g, b);
    }
} 