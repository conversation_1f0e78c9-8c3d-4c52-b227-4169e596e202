export interface test {
    id?: number;
    createTime?: string;
    updateTime?: string;
}
export interface testPageQuery {
    pageNum?: number;
    pageSize?: number;
}
export declare function gettestList(params: testPageQuery): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getAlltests(): Promise<import("axios").AxiosResponse<any, any>>;
export declare function gettestDetail(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function savetest(data: test): Promise<import("axios").AxiosResponse<any, any>>;
export declare function updatetest(id: number, data: test): Promise<import("axios").AxiosResponse<any, any>>;
export declare function deletetest(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function batchDeletetest(ids: number[]): Promise<import("axios").AxiosResponse<any, any>>;
export declare function exporttest(params?: testPageQuery): Promise<import("axios").AxiosResponse<any, any>>;
