import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 查找Vue文件
console.log('查找Vue文件...');
const findVueFiles = () => {
  try {
    const cmd = 'cd ' + __dirname + ' && dir /s /b *.vue';
    const stdout = execSync(cmd, { encoding: 'utf8' });
    return stdout.split('\r\n').filter(Boolean);
  } catch (error) {
    console.error('查找文件时出错:', error);
    return [];
  }
};

// 查找TS文件
console.log('查找TS文件...');
const findTsFiles = () => {
  try {
    const cmd = 'cd ' + __dirname + ' && dir /s /b *.ts';
    const stdout = execSync(cmd, { encoding: 'utf8' });
    return stdout.split('\r\n').filter(Boolean);
  } catch (error) {
    console.error('查找文件时出错:', error);
    return [];
  }
};

// 获取所有导出的接口
const getExportedInterfaces = (tsFiles) => {
  const interfaces = new Set();
  
  for (const file of tsFiles) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const interfaceMatches = content.match(/export\s+interface\s+(\w+)/g);
      
      if (interfaceMatches) {
        for (const match of interfaceMatches) {
          const interfaceName = match.split(/\s+/)[2];
          interfaces.add(interfaceName);
        }
      }
    } catch (error) {
      console.error(`处理文件 ${file} 时出错:`, error);
    }
  }
  
  return interfaces;
};

// 修复文件中的类型导入
const fixFile = (filePath, interfaces) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否包含脚本部分
    if (!content.includes('<script')) return false;
    
    let modified = false;
    let newContent = content;
    
    // 查找所有导入语句
    const importRegex = /import\s*{([^}]*)}\s*from\s*['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      const importedItems = match[1].split(',').map(item => item.trim());
      const source = match[2];
      const fullImport = match[0];
      
      // 检查是否有接口类型被直接导入
      const typeImports = [];
      const nonTypeImports = [];
      
      for (const item of importedItems) {
        if (item && interfaces.has(item)) {
          typeImports.push(item);
        } else if (item) {
          nonTypeImports.push(item);
        }
      }
      
      // 如果有接口类型被直接导入，修复导入语句
      if (typeImports.length > 0) {
        let newImports = '';
        
        // 保留非类型导入
        if (nonTypeImports.length > 0) {
          newImports += `import { ${nonTypeImports.join(', ')} } from '${source}';\n`;
        }
        
        // 添加类型导入
        newImports += `import type { ${typeImports.join(', ')} } from '${source}';`;
        
        // 替换原导入语句
        newContent = newContent.replace(fullImport, newImports);
        modified = true;
        console.log(`修复了文件 ${filePath} 中的类型导入: ${typeImports.join(', ')}`);
      }
    }
    
    // 如果文件被修改，则写回
    if (modified) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`处理文件 ${filePath} 时出错:`, error);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('开始检查和修复类型导入问题...');
  
  const tsFiles = findTsFiles();
  console.log(`找到 ${tsFiles.length} 个TS文件`);
  
  const interfaces = getExportedInterfaces(tsFiles);
  console.log(`找到 ${interfaces.size} 个导出的接口`);
  
  const vueFiles = findVueFiles();
  console.log(`找到 ${vueFiles.length} 个Vue文件`);
  
  let fixedCount = 0;
  for (const file of vueFiles) {
    if (fixFile(file, interfaces)) {
      fixedCount++;
    }
  }
  
  console.log(`检查完成，共修复了 ${fixedCount} 个文件`);
};

main(); 