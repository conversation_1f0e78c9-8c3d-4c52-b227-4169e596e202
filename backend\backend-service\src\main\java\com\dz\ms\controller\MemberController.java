package com.dz.ms.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.dz.ms.dto.MemberDTO;
import com.dz.ms.entity.Member;
import com.dz.ms.service.MemberService;
import com.dz.ms.vo.MemberVO;
import com.dz.ms.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员管理
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@RestController
@RequestMapping("/member")
@Api(tags = "会员管理")
@Validated
public class MemberController {
    @Autowired
    private MemberService memberService;

    @ApiOperation("分页查询会员")
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('member:view')")
    public Result<IPage<MemberVO>> page(@RequestParam(defaultValue = "1") long pageNum,
                                        @RequestParam(defaultValue = "10") long pageSize,
                                        @RequestParam(required = false) String username) {
        Page<Member> page = new Page<>(pageNum, pageSize);
        IPage<Member> memberPage = memberService.lambdaQuery()
                .like(username != null && !username.isEmpty(), Member::getUsername, username)
                .orderByDesc(Member::getCreateTime)
                .page(page);
        IPage<MemberVO> voPage = memberPage.convert(member -> {
            MemberVO vo = new MemberVO();
            BeanUtils.copyProperties(member, vo);
            return vo;
        });
        return Result.success(voPage);
    }
    
    @ApiOperation("获取所有会员")
    @GetMapping("/list")
    public Result<List<MemberVO>> list() {
        List<Member> members = memberService.lambdaQuery()
                .eq(Member::getStatus, 1) // 只获取状态为启用的会员
                .orderByAsc(Member::getId)
                .list();
        List<MemberVO> voList = members.stream().map(member -> {
            MemberVO vo = new MemberVO();
            BeanUtils.copyProperties(member, vo);
            return vo;
        }).collect(Collectors.toList());
        return Result.success(voList);
    }

    @ApiOperation("新增会员")
    @PostMapping
    @PreAuthorize("hasAuthority('member:add')")
    public Result<Boolean> add(@Validated(MemberDTO.Add.class) @RequestBody MemberDTO dto) {
        Member member = new Member();
        BeanUtils.copyProperties(dto, member);
        boolean saved = memberService.save(member);
        return Result.success(saved);
    }

    @ApiOperation("编辑会员")
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('member:edit')")
    public Result<Boolean> update(@PathVariable Long id, @Validated(MemberDTO.Update.class) @RequestBody MemberDTO dto) {
        Member member = memberService.getById(id);
        if (member == null) {
            return Result.fail("会员不存在");
        }
        
        // 如果密码为空，保留原密码
        if (dto.getPassword() == null || dto.getPassword().isEmpty()) {
            dto.setPassword(member.getPassword());
        }
        
        BeanUtils.copyProperties(dto, member);
        member.setId(id);
        boolean updated = memberService.updateById(member);
        return Result.success(updated);
    }

    @ApiOperation("删除会员")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('member:delete')")
    public Result<Boolean> delete(@PathVariable Long id) {
        boolean removed = memberService.removeById(id);
        return Result.success(removed);
    }

    @ApiOperation("会员详情")
    @GetMapping("/{id}")
    public Result<MemberVO> detail(@PathVariable Long id) {
        Member member = memberService.getById(id);
        if (member == null) return Result.success(null);
        MemberVO vo = new MemberVO();
        BeanUtils.copyProperties(member, vo);
        return Result.success(vo);
    }
} 