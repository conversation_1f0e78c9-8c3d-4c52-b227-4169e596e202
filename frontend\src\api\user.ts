import request from '../utils/request';
import type { Role } from './role'; // 导入 Role 类型
import type { Menu } from './menu'; // 导入 Menu 类型

export interface User {
  id?: number;
  username: string;
  password?: string;
  nickname?: string;
  phone?: string;
  email?: string;
  status: number;
  createTime?: string;
  updateTime?: string;
}

export interface UserPageParams {
  pageNum: number;
  pageSize: number;
  username?: string;
}

// 密码修改参数
export interface PasswordUpdateParams {
  oldPassword: string;
  newPassword: string;
}

// 后端Result统一返回结构
interface Result<T> {
  code: number;
  msg: string;
  data: T;
}

// TODO: 补充后端返回的用户权限类型定义（如果用户菜单接口不返回权限）
export interface UserInfo {
  permissions: string[]; // 用户权限列表
  // menus: any[]; // 如果用户信息接口也返回菜单，这里保留
}

// 分页查询用户
// request工具函数会返回Result对象，我们需要从data中提取分页数据
export async function getUserPage(params: UserPageParams): Promise<Result<{ records: User[]; total: number }>> {
  return request({
    url: '/user/page',
    method: 'get',
    params
  });
}

// 新增用户
// request工具函数会返回Result对象，我们可能只需要判断code或获取data中的boolean/id
export async function addUser(data: User): Promise<Result<number>> { // 修改返回类型为Result<number>，表示返回用户ID
  return request({
    url: '/user',
    method: 'post',
    data
  });
}

// 编辑用户
// request工具函数会返回Result对象，我们可能只需要判断code或获取data中的boolean
export async function updateUser(id: number, data: User): Promise<Result<any>> { // 假设后端data字段返回boolean
  return request({
    url: `/user/${id}`,
    method: 'put',
    data
  });
}

// 删除用户
// request工具函数会返回Result对象，我们可能只需要判断code或获取data中的boolean
export async function deleteUser(id: number): Promise<Result<any>> { // 假设后端data字段返回boolean
  return request({
    url: `/user/${id}`,
    method: 'delete'
  });
}

// 根据用户ID获取角色ID列表
// request工具函数会返回Result对象，我们需要从data中提取number[]
export async function getRoleIdsByUserId(userId: number): Promise<Result<number[]>> { // 假设后端data字段返回number[]
  return request({
    url: `/user-role/roleIds/${userId}`,
    method: 'get',
  });
}

// 保存用户角色关联
// request工具函数会返回Result对象，我们可能只需要判断code或获取data中的boolean
export async function saveUserRoles(userId: number, roleIds: number[]): Promise<Result<any>> { // 假设后端data字段返回boolean
   return request({
      url: `/user-role/save/${userId}`,
      method: 'post',
      data: roleIds
    });
}

// 假设后端有一个接口 /user/menus 用于获取当前用户的菜单树
// request工具函数会返回Result对象，我们需要从data中提取菜单树数据
export async function getUserMenus(): Promise<Result<Menu[]>> { 
  return request({
    url: '/user/menus', // 假设的后端接口地址
    method: 'get',
  });
}

export function getUserDetail(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.get(`/user/${id}`);
}

// 获取当前登录用户信息
export async function getCurrentUserInfo(): Promise<Result<User>> {
  return request({
    url: '/user/current',
    method: 'get'
  });
}

// 获取当前登录用户的角色
export async function getCurrentUserRoles(): Promise<Result<Role[]>> {
  return request({
    url: '/user/current/roles',
    method: 'get'
  });
}

// 修改密码
export async function updatePassword(data: PasswordUpdateParams): Promise<Result<any>> {
  // 获取当前用户ID
  return request({
    url: '/user/updatePassword',  // 修改为正确的API路径，基于后端实际接口
    method: 'post',  // 修改为post方法，通常密码更新使用post
    data
  });
} 