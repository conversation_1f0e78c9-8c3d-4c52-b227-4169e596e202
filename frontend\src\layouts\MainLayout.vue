<template>
  <el-container class="main-layout">
    <el-aside width="200px" class="sidebar-container">
      <!-- 侧边栏菜单 -->
      <el-menu
        :default-active="activeMenu"
        class="el-menu-vertical-demo"
        router
        :collapse="false"
        :unique-opened="true"
      >
        <!-- 使用递归组件渲染菜单项 -->
        <template v-for="menu in menuTree" :key="menu.id">
          <menu-item :menu="menu" />
        </template>
      </el-menu>
    </el-aside>
    <el-container>
      <el-header class="header-container">
        <!-- 顶部区域，例如面包屑或用户信息 -->
        <div class="header-title">系统后台管理系统</div>
        <div class="user-info">
          <el-dropdown trigger="click">
            <span class="user-dropdown-link">
              <el-avatar :size="32" class="user-avatar">
                {{ userInfo.nickname ? userInfo.nickname.substring(0, 1).toUpperCase() : 'U' }}
              </el-avatar>
              <span class="user-nickname" @click="goToMyAccount">{{ userInfo.nickname || userInfo.username }}</span>
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="goToMyAccount">
                  <el-icon><user /></el-icon>
                  <span>个人账号</span>
                </el-dropdown-item>
                <el-dropdown-item divided @click="handleLogout">
                  <el-icon><switch-button /></el-icon>
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      <el-main class="main-content">
        <!-- 路由视图，显示具体的功能页面 -->
        <router-view v-slot="{ Component }">
          <keep-alive>
            <component :is="Component" />
          </keep-alive>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router';
import { useMenuStore } from '../store/menu';
import { computed, onMounted, ref } from 'vue';
import { usePermissionStore } from '../store/permission';
import MenuItem from '../components/MenuItem.vue';
import { getCurrentUserInfo } from '../api/user';
import type { User } from '../api/user';
import { ElMessage } from 'element-plus';
// 图标已全局注册;

const router = useRouter();
const route = useRoute();
const menuStore = useMenuStore();
const permissionStore = usePermissionStore();
const menuTree = computed(() => menuStore.menuTree);

// 用户信息
const userInfo = ref<User>({
  username: '',
  status: 0
});

// 当前激活的菜单项
const activeMenu = computed(() => {
  //console.log('当前路径:', route.path);
  return route.path;
});

// 获取当前用户信息
const fetchUserInfo = async () => {
  try {
    const res = await getCurrentUserInfo();
    if (res && res.code === 0 && res.data) {
      userInfo.value = res.data;
    } else {
      console.error('获取用户信息失败:', res?.msg);
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

// 跳转到个人账号页面
const goToMyAccount = () => {
  router.push('/my');
};

onMounted(async () => {
  //console.log('主布局已加载，菜单树:', menuTree.value);
  //console.log('当前路由:', route.path);
  //console.log('所有权限:', permissionStore.permissions);
  
  // 加载用户信息
  await fetchUserInfo();
});

const handleLogout = () => {
  localStorage.removeItem('token');
  menuStore.setMenuTree([]);
  permissionStore.clearPermissions();
  ElMessage.success('已退出登录');
  router.push('/login');
};
</script>

<style>
/* 全局样式，覆盖Element Plus弹出菜单的默认样式 */
.el-menu--vertical {
  background-color: #2b3544 !important;
}

.el-menu--vertical .el-menu-item {
  background-color: #2b3544 !important;
  color: #b3bcc5 !important;
}

.el-menu--vertical .el-menu-item:hover {
  background-color: #3a4759 !important;
  color: #ffffff !important;
}

.el-menu--vertical .el-menu-item.is-active {
  color: #409EFF !important;
  background-color: #3a4759 !important;
}
</style>

<style scoped>
/* 保留原有的scoped样式 */
.main-layout {
  height: 100vh;
}

.sidebar-container {
  background-color: #2b3544; /* Darker background */
}

.el-menu-vertical-demo {
  background-color: #2b3544; /* Darker background */
  border-right: none;
}

.el-menu-vertical-demo .el-menu-item,
.el-menu-vertical-demo :deep(.el-sub-menu__title) {
  color: #b3bcc5; /* Light gray text */
}

.el-menu-vertical-demo .el-menu-item:hover,
.el-menu-vertical-demo :deep(.el-sub-menu__title:hover) {
  background-color: #3a4759; /* Slightly lighter background on hover */
  color: #ffffff; /* White text on hover */
}

.el-menu-vertical-demo .el-menu-item.is-active,
.el-menu-vertical-demo .is-active > .el-sub-menu__title {
  color: #409EFF; /* Element Plus primary blue */
  background-color: #3a4759; /* Keep hover background or choose a different active background */
}

/* Style for nested menu items in sub-menus */
.el-menu-vertical-demo .el-menu {
  background-color: #2b3544; /* Match parent background for consistency */
}

.el-menu-vertical-demo .el-menu .el-menu-item {
  color: #b3bcc5; /* Light gray text for sub-menu items */
  background-color: #2b3544; /* 与父菜单背景色一致 */
}

.el-menu-vertical-demo .el-menu .el-menu-item:hover {
  background-color: #3a4759; /* Hover background for sub-menu items */
  color: #ffffff; /* White text on hover */
}

.el-menu-vertical-demo .el-menu .el-menu-item.is-active {
  color: #409EFF; /* Active text color for sub-menu items */
  background-color: #3a4759; /* Active background for sub-menu items */
}

/* 确保子菜单的弹出框也使用深色背景 */
.el-menu-vertical-demo :deep(.el-menu--popup) {
  background-color: #2b3544;
  border: none;
}

/* Adjust icon color */
.el-menu-vertical-demo .el-icon {
  color: inherit; /* Inherit color from parent menu item */
}

.header-container {
  background-color: #ffffff;
  color: #333;
  line-height: 60px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-dropdown-link {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-avatar {
  background-color: #409EFF;
  color: #fff;
  margin-right: 8px;
}

.user-nickname {
  margin-right: 5px;
  font-size: 14px;
  color: #333;
}

.user-nickname:hover {
  color: #409EFF;
}

.main-content {
  background-color: #f0f2f5;
  padding: 0px;
}
</style>