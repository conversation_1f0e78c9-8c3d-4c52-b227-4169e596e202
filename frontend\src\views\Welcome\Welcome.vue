<template>
  <div class="page-container welcome-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="welcome-header">
          <h1>欢迎使用企业级管理系统</h1>
          <div class="time-display">
            <el-icon><Clock /></el-icon>
            <span>{{ currentTime }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <el-col :span="24">
        <el-card class="system-intro">
          <template #header>
            <div class="card-header">
              <h2><el-icon><InfoFilled /></el-icon> 系统介绍</h2>
            </div>
          </template>
          <div class="intro-content">
            <p>本系统是一个基于微服务架构的企业级管理平台，提供完善的权限管理、用户管理、角色管理等核心功能。系统采用前后端分离设计，具有高可用性、可扩展性和安全性。</p>
            <p>主要功能模块包括：</p>
            <el-row :gutter="20" class="feature-list">
              <el-col :span="8" v-for="(feature, index) in features" :key="index">
                <div class="feature-item">
                  <el-icon>
                    <component :is="feature.icon" />
                  </el-icon>
                  <div class="feature-info">
                    <h3>{{ feature.title }}</h3>
                    <p>{{ feature.desc }}</p>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
      
      <!-- <el-col :span="8">
        <el-card class="quick-stats">
          <template #header>
            <div class="card-header">
              <h2><el-icon><DataAnalysis /></el-icon> 系统概览</h2>
            </div>
          </template>
          <div class="stats-content">
            <div class="stat-item" v-for="(stat, index) in quickStats" :key="index">
              <el-icon>
                <component :is="stat.icon" />
              </el-icon>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="user-info mt-20">
          <template #header>
            <div class="card-header">
              <h2><el-icon><User /></el-icon> 用户信息</h2>
            </div>
          </template>
          <div class="user-content">
            <div class="user-avatar">
              <el-avatar :size="64" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
            </div>
            <div class="user-details">
              <p><strong>用户名：</strong>{{ username }}</p>
              <p><strong>上次登录：</strong>{{ lastLogin }}</p>
              <p><strong>登录IP：</strong>{{ loginIp }}</p>
            </div>
          </div>
        </el-card>
      </el-col> -->
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h2><el-icon><Setting /></el-icon> 技术架构</h2>
            </div>
          </template>
          <div class="tech-stack">
            <el-tabs>
              <el-tab-pane label="后端技术">
                <el-descriptions :column="3" border>
                  <el-descriptions-item label="核心框架">Spring Boot 2.7.x</el-descriptions-item>
                  <el-descriptions-item label="微服务框架">Spring Cloud 2021.0.8</el-descriptions-item>
                  <el-descriptions-item label="服务治理">Spring Cloud Alibaba 2021.0.5.0</el-descriptions-item>
                  <el-descriptions-item label="ORM框架">MyBatis 3.5.13 / MyBatis-Plus 4.2.0</el-descriptions-item>
                  <el-descriptions-item label="服务注册与配置">Nacos</el-descriptions-item>
                  <el-descriptions-item label="服务限流与熔断">Sentinel</el-descriptions-item>
                  <el-descriptions-item label="API网关">Spring Cloud Gateway</el-descriptions-item>
                  <el-descriptions-item label="数据库">MySQL / Redis</el-descriptions-item>
                  <el-descriptions-item label="安全框架">Spring Security</el-descriptions-item>
                </el-descriptions>
              </el-tab-pane>
              <el-tab-pane label="前端技术">
                <el-descriptions :column="3" border>
                  <el-descriptions-item label="核心框架">Vue 3</el-descriptions-item>
                  <el-descriptions-item label="类型系统">TypeScript</el-descriptions-item>
                  <el-descriptions-item label="UI组件库">Element Plus</el-descriptions-item>
                  <el-descriptions-item label="状态管理">Pinia</el-descriptions-item>
                  <el-descriptions-item label="路由">Vue Router</el-descriptions-item>
                  <el-descriptions-item label="HTTP客户端">Axios</el-descriptions-item>
                  <el-descriptions-item label="构建工具">Vite</el-descriptions-item>
                  <el-descriptions-item label="代码规范">ESLint</el-descriptions-item>
                  <el-descriptions-item label="CSS预处理">SCSS</el-descriptions-item>
                </el-descriptions>
              </el-tab-pane>
              <el-tab-pane label="系统架构">
                <div class="architecture-diagram">
                  <img src="https://cdn.jsdelivr.net/gh/apache/incubator-seata@develop/docs/img/architecture/Seata-Framework.png" alt="系统架构图">
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h2><el-icon><Document /></el-icon> 使用指南</h2>
            </div>
          </template>
          <div class="usage-guide">
            <el-steps :active="1" finish-status="success" simple>
              <el-step title="登录系统" description="使用管理员账号登录系统" />
              <el-step title="权限配置" description="配置用户角色和权限" />
              <el-step title="业务管理" description="进行日常业务操作" />
              <el-step title="数据分析" description="查看统计报表和分析" />
            </el-steps>
            
            <div class="guide-content mt-20">
              <p>1. <strong>用户管理</strong>：系统管理员可以创建用户、分配角色，管理用户权限</p>
              <p>2. <strong>角色管理</strong>：创建不同角色，为角色分配菜单权限</p>
              <p>3. <strong>菜单管理</strong>：自定义系统菜单，配置功能权限</p>
              <p>4. <strong>业务功能</strong>：根据权限使用系统提供的各项业务功能</p>
              <p>5. <strong>数据导出</strong>：支持Excel导出，方便数据处理和分析</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row class="mt-20">
      <el-col :span="24">
        <div class="footer">
          © {{ new Date().getFullYear() }} 企业级管理系统 | 技术支持：开发团队
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

const currentTime = ref('');
const username = ref('管理员');
const lastLogin = ref('2023-06-15 08:30:22');
const loginIp = ref('*************');

// 系统功能特点
const features = [
  { 
    icon: 'Menu', 
    title: '菜单管理', 
    desc: '灵活配置系统菜单和权限' 
  },
  { 
    icon: 'UserFilled', 
    title: '角色管理', 
    desc: '细粒度的角色权限控制' 
  },
  { 
    icon: 'Lock', 
    title: '安全机制', 
    desc: 'JWT认证和授权' 
  },
  { 
    icon: 'Setting', 
    title: '系统配置', 
    desc: '个性化系统参数设置' 
  },
  { 
    icon: 'Histogram', 
    title: '数据统计', 
    desc: '多维度数据分析报表' 
  },
  { 
    icon: 'Message', 
    title: '消息通知', 
    desc: '实时系统消息推送' 
  }
];

// 快速统计数据
const quickStats = [
  { icon: 'User', label: '系统用户', value: '128' },
  { icon: 'Menu', label: '系统菜单', value: '56' },
  { icon: 'UserFilled', label: '角色数量', value: '12' },
  { icon: 'Lock', label: '在线用户', value: '36' }
];

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

let timer: number;

onMounted(() => {
  updateTime();
  timer = window.setInterval(updateTime, 1000);
});

onUnmounted(() => {
  clearInterval(timer);
});
</script>

<style scoped>
.welcome-container {
  padding: 20px;
}

.welcome-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.welcome-header h1 {
  margin: 0;
  color: #409EFF;
  font-size: 24px;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #606266;
}

.mt-20 {
  margin-top: 20px;
}

.card-header {
  display: flex;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.intro-content p {
  line-height: 1.6;
  color: #606266;
}

.feature-list {
  margin-top: 20px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 4px;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.feature-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.feature-item .el-icon {
  font-size: 24px;
  color: #409EFF;
}

.feature-info h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #303133;
}

.feature-info p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.stats-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.stat-item .el-icon {
  font-size: 24px;
  color: #409EFF;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.user-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-details p {
  margin: 10px 0;
  color: #606266;
}

.architecture-diagram {
  text-align: center;
  margin: 20px 0;
}

.architecture-diagram img {
  max-width: 100%;
  height: auto;
}

.guide-content p {
  line-height: 1.8;
  color: #606266;
}

.footer {
  text-align: center;
  padding: 20px 0;
  color: #909399;
  font-size: 14px;
}
</style>