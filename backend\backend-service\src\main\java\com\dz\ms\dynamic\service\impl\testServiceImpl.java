package com.dz.ms.dynamic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.ms.dynamic.entity.test;
import com.dz.ms.dynamic.mapper.testMapper;
import com.dz.ms.dynamic.service.testService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * test Service实现类
 * 由低代码平台自动生成
 */
@Service
public class testServiceImpl extends ServiceImpl<testMapper, test> implements testService {

    @Override
    public IPage<test> page(Integer page, Integer size, String keyword) {
        Page<test> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<test> wrapper = new LambdaQueryWrapper<>();
        
        // TODO: 根据实际业务修改查询条件
        
        // 按更新时间倒序排序
        wrapper.orderByDesc(test::getUpdateTime);
        
        return page(pageParam, wrapper);
    }
}