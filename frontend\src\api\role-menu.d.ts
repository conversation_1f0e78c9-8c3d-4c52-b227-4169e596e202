/**
 * 根据角色ID获取关联的菜单ID列表
 * @param roleId 角色ID
 * @returns 菜单ID列表的Promise
 */
export declare function getRoleMenuIds(roleId: number | string): Promise<import("axios").AxiosResponse<any, any>>;
/**
 * 保存角色和菜单的关联关系
 * @param roleId 角色ID
 * @param menuIds 菜单ID列表
 * @returns 是否成功的Promise
 */
export declare function saveRoleMenus(roleId: number | string, menuIds: number[]): Promise<import("axios").AxiosResponse<any, any>>;
