package com.dz.ms.lowcode.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.ms.lowcode.dto.MetaEntityDTO;
import com.dz.ms.lowcode.dto.MetaFieldDTO;
import com.dz.ms.lowcode.entity.MetaEntity;
import com.dz.ms.lowcode.entity.MetaField;
import com.dz.ms.lowcode.mapper.MetaEntityMapper;
import com.dz.ms.lowcode.service.MetaEntityService;
import com.dz.ms.lowcode.service.MetaFieldService;
import com.dz.ms.lowcode.service.generator.CodeGeneratorService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 元数据实体Service实现类
 * <AUTHOR>
 * @date 2023-07-01
 */
@Service
public class MetaEntityServiceImpl extends ServiceImpl<MetaEntityMapper, MetaEntity> implements MetaEntityService {

    @Autowired
    private MetaFieldService metaFieldService;
    
    @Autowired(required = false)
    private CodeGeneratorService codeGeneratorService;

    @Override
    public IPage<MetaEntity> page(Integer page, Integer size, String keyword) {
        Page<MetaEntity> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<MetaEntity> wrapper = new LambdaQueryWrapper<>();
        
        // 如果有关键字，则根据实体编码或实体名称模糊查询
        if (StringUtils.hasText(keyword)) {
            wrapper.like(MetaEntity::getEntityCode, keyword)
                   .or()
                   .like(MetaEntity::getEntityName, keyword);
        }
        
        // 按更新时间倒序排序
        wrapper.orderByDesc(MetaEntity::getUpdateTime);
        
        return page(pageParam, wrapper);
    }

    @Override
    public MetaEntityDTO getEntityById(Long id) {
        MetaEntity entity = getById(id);
        if (entity == null) {
            return null;
        }
        
        // 转换为DTO
        MetaEntityDTO entityDTO = new MetaEntityDTO();
        BeanUtils.copyProperties(entity, entityDTO);
        
        // 查询字段列表
        List<MetaField> fields = metaFieldService.getFieldsByEntityId(id);
        if (fields != null && !fields.isEmpty()) {
            List<MetaFieldDTO> fieldDTOs = fields.stream().map(field -> {
                MetaFieldDTO fieldDTO = new MetaFieldDTO();
                BeanUtils.copyProperties(field, fieldDTO);
                return fieldDTO;
            }).collect(Collectors.toList());
            entityDTO.setFields(fieldDTOs);
        }
        
        return entityDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveEntity(MetaEntityDTO entityDTO) {
        // 转换为实体对象
        MetaEntity entity = new MetaEntity();
        BeanUtils.copyProperties(entityDTO, entity);
        
        // 设置默认值
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setPublished(false);
        entity.setEnabled(true);
        
        // 如果表名为空，则默认使用实体编码作为表名
        if (!StringUtils.hasText(entity.getTableName())) {
            entity.setTableName("lc_" + entity.getEntityCode().toLowerCase());
        }
        
        // 保存实体
        save(entity);
        
        // 保存字段
        List<MetaFieldDTO> fieldDTOs = entityDTO.getFields();
        if (fieldDTOs != null && !fieldDTOs.isEmpty()) {
            List<MetaField> fields = fieldDTOs.stream().map(fieldDTO -> {
                MetaField field = new MetaField();
                BeanUtils.copyProperties(fieldDTO, field);
                
                // 如果列名为空，则默认使用字段编码作为列名
                if (!StringUtils.hasText(field.getColumnName())) {
                    field.setColumnName(field.getFieldCode().toLowerCase());
                }
                
                return field;
            }).collect(Collectors.toList());
            
            metaFieldService.saveFields(fields, entity.getId());
        }
        
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEntity(MetaEntityDTO entityDTO) {
        // 判断实体是否存在
        MetaEntity existEntity = getById(entityDTO.getId());
        if (existEntity == null) {
            return false;
        }
        
        // 如果实体已发布，则不能修改
        if (existEntity.getPublished()) {
            throw new RuntimeException("实体已发布，不能修改");
        }
        
        // 转换为实体对象
        MetaEntity entity = new MetaEntity();
        BeanUtils.copyProperties(entityDTO, entity);
        
        // 设置更新时间
        entity.setUpdateTime(new Date());
        
        // 更新实体
        boolean result = updateById(entity);
        
        // 更新字段
        List<MetaFieldDTO> fieldDTOs = entityDTO.getFields();
        if (fieldDTOs != null) {
            List<MetaField> fields = fieldDTOs.stream().map(fieldDTO -> {
                MetaField field = new MetaField();
                BeanUtils.copyProperties(fieldDTO, field);
                
                // 如果列名为空，则默认使用字段编码作为列名
                if (!StringUtils.hasText(field.getColumnName())) {
                    field.setColumnName(field.getFieldCode().toLowerCase());
                }
                
                return field;
            }).collect(Collectors.toList());
            
            metaFieldService.updateFields(fields, entity.getId());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteEntity(Long id) {
        // 判断实体是否存在
        MetaEntity entity = getById(id);
        if (entity == null) {
            return false;
        }
        
        // 如果实体已发布，则不能删除
        if (entity.getPublished()) {
            throw new RuntimeException("实体已发布，不能删除");
        }
        
        // 删除字段
        metaFieldService.deleteByEntityId(id);
        
        // 删除实体
        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publishEntity(Long id) {
        // 判断实体是否存在
        MetaEntity entity = getById(id);
        if (entity == null) {
            return false;
        }
        
        // 如果实体已发布，则直接返回成功
        if (entity.getPublished()) {
            return true;
        }
        
        // 查询字段列表
        List<MetaField> fields = metaFieldService.getFieldsByEntityId(id);
        if (fields == null || fields.isEmpty()) {
            throw new RuntimeException("实体没有字段，不能发布");
        }
        
        // 生成数据库表和代码
        if (codeGeneratorService != null) {
            codeGeneratorService.generateCode(entity, fields);
        }
        
        // 更新状态为已发布
        entity.setPublished(true);
        entity.setUpdateTime(new Date());
        
        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unpublishEntity(Long id) {
        // 判断实体是否存在
        MetaEntity entity = getById(id);
        if (entity == null) {
            return false;
        }
        
        // 如果实体未发布，则直接返回成功
        if (!entity.getPublished()) {
            return true;
        }
        
        // 更新状态为未发布
        entity.setPublished(false);
        entity.setUpdateTime(new Date());
        
        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEntityFields(Long entityId, List<MetaFieldDTO> fields) {
        if (fields != null) {
            List<MetaField> metaFields = fields.stream().map(dto -> {
                MetaField f = new MetaField();
                org.springframework.beans.BeanUtils.copyProperties(dto, f);
                return f;
            }).collect(java.util.stream.Collectors.toList());
            metaFieldService.updateFields(metaFields, entityId);
        }
    }
} 