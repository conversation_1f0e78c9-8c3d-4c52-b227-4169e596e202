import { defineStore } from 'pinia';
export const usePermissionStore = defineStore('permission', {
    state: () => ({
        permissions: [],
        roles: []
    }),
    getters: {
        // 检查是否有某个权限
        hasPermission: (state) => (permission) => {
            return state.permissions.includes(permission);
        },
        // 检查是否有某个角色
        hasRole: (state) => (role) => {
            return state.roles.includes(role);
        }
    },
    actions: {
        // 设置权限
        setPermissions(permissions) {
            this.permissions = permissions;
        },
        // 设置角色
        setRoles(roles) {
            this.roles = roles;
        },
        // 清除权限
        clearPermissions() {
            this.permissions = [];
            this.roles = [];
        }
    }
});
