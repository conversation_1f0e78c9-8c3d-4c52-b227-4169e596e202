/// <reference types="../../../../node_modules/.vue-global-types/vue_3.5_0_0_0.d.ts" />
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getstaffList, getstaffDetail, savestaff, updatestaff, deletestaff, batchDeletestaff } from '../../../api/staff';
// 状态定义
const searchForm = reactive({});
const tableData = ref([]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
const showBatchDelete = ref(false);
const selectedRows = ref([]);
// 表单相关
const dialogVisible = ref(false);
const dialogTitle = ref('');
const form = reactive({});
const formRef = ref();
// 表单验证规则
const rules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
};
// 获取字段标签
function getFieldLabel(field) {
    // 将字段名转换为更友好的标签
    // 例如：user_name -> 用户名
    const labelMap = {
    // 可以在这里添加字段名到标签的映射
    // 例如：'user_name': '用户名'
    };
    // 如果有映射，使用映射的标签
    if (labelMap[field]) {
        return labelMap[field];
    }
    // 否则，将字段名转换为标题格式
    return field.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
}
// 获取数据
function fetchData() {
    getstaffList({
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        ...searchForm
    }).then(res => {
        if (res.code === 0) {
            const { records, total: t } = res.data;
            tableData.value = records;
            total.value = t;
        }
        else {
            ElMessage.error(res.msg || '获取数据失败');
            tableData.value = [];
            total.value = 0;
        }
    }).catch(error => {
        console.error("获取staff列表失败:", error);
        tableData.value = [];
        total.value = 0;
    });
}
// 重置搜索
function resetSearch() {
    Object.keys(searchForm).forEach(key => {
        searchForm[key] = undefined;
    });
    pageNum.value = 1;
    fetchData();
}
// 表格选择改变
function handleSelectionChange(rows) {
    selectedRows.value = rows;
    showBatchDelete.value = rows.length > 0;
}
// 初始化表单字段
function initFormFields() {
    // 确保表单包含验证规则中的所有字段
    Object.keys(rules).forEach(field => {
        if (!form.hasOwnProperty(field)) {
            form[field] = '';
        }
    });
}
// 打开新增对话框
function openAdd() {
    dialogTitle.value = '新增staff';
    // 重置表单 - 创建一个新对象而不是修改现有对象
    const newForm = {};
    // 设置ID为undefined
    newForm.id = undefined;
    // 确保所有验证规则中的字段都被初始化
    Object.keys(rules).forEach(field => {
        if (!newForm.hasOwnProperty(field)) {
            newForm[field] = '';
        }
    });
    // 用新对象替换表单
    Object.keys(form).forEach(key => delete form[key]);
    Object.assign(form, newForm);
    dialogVisible.value = true;
}
// 打开编辑对话框
function openEdit(row) {
    dialogTitle.value = '编辑staff';
    getstaffDetail(row.id).then(res => {
        if (res.code === 0) {
            // 重置表单 - 创建一个新对象而不是修改现有对象
            const newForm = {};
            // 用API返回的数据填充表单
            Object.assign(newForm, res.data);
            // 确保所有验证规则中的字段都被初始化
            Object.keys(rules).forEach(field => {
                if (!newForm.hasOwnProperty(field)) {
                    newForm[field] = '';
                }
            });
            // 用新对象替换表单
            Object.keys(form).forEach(key => delete form[key]);
            Object.assign(form, newForm);
            dialogVisible.value = true;
        }
        else {
            ElMessage.error(res.msg || '获取详情失败');
        }
    }).catch(error => {
        console.error("获取staff详情失败:", error);
        ElMessage.error('获取详情失败');
    });
}
// 提交表单
async function handleSubmit() {
    formRef.value?.validate(async (valid) => {
        if (!valid)
            return;
        try {
            let res;
            if (!form.id) {
                res = await savestaff(form);
            }
            else {
                res = await updatestaff(form.id, form);
            }
            if (res.code === 0) {
                ElMessage.success(form.id ? '编辑成功' : '新增成功');
                dialogVisible.value = false;
                fetchData();
            }
            else {
                ElMessage.error(res.msg || '操作失败');
            }
        }
        catch (error) {
            console.error("提交staff信息失败:", error);
            ElMessage.error('操作失败');
        }
    });
}
// 删除
async function handleDelete(id) {
    if (!id)
        return;
    ElMessageBox.confirm('确定要删除该staff吗？此操作不可恢复', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
        try {
            const res = await deletestaff(id);
            if (res.code === 0) {
                ElMessage.success('删除成功');
                fetchData();
            }
            else {
                ElMessage.error(res.msg || '删除失败');
            }
        }
        catch (error) {
            console.error("删除staff失败:", error);
            ElMessage.error('删除失败');
        }
    })
        .catch(() => {
        // 用户取消删除操作
        ElMessage.info('已取消删除');
    });
}
// 批量删除
async function handleBatchDelete() {
    if (!selectedRows.value.length) {
        ElMessage.warning('请选择要删除的记录');
        return;
    }
    ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条记录吗？此操作不可恢复`, '批量删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
        try {
            const ids = selectedRows.value.map(row => row.id);
            const res = await batchDeletestaff(ids);
            if (res.code === 0) {
                ElMessage.success('批量删除成功');
                fetchData();
            }
            else {
                ElMessage.error(res.msg || '批量删除失败');
            }
        }
        catch (error) {
            console.error("批量删除staff失败:", error);
            ElMessage.error('批量删除失败');
        }
    })
        .catch(() => {
        // 用户取消删除操作
        ElMessage.info('已取消删除');
    });
}
// 初始化
onMounted(() => {
    // 确保所有验证规则中的字段都被初始化
    initFormFields();
    fetchData();
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "page-container" },
});
const __VLS_0 = {}.ElCard;
/** @type {[typeof __VLS_components.ElCard, typeof __VLS_components.elCard, typeof __VLS_components.ElCard, typeof __VLS_components.elCard, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({}));
const __VLS_2 = __VLS_1({}, ...__VLS_functionalComponentArgsRest(__VLS_1));
__VLS_3.slots.default;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ style: {} },
});
const __VLS_4 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
    ...{ 'onClick': {} },
    type: "primary",
}));
const __VLS_6 = __VLS_5({
    ...{ 'onClick': {} },
    type: "primary",
}, ...__VLS_functionalComponentArgsRest(__VLS_5));
let __VLS_8;
let __VLS_9;
let __VLS_10;
const __VLS_11 = {
    onClick: (__VLS_ctx.fetchData)
};
__VLS_7.slots.default;
var __VLS_7;
const __VLS_12 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_13 = __VLS_asFunctionalComponent(__VLS_12, new __VLS_12({
    ...{ 'onClick': {} },
}));
const __VLS_14 = __VLS_13({
    ...{ 'onClick': {} },
}, ...__VLS_functionalComponentArgsRest(__VLS_13));
let __VLS_16;
let __VLS_17;
let __VLS_18;
const __VLS_19 = {
    onClick: (__VLS_ctx.resetSearch)
};
__VLS_15.slots.default;
var __VLS_15;
const __VLS_20 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({
    ...{ 'onClick': {} },
    type: "success",
}));
const __VLS_22 = __VLS_21({
    ...{ 'onClick': {} },
    type: "success",
}, ...__VLS_functionalComponentArgsRest(__VLS_21));
let __VLS_24;
let __VLS_25;
let __VLS_26;
const __VLS_27 = {
    onClick: (__VLS_ctx.openAdd)
};
__VLS_23.slots.default;
var __VLS_23;
const __VLS_28 = {}.ElTable;
/** @type {[typeof __VLS_components.ElTable, typeof __VLS_components.elTable, typeof __VLS_components.ElTable, typeof __VLS_components.elTable, ]} */ ;
// @ts-ignore
const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
    ...{ 'onSelectionChange': {} },
    data: (__VLS_ctx.tableData),
    ...{ style: {} },
}));
const __VLS_30 = __VLS_29({
    ...{ 'onSelectionChange': {} },
    data: (__VLS_ctx.tableData),
    ...{ style: {} },
}, ...__VLS_functionalComponentArgsRest(__VLS_29));
let __VLS_32;
let __VLS_33;
let __VLS_34;
const __VLS_35 = {
    onSelectionChange: (__VLS_ctx.handleSelectionChange)
};
__VLS_31.slots.default;
const __VLS_36 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_37 = __VLS_asFunctionalComponent(__VLS_36, new __VLS_36({
    type: "selection",
    width: "55",
}));
const __VLS_38 = __VLS_37({
    type: "selection",
    width: "55",
}, ...__VLS_functionalComponentArgsRest(__VLS_37));
const __VLS_40 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({
    prop: "name",
    label: "姓名",
    width: "50px",
}));
const __VLS_42 = __VLS_41({
    prop: "name",
    label: "姓名",
    width: "50px",
}, ...__VLS_functionalComponentArgsRest(__VLS_41));
const __VLS_44 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_45 = __VLS_asFunctionalComponent(__VLS_44, new __VLS_44({
    prop: "email",
    label: "邮箱",
    width: "255px",
}));
const __VLS_46 = __VLS_45({
    prop: "email",
    label: "邮箱",
    width: "255px",
}, ...__VLS_functionalComponentArgsRest(__VLS_45));
const __VLS_48 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_49 = __VLS_asFunctionalComponent(__VLS_48, new __VLS_48({
    label: "操作",
    width: "180",
}));
const __VLS_50 = __VLS_49({
    label: "操作",
    width: "180",
}, ...__VLS_functionalComponentArgsRest(__VLS_49));
__VLS_51.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_51.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_52 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_53 = __VLS_asFunctionalComponent(__VLS_52, new __VLS_52({
        ...{ 'onClick': {} },
        size: "small",
    }));
    const __VLS_54 = __VLS_53({
        ...{ 'onClick': {} },
        size: "small",
    }, ...__VLS_functionalComponentArgsRest(__VLS_53));
    let __VLS_56;
    let __VLS_57;
    let __VLS_58;
    const __VLS_59 = {
        onClick: (...[$event]) => {
            __VLS_ctx.openEdit(scope.row);
        }
    };
    __VLS_55.slots.default;
    var __VLS_55;
    const __VLS_60 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_61 = __VLS_asFunctionalComponent(__VLS_60, new __VLS_60({
        ...{ 'onClick': {} },
        size: "small",
        type: "danger",
    }));
    const __VLS_62 = __VLS_61({
        ...{ 'onClick': {} },
        size: "small",
        type: "danger",
    }, ...__VLS_functionalComponentArgsRest(__VLS_61));
    let __VLS_64;
    let __VLS_65;
    let __VLS_66;
    const __VLS_67 = {
        onClick: (...[$event]) => {
            __VLS_ctx.handleDelete(scope.row.id);
        }
    };
    __VLS_63.slots.default;
    var __VLS_63;
}
var __VLS_51;
var __VLS_31;
if (__VLS_ctx.showBatchDelete) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ style: {} },
    });
    const __VLS_68 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_69 = __VLS_asFunctionalComponent(__VLS_68, new __VLS_68({
        ...{ 'onClick': {} },
        type: "danger",
    }));
    const __VLS_70 = __VLS_69({
        ...{ 'onClick': {} },
        type: "danger",
    }, ...__VLS_functionalComponentArgsRest(__VLS_69));
    let __VLS_72;
    let __VLS_73;
    let __VLS_74;
    const __VLS_75 = {
        onClick: (__VLS_ctx.handleBatchDelete)
    };
    __VLS_71.slots.default;
    var __VLS_71;
}
const __VLS_76 = {}.ElPagination;
/** @type {[typeof __VLS_components.ElPagination, typeof __VLS_components.elPagination, ]} */ ;
// @ts-ignore
const __VLS_77 = __VLS_asFunctionalComponent(__VLS_76, new __VLS_76({
    ...{ 'onSizeChange': {} },
    ...{ 'onCurrentChange': {} },
    currentPage: (__VLS_ctx.pageNum),
    pageSize: (__VLS_ctx.pageSize),
    total: (__VLS_ctx.total),
    pageSizes: ([10, 20, 50]),
    layout: "total, sizes, prev, pager, next, jumper",
    ...{ style: {} },
}));
const __VLS_78 = __VLS_77({
    ...{ 'onSizeChange': {} },
    ...{ 'onCurrentChange': {} },
    currentPage: (__VLS_ctx.pageNum),
    pageSize: (__VLS_ctx.pageSize),
    total: (__VLS_ctx.total),
    pageSizes: ([10, 20, 50]),
    layout: "total, sizes, prev, pager, next, jumper",
    ...{ style: {} },
}, ...__VLS_functionalComponentArgsRest(__VLS_77));
let __VLS_80;
let __VLS_81;
let __VLS_82;
const __VLS_83 = {
    onSizeChange: (__VLS_ctx.fetchData)
};
const __VLS_84 = {
    onCurrentChange: (__VLS_ctx.fetchData)
};
var __VLS_79;
var __VLS_3;
const __VLS_85 = {}.ElDialog;
/** @type {[typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, ]} */ ;
// @ts-ignore
const __VLS_86 = __VLS_asFunctionalComponent(__VLS_85, new __VLS_85({
    modelValue: (__VLS_ctx.dialogVisible),
    title: (__VLS_ctx.dialogTitle),
    width: "500px",
}));
const __VLS_87 = __VLS_86({
    modelValue: (__VLS_ctx.dialogVisible),
    title: (__VLS_ctx.dialogTitle),
    width: "500px",
}, ...__VLS_functionalComponentArgsRest(__VLS_86));
__VLS_88.slots.default;
const __VLS_89 = {}.ElForm;
/** @type {[typeof __VLS_components.ElForm, typeof __VLS_components.elForm, typeof __VLS_components.ElForm, typeof __VLS_components.elForm, ]} */ ;
// @ts-ignore
const __VLS_90 = __VLS_asFunctionalComponent(__VLS_89, new __VLS_89({
    model: (__VLS_ctx.form),
    rules: (__VLS_ctx.rules),
    ref: "formRef",
    labelWidth: "100px",
}));
const __VLS_91 = __VLS_90({
    model: (__VLS_ctx.form),
    rules: (__VLS_ctx.rules),
    ref: "formRef",
    labelWidth: "100px",
}, ...__VLS_functionalComponentArgsRest(__VLS_90));
/** @type {typeof __VLS_ctx.formRef} */ ;
var __VLS_93 = {};
__VLS_92.slots.default;
for (const [rule, field] of __VLS_getVForSourceType((__VLS_ctx.rules))) {
    (field);
    const __VLS_95 = {}.ElFormItem;
    /** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
    // @ts-ignore
    const __VLS_96 = __VLS_asFunctionalComponent(__VLS_95, new __VLS_95({
        label: (__VLS_ctx.getFieldLabel(field)),
        prop: (field),
    }));
    const __VLS_97 = __VLS_96({
        label: (__VLS_ctx.getFieldLabel(field)),
        prop: (field),
    }, ...__VLS_functionalComponentArgsRest(__VLS_96));
    __VLS_98.slots.default;
    const __VLS_99 = {}.ElInput;
    /** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
    // @ts-ignore
    const __VLS_100 = __VLS_asFunctionalComponent(__VLS_99, new __VLS_99({
        modelValue: (__VLS_ctx.form[field]),
    }));
    const __VLS_101 = __VLS_100({
        modelValue: (__VLS_ctx.form[field]),
    }, ...__VLS_functionalComponentArgsRest(__VLS_100));
    var __VLS_98;
}
var __VLS_92;
{
    const { footer: __VLS_thisSlot } = __VLS_88.slots;
    const __VLS_103 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_104 = __VLS_asFunctionalComponent(__VLS_103, new __VLS_103({
        ...{ 'onClick': {} },
    }));
    const __VLS_105 = __VLS_104({
        ...{ 'onClick': {} },
    }, ...__VLS_functionalComponentArgsRest(__VLS_104));
    let __VLS_107;
    let __VLS_108;
    let __VLS_109;
    const __VLS_110 = {
        onClick: (...[$event]) => {
            __VLS_ctx.dialogVisible = false;
        }
    };
    __VLS_106.slots.default;
    var __VLS_106;
    const __VLS_111 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_112 = __VLS_asFunctionalComponent(__VLS_111, new __VLS_111({
        ...{ 'onClick': {} },
        type: "primary",
    }));
    const __VLS_113 = __VLS_112({
        ...{ 'onClick': {} },
        type: "primary",
    }, ...__VLS_functionalComponentArgsRest(__VLS_112));
    let __VLS_115;
    let __VLS_116;
    let __VLS_117;
    const __VLS_118 = {
        onClick: (__VLS_ctx.handleSubmit)
    };
    __VLS_114.slots.default;
    var __VLS_114;
}
var __VLS_88;
/** @type {__VLS_StyleScopedClasses['page-container']} */ ;
// @ts-ignore
var __VLS_94 = __VLS_93;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            tableData: tableData,
            pageNum: pageNum,
            pageSize: pageSize,
            total: total,
            showBatchDelete: showBatchDelete,
            dialogVisible: dialogVisible,
            dialogTitle: dialogTitle,
            form: form,
            formRef: formRef,
            rules: rules,
            getFieldLabel: getFieldLabel,
            fetchData: fetchData,
            resetSearch: resetSearch,
            handleSelectionChange: handleSelectionChange,
            openAdd: openAdd,
            openEdit: openEdit,
            handleSubmit: handleSubmit,
            handleDelete: handleDelete,
            handleBatchDelete: handleBatchDelete,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
