import request from '../utils/request';

// 实体接口定义
export interface client {
  id?: number;
  createTime?: string;
  updateTime?: string;
}

// 分页查询参数接口
export interface clientPageQuery {
  pageNum?: number;
  pageSize?: number;
}

// 获取分页列表
export function getclientList(params: clientPageQuery) {
  if (!params.pageNum) params.pageNum = 1;
  if (!params.pageSize) params.pageSize = 10;
  return request.get('/api/client/page', { params });
}

// 获取所有数据
export function getAllclients() {
  return request.get('/api/client/list');
}

// 获取详情
export function getclientDetail(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.get(`/api/client/${id}`);
}

// 新增
export function saveclient(data: client) {
  return request.post('/api/client', data);
}

// 更新
export function updateclient(id: number, data: client) {
  if (!id) return Promise.reject('ID不能为空');
  return request.put(`/api/client/${id}`, data);
}

// 删除
export function deleteclient(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.delete(`/api/client/${id}`);
}

// 批量删除
export function batchDeleteclient(ids: number[]) {
  if (!ids || ids.length === 0) return Promise.reject('ID列表不能为空');
  return request.delete('/api/client/batch', { data: { ids } });
}

// 导出数据
export function exportclient(params?: clientPageQuery) {
  return request.get('/api/client/export', { 
    params,
    responseType: 'blob'
  });
}