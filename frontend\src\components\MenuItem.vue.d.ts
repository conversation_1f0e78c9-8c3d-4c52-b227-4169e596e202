import type { Menu } from '../api/menu';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    menu: {
        type: () => Menu;
        required: true;
    };
}>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    menu: {
        type: () => Menu;
        required: true;
    };
}>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
