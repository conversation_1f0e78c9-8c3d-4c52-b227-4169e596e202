package com.dz.ms.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;

/**
 * 网关CORS配置
 * <AUTHOR>
 * @date 2025-07-26
 * @version 1.0.0
 */
@Configuration
public class CorsConfig {

    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true); // 允许携带cookie
        
        // 设置允许跨域的来源
        config.addAllowedOrigin("http://localhost:5173"); // 开发环境
        config.addAllowedOrigin("http://localhost"); // Docker部署环境
        config.addAllowedOrigin("http://localhost:80"); // Docker部署环境（显式端口）
        config.addAllowedOrigin("http://localhost:30080"); // K8s NodePort部署环境
        
        config.addAllowedHeader("*"); // 允许所有请求头
        config.addAllowedMethod("*"); // 允许所有请求方法（GET, POST, PUT, DELETE等）
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config); // 对所有路径生效
        
        return new CorsWebFilter(source);
    }
}
