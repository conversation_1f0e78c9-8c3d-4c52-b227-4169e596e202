package com.dz.ms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.entity.UserRole;
import java.util.List;

/**
 * 用户角色关联Service接口
 * <AUTHOR>
 * @date 2025-05-29
 * @version 1.0.0
 */
public interface UserRoleService extends IService<UserRole> {

    /**
     * 根据用户ID获取用户角色关联列表
     * @param userId 用户ID
     * @return 用户角色关联列表
     */
    List<UserRole> getUserRoleByUserId(Long userId);

    /**
     * 保存用户角色关联
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean saveUserRoles(Long userId, List<Long> roleIds);
} 