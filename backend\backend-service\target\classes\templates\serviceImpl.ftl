package ${basePackage}.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ${basePackage}.entity.${entityCode};
import ${basePackage}.mapper.${entityCode}Mapper;
import ${basePackage}.service.${entityCode}Service;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * ${entityCode} Service实现类
 * 由低代码平台自动生成
 */
@Service
public class ${entityCode}ServiceImpl extends ServiceImpl<${entityCode}Mapper, ${entityCode}> implements ${entityCode}Service {

    @Override
    public IPage<${entityCode}> page(Integer page, Integer size, String keyword) {
        Page<${entityCode}> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<${entityCode}> wrapper = new LambdaQueryWrapper<>();
        
        // TODO: 根据实际业务修改查询条件
        
        // 按更新时间倒序排序
        wrapper.orderByDesc(${entityCode}::getUpdateTime);
        
        return page(pageParam, wrapper);
    }
}