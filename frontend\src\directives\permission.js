import { usePermissionStore } from '../store/permission';
export const permission = {
    mounted(el, binding) {
        const permissionStore = usePermissionStore();
        const { value } = binding;
        if (value) {
            const hasPermission = permissionStore.hasPermission(value);
            if (!hasPermission) {
                el.parentNode?.removeChild(el);
            }
        }
    }
};
