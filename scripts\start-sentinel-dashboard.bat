@echo off
REM Sentinel 控制台启动脚本 (Windows)
REM 作者: AI
REM 日期: 2025-06-29

set SENTINEL_VERSION=1.8.6
set SENTINEL_JAR=sentinel-dashboard-%SENTINEL_VERSION%.jar
set SENTINEL_PORT=8080
set SENTINEL_USERNAME=sentinel
set SENTINEL_PASSWORD=sentinel123

echo 正在检查 Sentinel JAR 文件...
if not exist "%SENTINEL_JAR%" (
    echo Sentinel JAR 文件不存在，请下载 %SENTINEL_JAR%
    echo 下载地址: https://github.com/alibaba/Sentinel/releases/download/%SENTINEL_VERSION%/%SENTINEL_JAR%
    pause
    exit /b 1
)

echo 正在启动 Sentinel 控制台...
echo 端口: %SENTINEL_PORT%
echo 用户名: %SENTINEL_USERNAME%
echo 密码: %SENTINEL_PASSWORD%

java -Dserver.port=%SENTINEL_PORT% ^
     -Dcsp.sentinel.dashboard.server=localhost:%SENTINEL_PORT% ^
     -Dproject.name=sentinel-dashboard ^
     -Dsentinel.dashboard.auth.username=%SENTINEL_USERNAME% ^
     -Dsentinel.dashboard.auth.password=%SENTINEL_PASSWORD% ^
     -Xms512m ^
     -Xmx1024m ^
     -jar %SENTINEL_JAR%

pause
