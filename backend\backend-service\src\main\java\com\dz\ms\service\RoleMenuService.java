package com.dz.ms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.entity.RoleMenu;

import java.util.List;

/**
 * 角色-菜单关联Service接口
 * <AUTHOR>
 * @date 2025-05-29
 * @version 1.0.0
 */
public interface RoleMenuService extends IService<RoleMenu> {

    /**
     * 根据角色ID获取关联的菜单ID列表
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Long> getMenuIdsByRoleId(Long roleId);

    /**
     * 根据角色ID列表获取关联的菜单ID列表
     * @param roleIds 角色ID列表
     * @return 菜单ID列表
     */
    List<Long> getMenuIdsByRoleIds(List<Long> roleIds);

    /**
     * 保存角色和菜单的关联关系
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @return 是否成功
     */
    boolean saveRoleMenus(Long roleId, List<Long> menuIds);
} 