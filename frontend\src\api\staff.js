import request from '../utils/request';
// 获取分页列表
export function getstaffList(params) {
    if (!params.pageNum)
        params.pageNum = 1;
    if (!params.pageSize)
        params.pageSize = 10;
    return request.get('/api/staff/page', { params });
}
// 获取所有数据
export function getAllstaffs() {
    return request.get('/api/staff/list');
}
// 获取详情
export function getstaffDetail(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.get(`/api/staff/${id}`);
}
// 新增
export function savestaff(data) {
    return request.post('/api/staff', data);
}
// 更新
export function updatestaff(id, data) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.put(`/api/staff/${id}`, data);
}
// 删除
export function deletestaff(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.delete(`/api/staff/${id}`);
}
// 批量删除
export function batchDeletestaff(ids) {
    if (!ids || ids.length === 0)
        return Promise.reject('ID列表不能为空');
    return request.delete('/api/staff/batch', { data: { ids } });
}
// 导出数据
export function exportstaff(params) {
    return request.get('/api/staff/export', {
        params,
        responseType: 'blob'
    });
}
