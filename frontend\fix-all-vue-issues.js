import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Element Plus 实际存在的图标列表
const validElementIcons = [
  'AddLocation', 'Aim', 'AlarmClock', 'Apple', 'ArrowDown', 'ArrowDownBold', 'ArrowLeft',
  'ArrowLeftBold', 'ArrowRight', 'ArrowRightBold', 'ArrowUp', 'ArrowUpBold', 'Avatar',
  'Back', 'Baseball', 'Basketball', 'Bell', 'BellFilled', 'Bicycle', 'Bottom',
  'BottomLeft', 'BottomRight', 'Bowl', 'Box', 'Briefcase', 'Brush', 'BrushFilled',
  'Burger', 'Calendar', 'Camera', 'CameraFilled', 'CaretBottom', 'CaretLeft',
  'CaretRight', 'CaretTop', 'Cellphone', 'ChatDotRound', 'ChatDotSquare',
  'ChatLineRound', 'ChatLineSquare', 'ChatRound', 'ChatSquare', 'Check', 'Checked',
  'Cherry', 'Chicken', 'ChromeFilled', 'CircleCheck', 'CircleCheckFilled', 'CircleClose',
  'CircleCloseFilled', 'CirclePlus', 'CirclePlusFilled', 'Clock', 'Close', 'CloseBold',
  'Cloudy', 'Coffee', 'CoffeeCup', 'Coin', 'ColdDrink', 'Collection', 'CollectionTag',
  'Comment', 'Compass', 'Connection', 'Coordinate', 'CopyDocument', 'Cpu', 'CreditCard',
  'Crop', 'DArrowLeft', 'DArrowRight', 'DCaret', 'DataAnalysis', 'DataBoard', 'DataLine',
  'Delete', 'DeleteFilled', 'DeleteLocation', 'Dessert', 'Discount', 'Dish', 'DishDot',
  'Document', 'DocumentAdd', 'DocumentChecked', 'DocumentCopy', 'DocumentDelete',
  'DocumentRemove', 'Download', 'Drizzling', 'Edit', 'EditPen', 'Eleme', 'ElemeFilled',
  'ElementPlus', 'Expand', 'Failed', 'Female', 'Files', 'Film', 'Filter', 'Finished',
  'FirstAidKit', 'Flag', 'Fold', 'Folder', 'FolderAdd', 'FolderChecked', 'FolderDelete',
  'FolderOpened', 'FolderRemove', 'Food', 'Football', 'ForkSpoon', 'Fries', 'FullScreen',
  'Goblet', 'GobletFull', 'GobletSquare', 'GobletSquareFull', 'GoldMedal', 'Goods',
  'GoodsFilled', 'Grape', 'Grid', 'Guide', 'Handbag', 'Headset', 'HelpFilled', 'Hide',
  'Histogram', 'HomeFilled', 'HotWater', 'House', 'IceCream', 'IceCreamRound',
  'IceCreamSquare', 'IceDrink', 'IceTea', 'InfoFilled', 'Iphone', 'Key', 'KnifeFork',
  'Lightning', 'Link', 'List', 'Loading', 'Location', 'LocationFilled',
  'LocationInformation', 'Lock', 'Lollipop', 'Magic', 'Magnet', 'Male', 'Management',
  'MapLocation', 'Medal', 'Memo', 'Menu', 'Message', 'MessageBox', 'Mic', 'Microphone',
  'MilkTea', 'Minus', 'Money', 'Monitor', 'Moon', 'MoonNight', 'More', 'MoreFilled',
  'MostlyCloudy', 'Mouse', 'Mug', 'Mute', 'MuteNotification', 'NoSmoking', 'Notebook',
  'Notification', 'Odometer', 'OfficeBuilding', 'Open', 'Operation', 'Opportunity',
  'Orange', 'Paperclip', 'PartlyCloudy', 'Pear', 'Phone', 'PhoneFilled', 'Picture',
  'PictureFilled', 'PictureRounded', 'PieChart', 'Place', 'Platform', 'Plus', 'Pointer',
  'Position', 'Postcard', 'Pouring', 'Present', 'PriceTag', 'Printer', 'Promotion',
  'QuartzWatch', 'QuestionFilled', 'Rank', 'Reading', 'ReadingLamp', 'Refresh',
  'RefreshLeft', 'RefreshRight', 'Refrigerator', 'Remove', 'RemoveFilled', 'Right',
  'ScaleToOriginal', 'School', 'Scissor', 'Search', 'Select', 'Sell', 'SemiSelect',
  'Service', 'Setting', 'Share', 'Ship', 'Shop', 'ShoppingBag', 'ShoppingCart',
  'ShoppingCartFull', 'ShoppingTrolley', 'Smoking', 'Soccer', 'SoldOut', 'Sort',
  'SortDown', 'SortUp', 'Stamp', 'Star', 'StarFilled', 'Stopwatch', 'SuccessFilled',
  'Sugar', 'Suitcase', 'SuitcaseLine', 'Sunny', 'Sunrise', 'Sunset', 'Switch',
  'SwitchButton', 'SwitchFilled', 'TakeawayBox', 'Ticket', 'Tickets', 'Timer', 'ToiletPaper',
  'Tools', 'Top', 'TopLeft', 'TopRight', 'TrendCharts', 'Trophy', 'TrophyBase', 'TurnOff',
  'Umbrella', 'Unlock', 'Upload', 'UploadFilled', 'User', 'UserFilled', 'Van', 'VideoCameraFilled',
  'VideoPlay', 'View', 'Wallet', 'WalletFilled', 'Warning', 'WarningFilled',
  'WarnTriangleFilled', 'Watch', 'Watermelon', 'WindPower', 'ZoomIn', 'ZoomOut'
];

// 不存在的图标及其替代品映射
const iconReplaceMap = {
  'Role': 'UserFilled',
  'SetUp': 'Setting',
  'VideoPause': 'VideoPlay',
  'VideoCamera': 'VideoCameraFilled',
};

// 查找Vue文件
console.log('查找Vue文件...');
const findVueFiles = () => {
  try {
    const cmd = 'cd ' + __dirname + ' && dir /s /b *.vue';
    const stdout = execSync(cmd, { encoding: 'utf8' });
    return stdout.split('\r\n').filter(Boolean);
  } catch (error) {
    console.error('查找文件时出错:', error);
    return [];
  }
};

// 修复文件
const fixFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否包含脚本部分
    if (!content.includes('<script')) return false;
    
    let modified = false;
    let newContent = content;
    
    // 1. 修复导入语句中的多余逗号
    const commaRegex = /import\s*{([^}]*,\s*,\s*[^}]*)}\s*from\s*['"][^'"]+['"]/g;
    if (commaRegex.test(newContent)) {
      newContent = newContent.replace(/,\s*,\s*/g, ',');
      modified = true;
      console.log(`修复了文件 ${filePath} 中的多余逗号`);
    }
    
    // 2. 修复末尾多余的分号
    const semicolonRegex = /;\s*;/g;
    if (semicolonRegex.test(newContent)) {
      newContent = newContent.replace(/;\s*;/g, ';');
      modified = true;
      console.log(`修复了文件 ${filePath} 中的多余分号`);
    }
    
    // 3. 修复 Element Plus 类型导入
    const elementPlusImportRegex = /import\s*{([^}]*)}\s*from\s*['"]element-plus['"]/g;
    
    let match;
    while ((match = elementPlusImportRegex.exec(content)) !== null) {
      const importStatement = match[0];
      const importContent = match[1];
      
      // 检查是否包含 FormInstance 或 FormRules
      if (importContent.includes('FormInstance') || importContent.includes('FormRules')) {
        // 提取非类型导入
        const nonTypeImports = importContent
          .split(',')
          .map(item => item.trim())
          .filter(item => item && item !== 'FormInstance' && item !== 'FormRules');
        
        // 提取类型导入
        const typeImports = importContent
          .split(',')
          .map(item => item.trim())
          .filter(item => item && (item === 'FormInstance' || item === 'FormRules'));
        
        if (typeImports.length > 0) {
          let newImportStatement = '';
          
          // 如果有非类型导入，保留原导入语句但移除类型
          if (nonTypeImports.length > 0) {
            newImportStatement += `import { ${nonTypeImports.join(', ')} } from 'element-plus'\n`;
          }
          
          // 添加类型导入语句
          newImportStatement += `import type { ${typeImports.join(', ')} } from 'element-plus'`;
          
          // 替换原导入语句
          newContent = newContent.replace(importStatement, newImportStatement);
          modified = true;
          console.log(`修复了文件 ${filePath} 中的类型导入`);
        }
      }
    }
    
    // 4. 修复 Element Plus 图标导入
    const iconImportRegex = /import\s*{([^}]*?)}\s*from\s*['"]@element-plus\/icons-vue['"]/g;
    
    if (iconImportRegex.test(newContent)) {
      // 移除图标导入语句
      newContent = newContent.replace(iconImportRegex, '// 图标已全局注册');
      modified = true;
      console.log(`移除了文件 ${filePath} 中的图标导入`);
    }
    
    // 5. 检查字符串形式的图标使用
    const iconUsageRegex = /icon:\s*['"]([^'"]+)['"]/g;
    
    while ((match = iconUsageRegex.exec(content)) !== null) {
      const iconName = match[1];
      
      // 检查是否是无效图标
      if (!validElementIcons.includes(iconName) && iconReplaceMap[iconName]) {
        const newIconName = iconReplaceMap[iconName];
        newContent = newContent.replace(
          new RegExp(`icon:\\s*['"]${iconName}['"]`, 'g'),
          `icon: '${newIconName}'`
        );
        modified = true;
        console.log(`替换了文件 ${filePath} 中的无效图标 ${iconName} 为 ${newIconName}`);
      }
    }
    
    // 如果文件被修改，则写回
    if (modified) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`处理文件 ${filePath} 时出错:`, error);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('开始检查和修复Vue文件问题...');
  const vueFiles = findVueFiles();
  console.log(`找到 ${vueFiles.length} 个Vue文件`);
  
  let fixedCount = 0;
  for (const file of vueFiles) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`检查完成，共修复了 ${fixedCount} 个文件`);
};

main(); 