package com.dz.ms.dynamic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dz.ms.dynamic.entity.staff;
import com.dz.ms.dynamic.service.staffService;
import com.dz.ms.lowcode.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 员工控制器
 * 由低代码平台自动生成
 */
@RestController
@RequestMapping("/api/staff")
@Tag(name = "员工管理", description = "员工相关接口")
public class staffController {

    @Autowired
    private staffService staffService;

    /**
     * 分页查询
     * @param page 页码
     * @param size 每页数量
     * @param keyword 关键字
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询员工")
    public Result<IPage<staff>> page(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        IPage<staff> result = staffService.page(page, size, keyword);
        return Result.success(result);
    }

    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 实体
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询员工")
    public Result<staff> getById(@PathVariable Long id) {
        staff entity = staffService.getById(id);
        return entity != null ? Result.success(entity) : Result.error("数据不存在");
    }

    /**
     * 新增
     * @param entity 实体
     * @return 是否成功
     */
    @PostMapping
    @Operation(summary = "新增员工")
    public Result<Boolean> save(@RequestBody staff entity) {
        boolean result = staffService.save(entity);
        return Result.success(result);
    }

    /**
     * 修改
     * @param entity 实体
     * @return 是否成功
     */
    @PutMapping
    @Operation(summary = "修改员工")
    public Result<Boolean> update(@RequestBody staff entity) {
        boolean result = staffService.updateById(entity);
        return Result.success(result);
    }

    /**
     * 删除
     * @param id 主键ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除员工")
    public Result<Boolean> delete(@PathVariable Long id) {
        boolean result = staffService.removeById(id);
        return Result.success(result);
    }
} 