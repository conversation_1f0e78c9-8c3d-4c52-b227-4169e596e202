package com.dz.ms.lowcode.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 元数据字段DTO，用于前后端交互
 * <AUTHOR>
 * @date 2023-07-01
 */
@Data
public class MetaFieldDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 所属实体ID */
    private Long entityId;

    /** 字段编码 */
    @NotBlank(message = "字段编码不能为空")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*$", message = "字段编码只能包含字母、数字和下划线，且必须以字母开头")
    @Size(min = 2, max = 50, message = "字段编码长度必须在2-50之间")
    private String fieldCode;

    /** 字段名称 */
    @NotBlank(message = "字段名称不能为空")
    @Size(min = 2, max = 50, message = "字段名称长度必须在2-50之间")
    private String fieldName;

    /** 字段描述 */
    private String description;

    /** 数据库字段名 */
    private String columnName;

    /** 数据类型 */
    @NotBlank(message = "数据类型不能为空")
    private String dataType;

    /** Java类型 */
    private String javaType;

    /** 字段长度 */
    private Integer length;

    /** 是否必填 */
    private Boolean required;

    /** 是否唯一 */
    private Boolean unique;

    /** 默认值 */
    private String defaultValue;

    /** 验证规则(JSON格式) */
    private String validationRules;

    /** 是否在列表中显示 */
    private Boolean showInList;

    /** 是否在表单中显示 */
    private Boolean showInForm;

    /** 是否可搜索 */
    private Boolean searchable;

    /** 排序号 */
    private Integer sortNo;
} 