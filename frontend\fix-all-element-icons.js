import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Element Plus 实际存在的图标列表（部分列表，可以根据官方文档补充）
const validElementIcons = [
  'AddLocation', 'Aim', 'AlarmClock', 'Apple', 'ArrowDown', 'ArrowDownBold', 'ArrowLeft',
  'ArrowLeftBold', 'ArrowRight', 'ArrowRightBold', 'ArrowUp', 'ArrowUpBold', 'Avatar',
  'Back', 'Baseball', 'Basketball', 'Bell', 'BellFilled', 'Bicycle', 'Bottom',
  'BottomLeft', 'BottomRight', 'Bowl', 'Box', 'Briefcase', 'Brush', 'BrushFilled',
  'Burger', 'Calendar', 'Camera', 'CameraFilled', 'CaretBottom', 'CaretLeft',
  'CaretRight', 'CaretTop', 'Cellphone', 'ChatDotRound', 'ChatDotSquare',
  'ChatLineRound', 'ChatLineSquare', 'ChatRound', 'ChatSquare', 'Check', 'Checked',
  'Cherry', 'Chicken', 'ChromeFilled', 'CircleCheck', 'CircleCheckFilled', 'CircleClose',
  'CircleCloseFilled', 'CirclePlus', 'CirclePlusFilled', 'Clock', 'Close', 'CloseBold',
  'Cloudy', 'Coffee', 'CoffeeCup', 'Coin', 'ColdDrink', 'Collection', 'CollectionTag',
  'Comment', 'Compass', 'Connection', 'Coordinate', 'CopyDocument', 'Cpu', 'CreditCard',
  'Crop', 'DArrowLeft', 'DArrowRight', 'DCaret', 'DataAnalysis', 'DataBoard', 'DataLine',
  'Delete', 'DeleteFilled', 'DeleteLocation', 'Dessert', 'Discount', 'Dish', 'DishDot',
  'Document', 'DocumentAdd', 'DocumentChecked', 'DocumentCopy', 'DocumentDelete',
  'DocumentRemove', 'Download', 'Drizzling', 'Edit', 'EditPen', 'Eleme', 'ElemeFilled',
  'ElementPlus', 'Expand', 'Failed', 'Female', 'Files', 'Film', 'Filter', 'Finished',
  'FirstAidKit', 'Flag', 'Fold', 'Folder', 'FolderAdd', 'FolderChecked', 'FolderDelete',
  'FolderOpened', 'FolderRemove', 'Food', 'Football', 'ForkSpoon', 'Fries', 'FullScreen',
  'Goblet', 'GobletFull', 'GobletSquare', 'GobletSquareFull', 'GoldMedal', 'Goods',
  'GoodsFilled', 'Grape', 'Grid', 'Guide', 'Handbag', 'Headset', 'HelpFilled', 'Hide',
  'Histogram', 'HomeFilled', 'HotWater', 'House', 'IceCream', 'IceCreamRound',
  'IceCreamSquare', 'IceDrink', 'IceTea', 'InfoFilled', 'Iphone', 'Key', 'KnifeFork',
  'Lightning', 'Link', 'List', 'Loading', 'Location', 'LocationFilled',
  'LocationInformation', 'Lock', 'Lollipop', 'Magic', 'Magnet', 'Male', 'Management',
  'MapLocation', 'Medal', 'Memo', 'Menu', 'Message', 'MessageBox', 'Mic', 'Microphone',
  'MilkTea', 'Minus', 'Money', 'Monitor', 'Moon', 'MoonNight', 'More', 'MoreFilled',
  'MostlyCloudy', 'Mouse', 'Mug', 'Mute', 'MuteNotification', 'NoSmoking', 'Notebook',
  'Notification', 'Odometer', 'OfficeBuilding', 'Open', 'Operation', 'Opportunity',
  'Orange', 'Paperclip', 'PartlyCloudy', 'Pear', 'Phone', 'PhoneFilled', 'Picture',
  'PictureFilled', 'PictureRounded', 'PieChart', 'Place', 'Platform', 'Plus', 'Pointer',
  'Position', 'Postcard', 'Pouring', 'Present', 'PriceTag', 'Printer', 'Promotion',
  'QuartzWatch', 'QuestionFilled', 'Rank', 'Reading', 'ReadingLamp', 'Refresh',
  'RefreshLeft', 'RefreshRight', 'Refrigerator', 'Remove', 'RemoveFilled', 'Right',
  'ScaleToOriginal', 'School', 'Scissor', 'Search', 'Select', 'Sell', 'SemiSelect',
  'Service', 'Setting', 'Share', 'Ship', 'Shop', 'ShoppingBag', 'ShoppingCart',
  'ShoppingCartFull', 'ShoppingTrolley', 'Smoking', 'Soccer', 'SoldOut', 'Sort',
  'SortDown', 'SortUp', 'Stamp', 'Star', 'StarFilled', 'Stopwatch', 'SuccessFilled',
  'Sugar', 'Suitcase', 'SuitcaseLine', 'Sunny', 'Sunrise', 'Sunset', 'Switch',
  'SwitchButton', 'SwitchFilled', 'TakeawayBox', 'Ticket', 'Tickets', 'Timer', 'ToiletPaper',
  'Tools', 'Top', 'TopLeft', 'TopRight', 'TrendCharts', 'Trophy', 'TrophyBase', 'TurnOff',
  'Umbrella', 'Unlock', 'Upload', 'UploadFilled', 'User', 'UserFilled', 'Van', 'VideoCameraFilled',
  'VideoPlay', 'View', 'Wallet', 'WalletFilled', 'Warning', 'WarningFilled',
  'WarnTriangleFilled', 'Watch', 'Watermelon', 'WindPower', 'ZoomIn', 'ZoomOut'
];

// 不存在的图标及其替代品映射
const iconReplaceMap = {
  'Role': 'UserFilled',       // 用UserFilled替换Role
  'SetUp': 'Setting',         // 用Setting替换SetUp
  'VideoPause': 'VideoPlay',  // 可能是错误的图标名
  'VideoCamera': 'VideoCameraFilled',
  // 添加其他可能错误的图标映射
};

// 查找Vue文件
console.log('查找Vue文件...');
const findVueFiles = () => {
  try {
    // 使用 find 命令查找所有 .vue 文件
    const cmd = 'cd ' + __dirname + ' && dir /s /b *.vue';
    const stdout = execSync(cmd, { encoding: 'utf8' });
    return stdout.split('\r\n').filter(Boolean);
  } catch (error) {
    console.error('查找文件时出错:', error);
    return [];
  }
};

// 检查文件中的图标
const checkFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 查找从 @element-plus/icons-vue 导入的图标
    const importMatch = content.match(/import\s*{([^}]*)}\s*from\s*['"]@element-plus\/icons-vue['"]/g);
    if (!importMatch) return false;
    
    let modified = false;
    let newContent = content;
    
    // 对每个导入语句进行处理
    for (const importStatement of importMatch) {
      // 提取导入的图标名称
      const iconNames = importStatement.match(/import\s*{([^}]*)}\s*from/)[1]
        .split(',')
        .map(icon => icon.trim())
        .filter(Boolean);
      
      // 检查是否有重复的图标导入
      const uniqueIcons = [...new Set(iconNames)];
      if (uniqueIcons.length !== iconNames.length) {
        console.log(`文件 ${filePath} 包含重复的图标导入`);
        
        // 修复重复导入
        let fixedImport = `import { ${uniqueIcons.join(', ')} } from '@element-plus/icons-vue'`;
        newContent = newContent.replace(importStatement, fixedImport);
        modified = true;
      }
      
      // 检查导入的图标是否存在
      for (const iconName of uniqueIcons) {
        if (!validElementIcons.includes(iconName) && iconReplaceMap[iconName]) {
          console.log(`文件 ${filePath} 导入了不存在的图标 ${iconName}，将替换为 ${iconReplaceMap[iconName]}`);
          
          // 修复导入语句中的图标
          const iconRegex = new RegExp(`\\b${iconName}\\b`, 'g');
          newContent = newContent.replace(iconRegex, iconReplaceMap[iconName]);
          modified = true;
        }
      }
    }
    
    // 如果文件被修改，则写回
    if (modified) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`已修复文件: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`处理文件 ${filePath} 时出错:`, error);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('开始检查和修复Element Plus图标问题...');
  const vueFiles = findVueFiles();
  console.log(`找到 ${vueFiles.length} 个Vue文件`);
  
  let fixedCount = 0;
  for (const file of vueFiles) {
    if (checkFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`检查完成，共修复了 ${fixedCount} 个文件`);
};

main(); 