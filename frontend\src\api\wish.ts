import request from '../utils/request';

export interface Wish {
  id?: number;
  memberId?: number;
  memberUsername?: string;
  memberNickname?: string;
  content: string;
  status: number;
  createTime?: string;
  updateTime?: string;
}

export interface WishPageQuery {
  pageNum?: number;
  pageSize?: number;
  memberId?: number;
  keyword?: string;
  status?: number;
}

export function getWishPage(params: WishPageQuery) {
  return request.get('/wish/page', { params });
}

export function addWish(data: Wish) {
  return request.post('/wish', data);
}

export function updateWish(id: number, data: Wish) {
  return request.put(`/wish/${id}`, data);
}

export function deleteWish(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.delete(`/wish/${id}`);
}

export function getWishDetail(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.get(`/wish/${id}`);
} 