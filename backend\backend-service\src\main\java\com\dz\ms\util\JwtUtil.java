package com.dz.ms.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.SecretKey;

import org.springframework.security.core.userdetails.UserDetails;

/**
 * JWT工具类
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
public class JwtUtil {

    // 使用固定的密钥字符串，实际生产环境中应从配置文件中读取
    private static final String SECRET_KEY_STRING = "RTM_SECURE_JWT_SECRET_KEY_SHOULD_BE_LONGER_THAN_256_BITS_FOR_HS512_ALGORITHM";
    private static final SecretKey SECRET_KEY = Keys.hmacShaKeyFor(SECRET_KEY_STRING.getBytes(StandardCharsets.UTF_8));
    private static final long EXPIRATION_TIME = 24 * 60 * 60 * 1000; // 1天

    /**
     * 生成JWT Token
     * @param claims 载荷信息
     * @param subject 主题（通常是用户名）
     * @return JWT Token
     */
    public static String generateToken(Map<String, Object> claims, String subject) {
        Date now = new Date();
        Date expirationDate = new Date(now.getTime() + EXPIRATION_TIME);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expirationDate)
                .signWith(SECRET_KEY, SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从Token中解析载荷信息
     * @param token JWT Token
     * @return 载荷信息
     */
    public static Claims extractAllClaims(String token) {
        return Jwts.parserBuilder().setSigningKey(SECRET_KEY).build().parseClaimsJws(token).getBody();
    }

    /**
     * 从Token中解析用户名
     * @param token JWT Token
     * @return 用户名
     */
    public static String extractUsername(String token) {
        return extractAllClaims(token).getSubject();
    }

    /**
     * 验证Token是否有效
     * @param token JWT Token
     * @param userDetails 用户详情
     * @return 是否有效
     */
    public static boolean validateToken(String token, UserDetails userDetails) {
        final String username = extractUsername(token);
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }

    /**
     * 判断Token是否过期
     * @param token JWT Token
     * @return 是否过期
     */
    private static boolean isTokenExpired(String token) {
        return extractAllClaims(token).getExpiration().before(new Date());
    }

    // 示例：生成只包含用户名的Token
    public static String generateToken(String username) {
        Map<String, Object> claims = new HashMap<>();
        return generateToken(claims, username);
    }
} 