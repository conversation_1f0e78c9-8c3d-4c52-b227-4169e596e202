package com.dz.ms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.ms.entity.Wish;
import com.dz.ms.vo.WishVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 心愿单Mapper
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@Mapper
public interface WishMapper extends BaseMapper<Wish> {
    
    /**
     * 分页查询心愿单，关联会员信息
     * @param page 分页参数
     * @param memberId 会员ID，可为null
     * @param keyword 会员用户名或昵称关键字，可为null
     * @param status 状态，可为null
     * @return 分页结果
     */
    IPage<WishVO> pageWishWithMember(Page<Wish> page, @Param("memberId") Long memberId, @Param("keyword") String keyword, @Param("status") Integer status);
} 