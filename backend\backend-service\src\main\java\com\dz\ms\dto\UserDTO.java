package com.dz.ms.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import javax.validation.groups.Default;

/**
 * 用户DTO
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@Data
public class UserDTO implements Serializable {
    /** 用户名 */
    @NotBlank(message = "用户名不能为空", groups = {Add.class, Default.class})
    private String username;

    /** 密码 */
    @NotBlank(message = "密码不能为空", groups = {Add.class})
    private String password;

    /** 昵称 */
    private String nickname;

    /** 手机号 */
    private String phone;

    /** 邮箱 */
    private String email;

    /** 状态 */
    @NotNull(message = "状态不能为空", groups = {Add.class, Update.class, Default.class})
    private Integer status;

    // Validation groups
    public interface Add extends Default {}
    public interface Update extends Default {}
} 