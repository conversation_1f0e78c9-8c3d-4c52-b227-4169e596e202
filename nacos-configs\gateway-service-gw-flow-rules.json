[{"resource": "backend-service", "resourceMode": 0, "grade": 1, "count": 100, "intervalSec": 1, "controlBehavior": 0, "burst": 0, "maxQueueingTimeMs": 500}, {"resource": "user_api", "resourceMode": 1, "grade": 1, "count": 20, "intervalSec": 1, "controlBehavior": 0, "burst": 0, "maxQueueingTimeMs": 500}, {"resource": "member_api", "resourceMode": 1, "grade": 1, "count": 30, "intervalSec": 1, "controlBehavior": 0, "burst": 0, "maxQueueingTimeMs": 500}]