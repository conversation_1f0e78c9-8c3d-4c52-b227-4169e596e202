<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-left">
        <div class="system-info">
          <h1 class="system-title">企业级管理系统</h1>
          <p class="system-desc">基于 Spring Cloud 微服务架构的企业级管理平台</p>
          <div class="system-features">
            <div class="feature-item">
              <el-icon><Lock /></el-icon>
              <span>安全可靠</span>
            </div>
            <div class="feature-item">
              <el-icon><DataAnalysis /></el-icon>
              <span>数据分析</span>
            </div>
            <div class="feature-item">
              <el-icon><Operation /></el-icon>
              <span>高效管理</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="login-right">
        <el-card class="login-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h2>系统登录</h2>
              <p class="welcome-text">欢迎回来，请登录您的账号</p>
            </div>
          </template>
          
          <el-form 
            :model="loginForm" 
            :rules="loginRules" 
            ref="loginFormRef" 
            label-width="0px" 
            class="login-form"
            @keyup.enter="handleLogin"
          >
            <el-form-item prop="username">
              <el-input 
                v-model="loginForm.username" 
                placeholder="用户名" 
                :prefix-icon="User" 
                size="large"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input 
                type="password" 
                v-model="loginForm.password" 
                placeholder="密码" 
                :prefix-icon="Lock" 
                show-password
                size="large"
              />
            </el-form-item>
            
            <el-form-item prop="captcha">
              <div class="captcha-container">
                <el-input 
                  v-model="loginForm.captcha" 
                  placeholder="验证码" 
                  :prefix-icon="Key" 
                  size="large"
                />
                <div class="captcha-image" @click="refreshCaptcha">
                  <img :src="captchaUrl" alt="验证码" />
                </div>
              </div>
            </el-form-item>
            
            <el-form-item class="remember-me">
              <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
              <el-link type="primary" :underline="false">忘记密码?</el-link>
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                class="login-button" 
                @click="handleLogin" 
                :loading="loading"
                size="large"
              >
                登录
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="login-footer">
            <p>© {{ new Date().getFullYear() }} 企业级管理系统 版权所有</p>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { useRouter } from 'vue-router';
import { useMenuStore } from '../../store/menu';
import { usePermissionStore } from '../../store/permission';
import { getUserMenus } from '../../api/user';
import { getCaptcha, login } from '../../api/auth';
import { loadDynamicRoutes } from '../../router';
import { User, Lock, Key } from '@element-plus/icons-vue';
// 图标已全局注册;

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: '',
  captcha: '',
  remember: false
});

// 验证码相关
const captchaUrl = ref('');
const captchaId = ref('');

// 刷新验证码
/**
 * Refreshes the captcha image by:
 * 1. Generating a new captcha ID
 * 2. Fetching the captcha image blob from server
 * 3. Creating and managing Blob URL for the image
 * 4. Handling image loading states and fallbacks
 * 5. Properly cleaning up previous Blob URLs to prevent memory leaks
 * 
 * @async
 * @throws {Error} When captcha fetch fails
 * @emits console.log Debug information about captcha loading process
 * @emits console.error Error information when captcha fails to load
 * @emits ElMessage.error User-facing error message when captcha fetch fails
 */
const refreshCaptcha = async () => {
  try {
    captchaId.value = Date.now().toString();
    const captchaBlob = await getCaptcha(captchaId.value);
    
    console.log("验证码获取：",captchaBlob)
    

    // 释放之前的Blob URL，避免内存泄漏
    if (captchaUrl.value && captchaUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(captchaUrl.value);
    }

    
  
    // 创建新的Blob URL
    captchaUrl.value = URL.createObjectURL(captchaBlob);
    
    // // 强制更新视图
    // await nextTick();
    
    // console.log('验证码已刷新', {
    //   blobType: captchaBlob.type,
    //   blobSize: captchaBlob.size,
    //   blobUrl: captchaUrl.value
    // });
    
    // 检查图片加载状态
    const img = new Image();
    img.onload = () => {
      console.log('图片加载成功', {
        naturalWidth: img.naturalWidth,
        naturalHeight: img.naturalHeight
      });
      // 检查DOM中的图片元素
      const domImg = document.querySelector('.captcha-image img');
      console.log('DOM图片状态', {
        src: domImg?.src,
        complete: domImg?.complete,
        naturalWidth: domImg?.naturalWidth,
        naturalHeight: domImg?.naturalHeight
      });
    };
    img.onerror = (e) => {
      console.error('图片加载失败', e);
      // 尝试直接创建Base64 URL作为备用方案
      const reader = new FileReader();
      reader.onload = (e) => {
        console.log('Base64图片数据', e.target?.result);
        captchaUrl.value = e.target?.result as string;
      };
      reader.readAsDataURL(captchaBlob);
    };
    img.src = captchaUrl.value;
    
  } catch (error) {
    console.error('获取验证码失败:', error);
    ElMessage.error('获取验证码失败，请刷新页面重试');
  }
};

// 表单引用和加载状态
const loginFormRef = ref<FormInstance>();
const loading = ref(false);

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 6, message: '验证码长度不正确', trigger: 'blur' }
  ]
};

// 路由和状态管理
const router = useRouter();
const menuStore = useMenuStore();
const permissionStore = usePermissionStore();

// 登录处理
const handleLogin = () => {
  loginFormRef.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        // 发送登录请求
        const res = await login({
          ...loginForm,
          captchaId: captchaId.value
        });
        
        if (res && res.code === 0) {
          const token = res.data;
          
          // 保存token和记住我状态
          localStorage.setItem('token', token);
          if (loginForm.remember) {
            localStorage.setItem('remember', 'true');
            localStorage.setItem('username', loginForm.username);
          } else {
            localStorage.removeItem('remember');
            localStorage.removeItem('username');
          }
          
          // 清除旧的权限数据
          permissionStore.clearPermissions();
          menuStore.setMenuTree([]);

          try {
            // 获取用户菜单
            const menuRes = await getUserMenus();
            
            if (menuRes && menuRes.code === 0 && menuRes.data) {
              // 保存菜单树
              menuStore.setMenuTree(menuRes.data);
              
              // 从菜单中提取权限
              const permissions: string[] = [];
              const extractPermissions = (menus: any[]) => {
                menus.forEach(menu => {
                  if (menu.permission) {
                    permissions.push(menu.permission);
                  }
                  if (menu.children && menu.children.length > 0) {
                    extractPermissions(menu.children);
                  }
                });
              };
              
              extractPermissions(menuRes.data);
              permissionStore.setPermissions(permissions);
              
              // 添加动态路由
              loadDynamicRoutes(menuRes.data);
              
              ElMessage.success('登录成功');
              
              // 延迟跳转，确保路由已添加完成
              setTimeout(() => {
                router.push('/welcome');
              }, 100);
            } else {
              ElMessage.error(menuRes.msg || '获取菜单失败');
              localStorage.removeItem('token');
              refreshCaptcha();
            }
          } catch (menuError) {
            console.error('获取菜单错误:', menuError);
            ElMessage.error('获取用户权限失败');
            localStorage.removeItem('token');
            refreshCaptcha();
          }
        } else {
          ElMessage.error(res.data?.msg || '登录失败');
          refreshCaptcha();
        }
      } catch (error) {
        console.error('登录请求失败:', error);
        ElMessage.error('登录失败，请稍后重试');
        refreshCaptcha();
      } finally {
        loading.value = false;
      }
    }
  });
};

// 页面加载时初始化
onMounted(() => {
  // 刷新验证码
  refreshCaptcha();
  
  // 检查是否记住了用户名
  const remembered = localStorage.getItem('remember');
  if (remembered === 'true') {
    const savedUsername = localStorage.getItem('username');
    if (savedUsername) {
      loginForm.username = savedUsername;
      loginForm.remember = true;
    }
  }
});

// 组件卸载时释放Blob URL，避免内存泄漏
onUnmounted(() => {
  if (captchaUrl.value && captchaUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(captchaUrl.value);
  }
});
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.login-box {
  display: flex;
  width: 900px;
  height: 560px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.login-left {
  width: 45%;
  background: linear-gradient(135deg, #409EFF 0%, #1E88E5 100%);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(30deg);
    z-index: 0;
  }
}

.system-info {
  position: relative;
  z-index: 1;
}

.system-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 16px;
}

.system-desc {
  font-size: 16px;
  margin-bottom: 40px;
  opacity: 0.9;
}

.system-features {
  margin-top: 30px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  
  .el-icon {
    font-size: 24px;
    margin-right: 12px;
  }
  
  span {
    font-size: 16px;
  }
}

.login-right {
  width: 55%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.login-card {
  width: 100%;
  border: none;
  
  :deep(.el-card__header) {
    padding-bottom: 0;
    border-bottom: none;
  }
  
  :deep(.el-card__body) {
    padding-top: 10px;
  }
}

.card-header {
  text-align: center;
  
  h2 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #303133;
  }
}

.welcome-text {
  color: #909399;
  font-size: 14px;
  margin-bottom: 20px;
}

.login-form {
  padding: 10px 0;
}

.captcha-container {
  display: flex;
  gap: 10px;
  
  .el-input {
    flex: 1;
  }
  
  .captcha-image {
    width: 120px;
    height: 40px;
    cursor: pointer;
    border-radius: 4px;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.remember-me {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  border-radius: 4px;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
  color: #909399;
  font-size: 12px;
}

// 响应式布局
@media (max-width: 768px) {
  .login-box {
    flex-direction: column;
    width: 100%;
    height: auto;
  }
  
  .login-left {
    width: 100%;
    padding: 30px;
  }
  
  .login-right {
    width: 100%;
  }
}
</style> 