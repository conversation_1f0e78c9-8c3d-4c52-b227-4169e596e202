# Sentinel 熔断限流部署指南

## 1. 概述

本文档介绍如何在 RTM 项目中部署和配置 Sentinel 熔断限流功能，包括 Sentinel 控制台的部署、规则配置和监控设置。

## 2. Sentinel 控制台部署

### 2.1 下载 Sentinel 控制台

```bash
# 下载 Sentinel 控制台 JAR 包
wget https://github.com/alibaba/Sentinel/releases/download/1.8.6/sentinel-dashboard-1.8.6.jar
```

### 2.2 启动 Sentinel 控制台

```bash
# 启动控制台（默认端口 8080）
java -Dserver.port=8080 -Dcsp.sentinel.dashboard.server=localhost:8080 -Dproject.name=sentinel-dashboard -jar sentinel-dashboard-1.8.6.jar

# 或者使用自定义配置启动
java -Dserver.port=8080 \
     -Dcsp.sentinel.dashboard.server=localhost:8080 \
     -Dproject.name=sentinel-dashboard \
     -Dsentinel.dashboard.auth.username=sentinel \
     -Dsentinel.dashboard.auth.password=sentinel123 \
     -jar sentinel-dashboard-1.8.6.jar
```

### 2.3 访问控制台

- 访问地址：http://localhost:8080
- 默认用户名：sentinel
- 默认密码：sentinel

## 3. Nacos 配置中心规则配置

### 3.1 在 Nacos 中创建配置

登录 Nacos 控制台（http://localhost:8848/nacos），创建以下配置：

#### 3.1.1 后端服务流控规则

- **Data ID**: `backend-service-flow-rules`
- **Group**: `SENTINEL_GROUP`
- **配置格式**: JSON
- **配置内容**: 参考 `nacos-configs/backend-service-flow-rules.json`

#### 3.1.2 后端服务熔断规则

- **Data ID**: `backend-service-degrade-rules`
- **Group**: `SENTINEL_GROUP`
- **配置格式**: JSON
- **配置内容**: 参考 `nacos-configs/backend-service-degrade-rules.json`

#### 3.1.3 后端服务系统规则

- **Data ID**: `backend-service-system-rules`
- **Group**: `SENTINEL_GROUP`
- **配置格式**: JSON
- **配置内容**: 参考 `nacos-configs/backend-service-system-rules.json`

#### 3.1.4 网关流控规则

- **Data ID**: `gateway-service-gw-flow-rules`
- **Group**: `SENTINEL_GROUP`
- **配置格式**: JSON
- **配置内容**: 参考 `nacos-configs/gateway-service-gw-flow-rules.json`

#### 3.1.5 网关API分组规则

- **Data ID**: `gateway-service-gw-api-group-rules`
- **Group**: `SENTINEL_GROUP`
- **配置格式**: JSON
- **配置内容**: 参考 `nacos-configs/gateway-service-gw-api-group-rules.json`

## 4. 应用启动顺序

1. **启动 Nacos**
   ```bash
   cd nacos/bin
   ./startup.sh -m standalone
   ```

2. **启动 Sentinel 控制台**
   ```bash
   java -jar sentinel-dashboard-1.8.6.jar
   ```

3. **启动网关服务**
   ```bash
   cd backend/gateway
   mvn spring-boot:run
   ```

4. **启动后端服务**
   ```bash
   cd backend/backend-service
   mvn spring-boot:run
   ```

## 5. 验证部署

### 5.1 检查服务注册

1. 访问 Nacos 控制台，确认服务已注册
2. 访问 Sentinel 控制台，确认应用已接入

### 5.2 测试限流功能

```bash
# 快速发送多个请求测试限流
for i in {1..20}; do
  curl -X GET "http://localhost:9090/api/user/page?pageNum=1&pageSize=10" &
done
```

### 5.3 查看监控数据

在 Sentinel 控制台中查看：
- 实时监控数据
- 流控规则效果
- 熔断状态

## 6. 规则配置说明

### 6.1 流控规则参数

- **resource**: 资源名称
- **grade**: 限流阈值类型（1-QPS，0-线程数）
- **count**: 限流阈值
- **strategy**: 流控模式（0-直接，1-关联，2-链路）
- **controlBehavior**: 流控效果（0-快速失败，1-Warm Up，2-排队等待）

### 6.2 熔断规则参数

- **resource**: 资源名称
- **grade**: 熔断策略（0-慢调用比例，1-异常比例，2-异常数）
- **count**: 阈值
- **timeWindow**: 熔断时长（秒）
- **minRequestAmount**: 最小请求数

## 7. 监控和告警

### 7.1 监控指标

- QPS（每秒查询率）
- 响应时间
- 异常比例
- 线程数

### 7.2 告警配置

可以通过以下方式配置告警：
- Sentinel 控制台告警
- 自定义监控系统集成
- 日志监控

## 8. 故障排查

### 8.1 常见问题

1. **控制台无法看到应用**
   - 检查应用是否正确配置了 dashboard 地址
   - 确认端口是否正确
   - 检查网络连通性

2. **规则不生效**
   - 检查 Nacos 配置是否正确
   - 确认 dataId 和 group 配置
   - 查看应用日志

3. **限流不准确**
   - 检查时间窗口配置
   - 确认资源名称是否正确
   - 查看统计窗口设置

### 8.2 日志查看

```bash
# 查看应用日志
tail -f logs/backend-service.log | grep Sentinel

# 查看 Sentinel 相关日志
tail -f logs/sentinel-record.log
```

## 9. 性能优化建议

1. **合理设置限流阈值**：根据实际业务需求和系统容量设置
2. **选择合适的流控模式**：根据业务场景选择直接、关联或链路模式
3. **配置合理的熔断参数**：避免过于敏感或过于迟钝的熔断
4. **监控资源使用情况**：定期查看 CPU、内存使用情况
5. **定期调整规则**：根据业务发展调整限流和熔断规则

## 10. 安全注意事项

1. **控制台访问控制**：设置强密码，限制访问IP
2. **规则变更审计**：记录规则变更历史
3. **敏感信息保护**：避免在规则中暴露敏感信息
4. **网络安全**：使用防火墙限制不必要的端口访问
