package com.dz.ms.dynamic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 测试实体类
 * 由低代码平台自动生成
 */
@Data
@TableName("lc_test")
public class test implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** c1 */
    @TableField("c")
    private String c1;
    
    /** d1 */
    @TableField("d")
    private String d1;
    
    /** e */
    @TableField("e")
    private String e;
    
    /** f */
    @TableField("f")
    private String f;
    
    /** gg1 */
    @TableField("g")
    private String g1;
    
    /** h */
    @TableField("h")
    private Integer h;
    
    /** 创建人 */
    @TableField("creator")
    private String creator;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 更新人 */
    @TableField("updater")
    private String updater;

    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;
} 