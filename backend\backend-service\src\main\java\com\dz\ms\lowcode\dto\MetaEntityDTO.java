package com.dz.ms.lowcode.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 元数据实体DTO，用于前后端交互
 * <AUTHOR>
 * @date 2023-07-01
 */
@Data
public class MetaEntityDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 实体编码，用于生成表名和类名 */
    @NotBlank(message = "实体编码不能为空")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*$", message = "实体编码只能包含字母、数字和下划线，且必须以字母开头")
    @Size(min = 2, max = 50, message = "实体编码长度必须在2-50之间")
    private String entityCode;

    /** 实体名称 */
    @NotBlank(message = "实体名称不能为空")
    @Size(min = 2, max = 50, message = "实体名称长度必须在2-50之间")
    private String entityName;

    /** 实体描述 */
    private String description;

    /** 数据库表名 */
    private String tableName;

    /** 是否已发布 */
    private Boolean published;

    /** 是否启用 */
    private Boolean enabled;

    /** 字段列表 */
    private List<MetaFieldDTO> fields;
} 