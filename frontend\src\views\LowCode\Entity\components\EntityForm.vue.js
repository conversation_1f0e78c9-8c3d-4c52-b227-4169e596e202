/// <reference types="../../../../../node_modules/.vue-global-types/vue_3.5_0_0_0.d.ts" />
import { ref, reactive, defineProps, defineEmits, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
// 图标已全局注册;
import { saveEntity, updateEntity, getEntityById, dataTypeOptions, getJavaType, generateAlterSql, executeSql } from '@/api/lowcode';
// 定义属性
const props = defineProps({
    entityData: {
        type: Object,
        default: () => null
    },
    formType: {
        type: String,
        default: 'add'
    }
});
// 定义事件
const emit = defineEmits(['success', 'cancel']);
// 表单引用
const formRef = ref();
// 表单数据
const form = reactive({
    id: undefined,
    entityCode: '',
    entityName: '',
    tableName: '',
    description: '',
    fields: []
});
// 表单验证规则
const rules = {
    entityCode: [
        { required: true, message: '请输入实体编码', trigger: 'blur' },
        { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '实体编码只能包含字母、数字和下划线，且必须以字母开头', trigger: 'blur' },
        { min: 1, max: 50, message: '实体编码长度必须在1-50之间', trigger: 'blur' }
    ],
    entityName: [
        { required: true, message: '请输入实体名称', trigger: 'blur' },
        { min: 1, max: 50, message: '实体名称长度必须在1-50之间', trigger: 'blur' }
    ],
    tableName: [
        { required: true, message: '请输入表名', trigger: 'blur' }
    ]
};
// 字段验证规则
const fieldRules = {
    fieldCode: [
        { required: true, message: '请输入字段编码', trigger: 'blur' },
        { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段编码只能包含字母、数字和下划线，且必须以字母开头', trigger: 'blur' },
        { min: 1, max: 50, message: '字段编码长度必须在1-50之间', trigger: 'blur' }
    ],
    fieldName: [
        { required: true, message: '请输入字段名称', trigger: 'blur' },
        { min: 1, max: 50, message: '字段名称长度必须在1-50之间', trigger: 'blur' }
    ],
    dataType: [
        { required: true, message: '请选择数据类型', trigger: 'change' }
    ]
};
// SQL弹窗相关
const sqlDialogVisible = ref(false);
const alterSql = ref('');
const sqlExecLoading = ref(false);
// 初始化表单数据
const initForm = async () => {
    if (props.formType === 'edit' && props.entityData) {
        try {
            const res = await getEntityById(props.entityData.id);
            if (res.success) {
                const entityData = res.data;
                Object.assign(form, {
                    id: entityData.id,
                    entityCode: entityData.entityCode,
                    entityName: entityData.entityName,
                    tableName: entityData.tableName,
                    description: entityData.description,
                    fields: entityData.fields || []
                });
            }
            else {
                ElMessage.error(res.message || '获取实体详情失败');
            }
        }
        catch (error) {
            console.error(error);
            ElMessage.error('获取实体详情失败');
        }
    }
    else {
        Object.assign(form, {
            id: undefined,
            entityCode: '',
            entityName: '',
            tableName: '',
            description: '',
            fields: []
        });
    }
};
// 监听表单类型和数据变化
watch(() => props.formType, initForm, { immediate: true });
watch(() => props.entityData, initForm, { immediate: true });
// 监听实体编码变化，自动生成表名
watch(() => form.entityCode, (val) => {
    if (val && !form.tableName) {
        form.tableName = `lc_${val.toLowerCase()}`;
    }
});
// 添加字段
const addField = () => {
    form.fields.push({
        fieldCode: '',
        fieldName: '',
        dataType: 'String',
        javaType: 'String',
        length: 255,
        required: false,
        unique: false,
        showInList: true,
        showInForm: true,
        searchable: false
    });
};
// 移除字段
const removeField = (index) => {
    form.fields.splice(index, 1);
};
// 处理数据类型变更
const handleDataTypeChange = (row) => {
    row.javaType = getJavaType(row.dataType);
    // 根据数据类型设置默认长度
    if (row.dataType === 'String') {
        row.length = 255;
    }
    else if (row.dataType === 'Text') {
        row.length = 0;
    }
};
// 提交表单
const submitForm = async () => {
    if (!formRef.value)
        return;
    try {
        await formRef.value.validate();
        // 为每个字段设置列名
        form.fields.forEach(field => {
            if (!field.columnName) {
                field.columnName = field.fieldCode.toLowerCase();
            }
        });
        // 1. 先生成ALTER SQL
        if (form.id) {
            // 编辑模式，生成ALTER SQL
            const res = await generateAlterSql(form.id, form.fields);
            alterSql.value = res.data || '';
            sqlDialogVisible.value = true;
        }
        else {
            // 新增直接保存
            const api = saveEntity;
            const res = await api(form);
            if (res.success) {
                ElMessage.success('保存成功');
                emit('success');
            }
            else {
                ElMessage.error(res.message || '保存失败');
            }
        }
    }
    catch (error) {
        console.error(error);
        ElMessage.error('表单验证失败，请检查输入');
    }
};
// 执行SQL
const handleExecuteSql = async () => {
    sqlExecLoading.value = true;
    try {
        const res = await executeSql(alterSql.value);
        if (res.success) {
            ElMessage.success('SQL执行成功');
            sqlDialogVisible.value = false;
            // 执行完毕后再保存字段信息
            const api = updateEntity;
            const saveRes = await api(form);
            if (saveRes.success) {
                ElMessage.success('保存成功');
                emit('success');
            }
            else {
                ElMessage.error(saveRes.message || '保存失败');
            }
        }
        else {
            ElMessage.error(res.message || 'SQL执行失败');
        }
    }
    catch (e) {
        ElMessage.error('SQL执行异常');
    }
    finally {
        sqlExecLoading.value = false;
    }
};
// 取消
const cancel = () => {
    emit('cancel');
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['el-table']} */ ;
/** @type {__VLS_StyleScopedClasses['el-table']} */ ;
/** @type {__VLS_StyleScopedClasses['el-table']} */ ;
/** @type {__VLS_StyleScopedClasses['el-table']} */ ;
/** @type {__VLS_StyleScopedClasses['el-table']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "entity-form" },
});
const __VLS_0 = {}.ElForm;
/** @type {[typeof __VLS_components.ElForm, typeof __VLS_components.elForm, typeof __VLS_components.ElForm, typeof __VLS_components.elForm, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
    ref: "formRef",
    model: (__VLS_ctx.form),
    rules: (__VLS_ctx.rules),
    labelWidth: "100px",
}));
const __VLS_2 = __VLS_1({
    ref: "formRef",
    model: (__VLS_ctx.form),
    rules: (__VLS_ctx.rules),
    labelWidth: "100px",
}, ...__VLS_functionalComponentArgsRest(__VLS_1));
/** @type {typeof __VLS_ctx.formRef} */ ;
var __VLS_4 = {};
__VLS_3.slots.default;
const __VLS_6 = {}.ElDivider;
/** @type {[typeof __VLS_components.ElDivider, typeof __VLS_components.elDivider, typeof __VLS_components.ElDivider, typeof __VLS_components.elDivider, ]} */ ;
// @ts-ignore
const __VLS_7 = __VLS_asFunctionalComponent(__VLS_6, new __VLS_6({}));
const __VLS_8 = __VLS_7({}, ...__VLS_functionalComponentArgsRest(__VLS_7));
__VLS_9.slots.default;
var __VLS_9;
const __VLS_10 = {}.ElRow;
/** @type {[typeof __VLS_components.ElRow, typeof __VLS_components.elRow, typeof __VLS_components.ElRow, typeof __VLS_components.elRow, ]} */ ;
// @ts-ignore
const __VLS_11 = __VLS_asFunctionalComponent(__VLS_10, new __VLS_10({
    gutter: (20),
}));
const __VLS_12 = __VLS_11({
    gutter: (20),
}, ...__VLS_functionalComponentArgsRest(__VLS_11));
__VLS_13.slots.default;
const __VLS_14 = {}.ElCol;
/** @type {[typeof __VLS_components.ElCol, typeof __VLS_components.elCol, typeof __VLS_components.ElCol, typeof __VLS_components.elCol, ]} */ ;
// @ts-ignore
const __VLS_15 = __VLS_asFunctionalComponent(__VLS_14, new __VLS_14({
    span: (12),
}));
const __VLS_16 = __VLS_15({
    span: (12),
}, ...__VLS_functionalComponentArgsRest(__VLS_15));
__VLS_17.slots.default;
const __VLS_18 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_19 = __VLS_asFunctionalComponent(__VLS_18, new __VLS_18({
    label: "实体编码",
    prop: "entityCode",
}));
const __VLS_20 = __VLS_19({
    label: "实体编码",
    prop: "entityCode",
}, ...__VLS_functionalComponentArgsRest(__VLS_19));
__VLS_21.slots.default;
const __VLS_22 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_23 = __VLS_asFunctionalComponent(__VLS_22, new __VLS_22({
    modelValue: (__VLS_ctx.form.entityCode),
    disabled: (__VLS_ctx.formType === 'edit'),
    placeholder: "请输入实体编码，如：Customer",
}));
const __VLS_24 = __VLS_23({
    modelValue: (__VLS_ctx.form.entityCode),
    disabled: (__VLS_ctx.formType === 'edit'),
    placeholder: "请输入实体编码，如：Customer",
}, ...__VLS_functionalComponentArgsRest(__VLS_23));
var __VLS_21;
var __VLS_17;
const __VLS_26 = {}.ElCol;
/** @type {[typeof __VLS_components.ElCol, typeof __VLS_components.elCol, typeof __VLS_components.ElCol, typeof __VLS_components.elCol, ]} */ ;
// @ts-ignore
const __VLS_27 = __VLS_asFunctionalComponent(__VLS_26, new __VLS_26({
    span: (12),
}));
const __VLS_28 = __VLS_27({
    span: (12),
}, ...__VLS_functionalComponentArgsRest(__VLS_27));
__VLS_29.slots.default;
const __VLS_30 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_31 = __VLS_asFunctionalComponent(__VLS_30, new __VLS_30({
    label: "实体名称",
    prop: "entityName",
}));
const __VLS_32 = __VLS_31({
    label: "实体名称",
    prop: "entityName",
}, ...__VLS_functionalComponentArgsRest(__VLS_31));
__VLS_33.slots.default;
const __VLS_34 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_35 = __VLS_asFunctionalComponent(__VLS_34, new __VLS_34({
    modelValue: (__VLS_ctx.form.entityName),
    placeholder: "请输入实体名称，如：客户",
}));
const __VLS_36 = __VLS_35({
    modelValue: (__VLS_ctx.form.entityName),
    placeholder: "请输入实体名称，如：客户",
}, ...__VLS_functionalComponentArgsRest(__VLS_35));
var __VLS_33;
var __VLS_29;
var __VLS_13;
const __VLS_38 = {}.ElRow;
/** @type {[typeof __VLS_components.ElRow, typeof __VLS_components.elRow, typeof __VLS_components.ElRow, typeof __VLS_components.elRow, ]} */ ;
// @ts-ignore
const __VLS_39 = __VLS_asFunctionalComponent(__VLS_38, new __VLS_38({
    gutter: (20),
}));
const __VLS_40 = __VLS_39({
    gutter: (20),
}, ...__VLS_functionalComponentArgsRest(__VLS_39));
__VLS_41.slots.default;
const __VLS_42 = {}.ElCol;
/** @type {[typeof __VLS_components.ElCol, typeof __VLS_components.elCol, typeof __VLS_components.ElCol, typeof __VLS_components.elCol, ]} */ ;
// @ts-ignore
const __VLS_43 = __VLS_asFunctionalComponent(__VLS_42, new __VLS_42({
    span: (12),
}));
const __VLS_44 = __VLS_43({
    span: (12),
}, ...__VLS_functionalComponentArgsRest(__VLS_43));
__VLS_45.slots.default;
const __VLS_46 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_47 = __VLS_asFunctionalComponent(__VLS_46, new __VLS_46({
    label: "表名",
    prop: "tableName",
}));
const __VLS_48 = __VLS_47({
    label: "表名",
    prop: "tableName",
}, ...__VLS_functionalComponentArgsRest(__VLS_47));
__VLS_49.slots.default;
const __VLS_50 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_51 = __VLS_asFunctionalComponent(__VLS_50, new __VLS_50({
    modelValue: (__VLS_ctx.form.tableName),
    placeholder: "请输入数据库表名，如：lc_customer",
}));
const __VLS_52 = __VLS_51({
    modelValue: (__VLS_ctx.form.tableName),
    placeholder: "请输入数据库表名，如：lc_customer",
}, ...__VLS_functionalComponentArgsRest(__VLS_51));
var __VLS_49;
var __VLS_45;
const __VLS_54 = {}.ElCol;
/** @type {[typeof __VLS_components.ElCol, typeof __VLS_components.elCol, typeof __VLS_components.ElCol, typeof __VLS_components.elCol, ]} */ ;
// @ts-ignore
const __VLS_55 = __VLS_asFunctionalComponent(__VLS_54, new __VLS_54({
    span: (12),
}));
const __VLS_56 = __VLS_55({
    span: (12),
}, ...__VLS_functionalComponentArgsRest(__VLS_55));
__VLS_57.slots.default;
const __VLS_58 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_59 = __VLS_asFunctionalComponent(__VLS_58, new __VLS_58({
    label: "描述",
    prop: "description",
}));
const __VLS_60 = __VLS_59({
    label: "描述",
    prop: "description",
}, ...__VLS_functionalComponentArgsRest(__VLS_59));
__VLS_61.slots.default;
const __VLS_62 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_63 = __VLS_asFunctionalComponent(__VLS_62, new __VLS_62({
    modelValue: (__VLS_ctx.form.description),
    placeholder: "请输入实体描述",
}));
const __VLS_64 = __VLS_63({
    modelValue: (__VLS_ctx.form.description),
    placeholder: "请输入实体描述",
}, ...__VLS_functionalComponentArgsRest(__VLS_63));
var __VLS_61;
var __VLS_57;
var __VLS_41;
const __VLS_66 = {}.ElDivider;
/** @type {[typeof __VLS_components.ElDivider, typeof __VLS_components.elDivider, typeof __VLS_components.ElDivider, typeof __VLS_components.elDivider, ]} */ ;
// @ts-ignore
const __VLS_67 = __VLS_asFunctionalComponent(__VLS_66, new __VLS_66({}));
const __VLS_68 = __VLS_67({}, ...__VLS_functionalComponentArgsRest(__VLS_67));
__VLS_69.slots.default;
var __VLS_69;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "field-list-toolbar" },
});
const __VLS_70 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_71 = __VLS_asFunctionalComponent(__VLS_70, new __VLS_70({
    ...{ 'onClick': {} },
    type: "primary",
}));
const __VLS_72 = __VLS_71({
    ...{ 'onClick': {} },
    type: "primary",
}, ...__VLS_functionalComponentArgsRest(__VLS_71));
let __VLS_74;
let __VLS_75;
let __VLS_76;
const __VLS_77 = {
    onClick: (__VLS_ctx.addField)
};
__VLS_73.slots.default;
const __VLS_78 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
const __VLS_79 = __VLS_asFunctionalComponent(__VLS_78, new __VLS_78({}));
const __VLS_80 = __VLS_79({}, ...__VLS_functionalComponentArgsRest(__VLS_79));
__VLS_81.slots.default;
const __VLS_82 = {}.Plus;
/** @type {[typeof __VLS_components.Plus, ]} */ ;
// @ts-ignore
const __VLS_83 = __VLS_asFunctionalComponent(__VLS_82, new __VLS_82({}));
const __VLS_84 = __VLS_83({}, ...__VLS_functionalComponentArgsRest(__VLS_83));
var __VLS_81;
var __VLS_73;
const __VLS_86 = {}.ElTable;
/** @type {[typeof __VLS_components.ElTable, typeof __VLS_components.elTable, typeof __VLS_components.ElTable, typeof __VLS_components.elTable, ]} */ ;
// @ts-ignore
const __VLS_87 = __VLS_asFunctionalComponent(__VLS_86, new __VLS_86({
    data: (__VLS_ctx.form.fields),
    border: true,
    ...{ style: {} },
}));
const __VLS_88 = __VLS_87({
    data: (__VLS_ctx.form.fields),
    border: true,
    ...{ style: {} },
}, ...__VLS_functionalComponentArgsRest(__VLS_87));
__VLS_89.slots.default;
const __VLS_90 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_91 = __VLS_asFunctionalComponent(__VLS_90, new __VLS_90({
    type: "index",
    width: "50",
    label: "#",
}));
const __VLS_92 = __VLS_91({
    type: "index",
    width: "50",
    label: "#",
}, ...__VLS_functionalComponentArgsRest(__VLS_91));
const __VLS_94 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_95 = __VLS_asFunctionalComponent(__VLS_94, new __VLS_94({
    label: "字段编码",
    minWidth: "120",
}));
const __VLS_96 = __VLS_95({
    label: "字段编码",
    minWidth: "120",
}, ...__VLS_functionalComponentArgsRest(__VLS_95));
__VLS_97.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_97.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_98 = {}.ElFormItem;
    /** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
    // @ts-ignore
    const __VLS_99 = __VLS_asFunctionalComponent(__VLS_98, new __VLS_98({
        prop: ('fields.' + scope.$index + '.fieldCode'),
        rules: (__VLS_ctx.fieldRules.fieldCode),
        ...{ class: "no-margin" },
    }));
    const __VLS_100 = __VLS_99({
        prop: ('fields.' + scope.$index + '.fieldCode'),
        rules: (__VLS_ctx.fieldRules.fieldCode),
        ...{ class: "no-margin" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_99));
    __VLS_101.slots.default;
    const __VLS_102 = {}.ElInput;
    /** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
    // @ts-ignore
    const __VLS_103 = __VLS_asFunctionalComponent(__VLS_102, new __VLS_102({
        modelValue: (scope.row.fieldCode),
        placeholder: "如：name",
    }));
    const __VLS_104 = __VLS_103({
        modelValue: (scope.row.fieldCode),
        placeholder: "如：name",
    }, ...__VLS_functionalComponentArgsRest(__VLS_103));
    var __VLS_101;
}
var __VLS_97;
const __VLS_106 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_107 = __VLS_asFunctionalComponent(__VLS_106, new __VLS_106({
    label: "字段名称",
    minWidth: "120",
}));
const __VLS_108 = __VLS_107({
    label: "字段名称",
    minWidth: "120",
}, ...__VLS_functionalComponentArgsRest(__VLS_107));
__VLS_109.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_109.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_110 = {}.ElFormItem;
    /** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
    // @ts-ignore
    const __VLS_111 = __VLS_asFunctionalComponent(__VLS_110, new __VLS_110({
        prop: ('fields.' + scope.$index + '.fieldName'),
        rules: (__VLS_ctx.fieldRules.fieldName),
        ...{ class: "no-margin" },
    }));
    const __VLS_112 = __VLS_111({
        prop: ('fields.' + scope.$index + '.fieldName'),
        rules: (__VLS_ctx.fieldRules.fieldName),
        ...{ class: "no-margin" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_111));
    __VLS_113.slots.default;
    const __VLS_114 = {}.ElInput;
    /** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
    // @ts-ignore
    const __VLS_115 = __VLS_asFunctionalComponent(__VLS_114, new __VLS_114({
        modelValue: (scope.row.fieldName),
        placeholder: "如：姓名",
    }));
    const __VLS_116 = __VLS_115({
        modelValue: (scope.row.fieldName),
        placeholder: "如：姓名",
    }, ...__VLS_functionalComponentArgsRest(__VLS_115));
    var __VLS_113;
}
var __VLS_109;
const __VLS_118 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_119 = __VLS_asFunctionalComponent(__VLS_118, new __VLS_118({
    label: "字段描述",
    minWidth: "120",
}));
const __VLS_120 = __VLS_119({
    label: "字段描述",
    minWidth: "120",
}, ...__VLS_functionalComponentArgsRest(__VLS_119));
__VLS_121.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_121.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_122 = {}.ElFormItem;
    /** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
    // @ts-ignore
    const __VLS_123 = __VLS_asFunctionalComponent(__VLS_122, new __VLS_122({
        prop: ('fields.' + scope.$index + '.description'),
        ...{ class: "no-margin" },
    }));
    const __VLS_124 = __VLS_123({
        prop: ('fields.' + scope.$index + '.description'),
        ...{ class: "no-margin" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_123));
    __VLS_125.slots.default;
    const __VLS_126 = {}.ElInput;
    /** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
    // @ts-ignore
    const __VLS_127 = __VLS_asFunctionalComponent(__VLS_126, new __VLS_126({
        modelValue: (scope.row.description),
        placeholder: "如：身份证号",
    }));
    const __VLS_128 = __VLS_127({
        modelValue: (scope.row.description),
        placeholder: "如：身份证号",
    }, ...__VLS_functionalComponentArgsRest(__VLS_127));
    var __VLS_125;
}
var __VLS_121;
const __VLS_130 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_131 = __VLS_asFunctionalComponent(__VLS_130, new __VLS_130({
    label: "数据类型",
    width: "120",
}));
const __VLS_132 = __VLS_131({
    label: "数据类型",
    width: "120",
}, ...__VLS_functionalComponentArgsRest(__VLS_131));
__VLS_133.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_133.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_134 = {}.ElFormItem;
    /** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
    // @ts-ignore
    const __VLS_135 = __VLS_asFunctionalComponent(__VLS_134, new __VLS_134({
        prop: ('fields.' + scope.$index + '.dataType'),
        rules: (__VLS_ctx.fieldRules.dataType),
        ...{ class: "no-margin" },
    }));
    const __VLS_136 = __VLS_135({
        prop: ('fields.' + scope.$index + '.dataType'),
        rules: (__VLS_ctx.fieldRules.dataType),
        ...{ class: "no-margin" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_135));
    __VLS_137.slots.default;
    const __VLS_138 = {}.ElSelect;
    /** @type {[typeof __VLS_components.ElSelect, typeof __VLS_components.elSelect, typeof __VLS_components.ElSelect, typeof __VLS_components.elSelect, ]} */ ;
    // @ts-ignore
    const __VLS_139 = __VLS_asFunctionalComponent(__VLS_138, new __VLS_138({
        ...{ 'onChange': {} },
        modelValue: (scope.row.dataType),
        placeholder: "请选择",
        ...{ style: {} },
    }));
    const __VLS_140 = __VLS_139({
        ...{ 'onChange': {} },
        modelValue: (scope.row.dataType),
        placeholder: "请选择",
        ...{ style: {} },
    }, ...__VLS_functionalComponentArgsRest(__VLS_139));
    let __VLS_142;
    let __VLS_143;
    let __VLS_144;
    const __VLS_145 = {
        onChange: (...[$event]) => {
            __VLS_ctx.handleDataTypeChange(scope.row);
        }
    };
    __VLS_141.slots.default;
    for (const [item] of __VLS_getVForSourceType((__VLS_ctx.dataTypeOptions))) {
        const __VLS_146 = {}.ElOption;
        /** @type {[typeof __VLS_components.ElOption, typeof __VLS_components.elOption, ]} */ ;
        // @ts-ignore
        const __VLS_147 = __VLS_asFunctionalComponent(__VLS_146, new __VLS_146({
            key: (item.value),
            label: (item.label),
            value: (item.value),
        }));
        const __VLS_148 = __VLS_147({
            key: (item.value),
            label: (item.label),
            value: (item.value),
        }, ...__VLS_functionalComponentArgsRest(__VLS_147));
    }
    var __VLS_141;
    var __VLS_137;
}
var __VLS_133;
const __VLS_150 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_151 = __VLS_asFunctionalComponent(__VLS_150, new __VLS_150({
    label: "长度",
    width: "120",
}));
const __VLS_152 = __VLS_151({
    label: "长度",
    width: "120",
}, ...__VLS_functionalComponentArgsRest(__VLS_151));
__VLS_153.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_153.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_154 = {}.ElFormItem;
    /** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
    // @ts-ignore
    const __VLS_155 = __VLS_asFunctionalComponent(__VLS_154, new __VLS_154({
        prop: ('fields.' + scope.$index + '.length'),
        ...{ class: "no-margin" },
    }));
    const __VLS_156 = __VLS_155({
        prop: ('fields.' + scope.$index + '.length'),
        ...{ class: "no-margin" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_155));
    __VLS_157.slots.default;
    const __VLS_158 = {}.ElInputNumber;
    /** @type {[typeof __VLS_components.ElInputNumber, typeof __VLS_components.elInputNumber, ]} */ ;
    // @ts-ignore
    const __VLS_159 = __VLS_asFunctionalComponent(__VLS_158, new __VLS_158({
        modelValue: (scope.row.length),
        min: (0),
        disabled: (!['String', 'Text'].includes(scope.row.dataType)),
        controlsPosition: "right",
        ...{ style: {} },
    }));
    const __VLS_160 = __VLS_159({
        modelValue: (scope.row.length),
        min: (0),
        disabled: (!['String', 'Text'].includes(scope.row.dataType)),
        controlsPosition: "right",
        ...{ style: {} },
    }, ...__VLS_functionalComponentArgsRest(__VLS_159));
    var __VLS_157;
}
var __VLS_153;
const __VLS_162 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_163 = __VLS_asFunctionalComponent(__VLS_162, new __VLS_162({
    label: "必填",
    width: "70",
}));
const __VLS_164 = __VLS_163({
    label: "必填",
    width: "70",
}, ...__VLS_functionalComponentArgsRest(__VLS_163));
__VLS_165.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_165.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_166 = {}.ElCheckbox;
    /** @type {[typeof __VLS_components.ElCheckbox, typeof __VLS_components.elCheckbox, ]} */ ;
    // @ts-ignore
    const __VLS_167 = __VLS_asFunctionalComponent(__VLS_166, new __VLS_166({
        modelValue: (scope.row.required),
    }));
    const __VLS_168 = __VLS_167({
        modelValue: (scope.row.required),
    }, ...__VLS_functionalComponentArgsRest(__VLS_167));
}
var __VLS_165;
const __VLS_170 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_171 = __VLS_asFunctionalComponent(__VLS_170, new __VLS_170({
    label: "唯一",
    width: "70",
}));
const __VLS_172 = __VLS_171({
    label: "唯一",
    width: "70",
}, ...__VLS_functionalComponentArgsRest(__VLS_171));
__VLS_173.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_173.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_174 = {}.ElCheckbox;
    /** @type {[typeof __VLS_components.ElCheckbox, typeof __VLS_components.elCheckbox, ]} */ ;
    // @ts-ignore
    const __VLS_175 = __VLS_asFunctionalComponent(__VLS_174, new __VLS_174({
        modelValue: (scope.row.unique),
    }));
    const __VLS_176 = __VLS_175({
        modelValue: (scope.row.unique),
    }, ...__VLS_functionalComponentArgsRest(__VLS_175));
}
var __VLS_173;
const __VLS_178 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_179 = __VLS_asFunctionalComponent(__VLS_178, new __VLS_178({
    label: "列表显示",
    width: "90",
}));
const __VLS_180 = __VLS_179({
    label: "列表显示",
    width: "90",
}, ...__VLS_functionalComponentArgsRest(__VLS_179));
__VLS_181.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_181.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_182 = {}.ElCheckbox;
    /** @type {[typeof __VLS_components.ElCheckbox, typeof __VLS_components.elCheckbox, ]} */ ;
    // @ts-ignore
    const __VLS_183 = __VLS_asFunctionalComponent(__VLS_182, new __VLS_182({
        modelValue: (scope.row.showInList),
    }));
    const __VLS_184 = __VLS_183({
        modelValue: (scope.row.showInList),
    }, ...__VLS_functionalComponentArgsRest(__VLS_183));
}
var __VLS_181;
const __VLS_186 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_187 = __VLS_asFunctionalComponent(__VLS_186, new __VLS_186({
    label: "表单显示",
    width: "90",
}));
const __VLS_188 = __VLS_187({
    label: "表单显示",
    width: "90",
}, ...__VLS_functionalComponentArgsRest(__VLS_187));
__VLS_189.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_189.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_190 = {}.ElCheckbox;
    /** @type {[typeof __VLS_components.ElCheckbox, typeof __VLS_components.elCheckbox, ]} */ ;
    // @ts-ignore
    const __VLS_191 = __VLS_asFunctionalComponent(__VLS_190, new __VLS_190({
        modelValue: (scope.row.showInForm),
    }));
    const __VLS_192 = __VLS_191({
        modelValue: (scope.row.showInForm),
    }, ...__VLS_functionalComponentArgsRest(__VLS_191));
}
var __VLS_189;
const __VLS_194 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_195 = __VLS_asFunctionalComponent(__VLS_194, new __VLS_194({
    label: "可搜索",
    width: "80",
}));
const __VLS_196 = __VLS_195({
    label: "可搜索",
    width: "80",
}, ...__VLS_functionalComponentArgsRest(__VLS_195));
__VLS_197.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_197.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_198 = {}.ElCheckbox;
    /** @type {[typeof __VLS_components.ElCheckbox, typeof __VLS_components.elCheckbox, ]} */ ;
    // @ts-ignore
    const __VLS_199 = __VLS_asFunctionalComponent(__VLS_198, new __VLS_198({
        modelValue: (scope.row.searchable),
    }));
    const __VLS_200 = __VLS_199({
        modelValue: (scope.row.searchable),
    }, ...__VLS_functionalComponentArgsRest(__VLS_199));
}
var __VLS_197;
const __VLS_202 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_203 = __VLS_asFunctionalComponent(__VLS_202, new __VLS_202({
    label: "操作",
    width: "80",
}));
const __VLS_204 = __VLS_203({
    label: "操作",
    width: "80",
}, ...__VLS_functionalComponentArgsRest(__VLS_203));
__VLS_205.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_205.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_206 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_207 = __VLS_asFunctionalComponent(__VLS_206, new __VLS_206({
        ...{ 'onClick': {} },
        type: "danger",
        size: "small",
        link: true,
    }));
    const __VLS_208 = __VLS_207({
        ...{ 'onClick': {} },
        type: "danger",
        size: "small",
        link: true,
    }, ...__VLS_functionalComponentArgsRest(__VLS_207));
    let __VLS_210;
    let __VLS_211;
    let __VLS_212;
    const __VLS_213 = {
        onClick: (...[$event]) => {
            __VLS_ctx.removeField(scope.$index);
        }
    };
    __VLS_209.slots.default;
    var __VLS_209;
}
var __VLS_205;
var __VLS_89;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "form-actions" },
});
const __VLS_214 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_215 = __VLS_asFunctionalComponent(__VLS_214, new __VLS_214({
    ...{ 'onClick': {} },
}));
const __VLS_216 = __VLS_215({
    ...{ 'onClick': {} },
}, ...__VLS_functionalComponentArgsRest(__VLS_215));
let __VLS_218;
let __VLS_219;
let __VLS_220;
const __VLS_221 = {
    onClick: (__VLS_ctx.cancel)
};
__VLS_217.slots.default;
var __VLS_217;
const __VLS_222 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_223 = __VLS_asFunctionalComponent(__VLS_222, new __VLS_222({
    ...{ 'onClick': {} },
    type: "primary",
}));
const __VLS_224 = __VLS_223({
    ...{ 'onClick': {} },
    type: "primary",
}, ...__VLS_functionalComponentArgsRest(__VLS_223));
let __VLS_226;
let __VLS_227;
let __VLS_228;
const __VLS_229 = {
    onClick: (__VLS_ctx.submitForm)
};
__VLS_225.slots.default;
var __VLS_225;
var __VLS_3;
const __VLS_230 = {}.ElDialog;
/** @type {[typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, ]} */ ;
// @ts-ignore
const __VLS_231 = __VLS_asFunctionalComponent(__VLS_230, new __VLS_230({
    modelValue: (__VLS_ctx.sqlDialogVisible),
    title: "表结构变更SQL",
    width: "700px",
}));
const __VLS_232 = __VLS_231({
    modelValue: (__VLS_ctx.sqlDialogVisible),
    title: "表结构变更SQL",
    width: "700px",
}, ...__VLS_functionalComponentArgsRest(__VLS_231));
__VLS_233.slots.default;
const __VLS_234 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_235 = __VLS_asFunctionalComponent(__VLS_234, new __VLS_234({
    type: "textarea",
    modelValue: (__VLS_ctx.alterSql),
    rows: (8),
    ...{ style: {} },
}));
const __VLS_236 = __VLS_235({
    type: "textarea",
    modelValue: (__VLS_ctx.alterSql),
    rows: (8),
    ...{ style: {} },
}, ...__VLS_functionalComponentArgsRest(__VLS_235));
{
    const { footer: __VLS_thisSlot } = __VLS_233.slots;
    const __VLS_238 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_239 = __VLS_asFunctionalComponent(__VLS_238, new __VLS_238({
        ...{ 'onClick': {} },
    }));
    const __VLS_240 = __VLS_239({
        ...{ 'onClick': {} },
    }, ...__VLS_functionalComponentArgsRest(__VLS_239));
    let __VLS_242;
    let __VLS_243;
    let __VLS_244;
    const __VLS_245 = {
        onClick: (...[$event]) => {
            __VLS_ctx.sqlDialogVisible = false;
        }
    };
    __VLS_241.slots.default;
    var __VLS_241;
    const __VLS_246 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_247 = __VLS_asFunctionalComponent(__VLS_246, new __VLS_246({
        ...{ 'onClick': {} },
        type: "primary",
        loading: (__VLS_ctx.sqlExecLoading),
    }));
    const __VLS_248 = __VLS_247({
        ...{ 'onClick': {} },
        type: "primary",
        loading: (__VLS_ctx.sqlExecLoading),
    }, ...__VLS_functionalComponentArgsRest(__VLS_247));
    let __VLS_250;
    let __VLS_251;
    let __VLS_252;
    const __VLS_253 = {
        onClick: (__VLS_ctx.handleExecuteSql)
    };
    __VLS_249.slots.default;
    var __VLS_249;
}
var __VLS_233;
/** @type {__VLS_StyleScopedClasses['entity-form']} */ ;
/** @type {__VLS_StyleScopedClasses['field-list-toolbar']} */ ;
/** @type {__VLS_StyleScopedClasses['no-margin']} */ ;
/** @type {__VLS_StyleScopedClasses['no-margin']} */ ;
/** @type {__VLS_StyleScopedClasses['no-margin']} */ ;
/** @type {__VLS_StyleScopedClasses['no-margin']} */ ;
/** @type {__VLS_StyleScopedClasses['no-margin']} */ ;
/** @type {__VLS_StyleScopedClasses['form-actions']} */ ;
// @ts-ignore
var __VLS_5 = __VLS_4;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            dataTypeOptions: dataTypeOptions,
            formRef: formRef,
            form: form,
            rules: rules,
            fieldRules: fieldRules,
            sqlDialogVisible: sqlDialogVisible,
            alterSql: alterSql,
            sqlExecLoading: sqlExecLoading,
            addField: addField,
            removeField: removeField,
            handleDataTypeChange: handleDataTypeChange,
            submitForm: submitForm,
            handleExecuteSql: handleExecuteSql,
            cancel: cancel,
        };
    },
    emits: {},
    props: {
        entityData: {
            type: Object,
            default: () => null
        },
        formType: {
            type: String,
            default: 'add'
        }
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    emits: {},
    props: {
        entityData: {
            type: Object,
            default: () => null
        },
        formType: {
            type: String,
            default: 'add'
        }
    },
});
; /* PartiallyEnd: #4569/main.vue */
