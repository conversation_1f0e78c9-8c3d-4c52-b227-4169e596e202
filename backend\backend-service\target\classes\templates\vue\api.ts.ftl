import request from '../utils/request';

// 实体接口定义
export interface ${entityCode} {
  id?: number;
<#if fields??>
  <#list fields as field>
  ${field.name}${field.required?string("", "?")}:${field.tsType};
  </#list>
</#if>
  createTime?: string;
  updateTime?: string;
}

// 分页查询参数接口
export interface ${entityCode}PageQuery {
  pageNum?: number;
  pageSize?: number;
<#if fields??>
  <#list fields as field>
    <#if field.queryable?? && field.queryable>
  ${field.name}?: ${field.tsType};
    </#if>
  </#list>
</#if>
}

// 获取分页列表
export function get${entityCode}List(params: ${entityCode}PageQuery) {
  if (!params.pageNum) params.pageNum = 1;
  if (!params.pageSize) params.pageSize = 10;
  return request.get('/api/${apiPath}/page', { params });
}

// 获取所有数据
export function getAll${entityCode}s() {
  return request.get('/api/${apiPath}/list');
}

// 获取详情
export function get${entityCode}Detail(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.get(`/api/${apiPath}/${r"${id}"}`);
}

// 新增
export function save${entityCode}(data: ${entityCode}) {
  return request.post('/api/${apiPath}', data);
}

// 更新
export function update${entityCode}(id: number, data: ${entityCode}) {
  if (!id) return Promise.reject('ID不能为空');
  return request.put(`/api/${apiPath}/${r"${id}"}`, data);
}

// 删除
export function delete${entityCode}(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.delete(`/api/${apiPath}/${r"${id}"}`);
}

// 批量删除
export function batchDelete${entityCode}(ids: number[]) {
  if (!ids || ids.length === 0) return Promise.reject('ID列表不能为空');
  return request.delete('/api/${apiPath}/batch', { data: { ids } });
}

// 导出数据
export function export${entityCode}(params?: ${entityCode}PageQuery) {
  return request.get('/api/${apiPath}/export', { 
    params,
    responseType: 'blob'
  });
}