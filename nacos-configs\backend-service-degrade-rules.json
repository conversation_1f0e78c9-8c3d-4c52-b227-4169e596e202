[{"resource": "userController", "grade": 2, "count": 0.5, "timeWindow": 10, "minRequestAmount": 5, "slowRatioThreshold": 0.5, "statIntervalMs": 1000}, {"resource": "memberController", "grade": 0, "count": 1000, "timeWindow": 10, "minRequestAmount": 5, "slowRatioThreshold": 0.5, "statIntervalMs": 1000}, {"resource": "menuController", "grade": 1, "count": 5, "timeWindow": 10, "minRequestAmount": 3, "slowRatioThreshold": 0.5, "statIntervalMs": 1000}]