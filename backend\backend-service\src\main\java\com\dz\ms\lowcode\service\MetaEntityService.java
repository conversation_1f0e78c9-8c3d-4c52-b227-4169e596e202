package com.dz.ms.lowcode.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.lowcode.dto.MetaEntityDTO;
import com.dz.ms.lowcode.entity.MetaEntity;
import com.dz.ms.lowcode.dto.MetaFieldDTO;
import java.util.List;

/**
 * 元数据实体Service接口
 * <AUTHOR>
 * @date 2023-07-01
 */
public interface MetaEntityService extends IService<MetaEntity> {
    
    /**
     * 分页查询元数据实体
     * @param page 页码
     * @param size 每页数量
     * @param keyword 关键字
     * @return 分页结果
     */
    IPage<MetaEntity> page(Integer page, Integer size, String keyword);
    
    /**
     * 根据ID查询元数据实体（包含字段信息）
     * @param id 元数据实体ID
     * @return 元数据实体DTO
     */
    MetaEntityDTO getEntityById(Long id);
    
    /**
     * 保存元数据实体（包含字段信息）
     * @param entityDTO 元数据实体DTO
     * @return 保存后的元数据实体ID
     */
    Long saveEntity(MetaEntityDTO entityDTO);
    
    /**
     * 更新元数据实体（包含字段信息）
     * @param entityDTO 元数据实体DTO
     * @return 是否成功
     */
    boolean updateEntity(MetaEntityDTO entityDTO);
    
    /**
     * 删除元数据实体（包含字段信息）
     * @param id 元数据实体ID
     * @return 是否成功
     */
    boolean deleteEntity(Long id);
    
    /**
     * 发布元数据实体（生成实体表和代码）
     * @param id 元数据实体ID
     * @return 是否成功
     */
    boolean publishEntity(Long id);
    
    /**
     * 取消发布元数据实体
     * @param id 元数据实体ID
     * @return 是否成功
     */
    boolean unpublishEntity(Long id);
    
    /**
     * 只更新实体字段元数据（不更新实体本身）
     */
    void updateEntityFields(Long entityId, List<MetaFieldDTO> fields);
} 