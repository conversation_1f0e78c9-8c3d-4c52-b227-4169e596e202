/// <reference types="../../node_modules/.vue-global-types/vue_3.5_0_0_0.d.ts" />
import { ElSubMenu, ElMenuItem, ElIcon } from 'element-plus';
// 注册组件自身为递归组件
import { defineComponent } from 'vue';
export default await (async () => {
    const __VLS_props = defineProps({
        menu: {
            type: Object,
            required: true
        }
    });
    debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
    const __VLS_ctx = {};
    let __VLS_components;
    let __VLS_directives;
    if (__VLS_ctx.menu.type === 'M' && __VLS_ctx.menu.children && __VLS_ctx.menu.children.length > 0) {
        const __VLS_0 = {}.ElSubMenu;
        /** @type {[typeof __VLS_components.ElSubMenu, typeof __VLS_components.elSubMenu, typeof __VLS_components.ElSubMenu, typeof __VLS_components.elSubMenu, ]} */ ;
        // @ts-ignore
        const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
            index: (__VLS_ctx.menu.path || String(__VLS_ctx.menu.id)),
        }));
        const __VLS_2 = __VLS_1({
            index: (__VLS_ctx.menu.path || String(__VLS_ctx.menu.id)),
        }, ...__VLS_functionalComponentArgsRest(__VLS_1));
        __VLS_3.slots.default;
        {
            const { title: __VLS_thisSlot } = __VLS_3.slots;
            if (__VLS_ctx.menu.icon) {
                const __VLS_4 = {}.ElIcon;
                /** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
                // @ts-ignore
                const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({}));
                const __VLS_6 = __VLS_5({}, ...__VLS_functionalComponentArgsRest(__VLS_5));
                __VLS_7.slots.default;
                const __VLS_8 = ((__VLS_ctx.menu.icon));
                // @ts-ignore
                const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({}));
                const __VLS_10 = __VLS_9({}, ...__VLS_functionalComponentArgsRest(__VLS_9));
                var __VLS_7;
            }
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
            (__VLS_ctx.menu.name);
        }
        for (const [child] of __VLS_getVForSourceType((__VLS_ctx.menu.children))) {
            const __VLS_12 = {}.MenuItem;
            /** @type {[typeof __VLS_components.MenuItem, typeof __VLS_components.menuItem, ]} */ ;
            // @ts-ignore
            const __VLS_13 = __VLS_asFunctionalComponent(__VLS_12, new __VLS_12({
                menu: (child),
            }));
            const __VLS_14 = __VLS_13({
                menu: (child),
            }, ...__VLS_functionalComponentArgsRest(__VLS_13));
        }
        var __VLS_3;
    }
    else if (__VLS_ctx.menu.type === 'C') {
        const __VLS_16 = {}.ElMenuItem;
        /** @type {[typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, typeof __VLS_components.ElMenuItem, typeof __VLS_components.elMenuItem, ]} */ ;
        // @ts-ignore
        const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
            index: (__VLS_ctx.menu.path || String(__VLS_ctx.menu.id)),
        }));
        const __VLS_18 = __VLS_17({
            index: (__VLS_ctx.menu.path || String(__VLS_ctx.menu.id)),
        }, ...__VLS_functionalComponentArgsRest(__VLS_17));
        __VLS_19.slots.default;
        if (__VLS_ctx.menu.icon) {
            const __VLS_20 = {}.ElIcon;
            /** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
            // @ts-ignore
            const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({}));
            const __VLS_22 = __VLS_21({}, ...__VLS_functionalComponentArgsRest(__VLS_21));
            __VLS_23.slots.default;
            const __VLS_24 = ((__VLS_ctx.menu.icon));
            // @ts-ignore
            const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({}));
            const __VLS_26 = __VLS_25({}, ...__VLS_functionalComponentArgsRest(__VLS_25));
            var __VLS_23;
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
        (__VLS_ctx.menu.name);
        var __VLS_19;
    }
    var __VLS_dollars;
    const __VLS_self = (await import('vue')).defineComponent({
        setup() {
            return {
                ElSubMenu: ElSubMenu,
                ElMenuItem: ElMenuItem,
                ElIcon: ElIcon,
            };
        },
        props: {
            menu: {
                type: Object,
                required: true
            }
        },
        name: 'MenuItem'
    });
    return defineComponent({
        setup() {
            return {};
        },
        props: {
            menu: {
                type: Object,
                required: true
            }
        },
        name: 'MenuItem'
    });
})();
; /* PartiallyEnd: #4569/main.vue */
