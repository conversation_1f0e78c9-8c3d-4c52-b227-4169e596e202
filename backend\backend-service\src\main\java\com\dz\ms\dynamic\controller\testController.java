package com.dz.ms.dynamic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dz.ms.dynamic.entity.test;
import com.dz.ms.dynamic.service.testService;
import com.dz.ms.lowcode.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 测试控制器
 * 由低代码平台自动生成
 */
@RestController
@RequestMapping("/api/test")
@Tag(name = "测试管理", description = "测试相关接口")
public class testController {

    @Autowired
    private testService testService;

    /**
     * 分页查询
     * @param page 页码
     * @param size 每页数量
     * @param keyword 关键字
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询测试")
    public Result<IPage<test>> page(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        IPage<test> result = testService.page(page, size, keyword);
        return Result.success(result);
    }

    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 实体
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询测试")
    public Result<test> getById(@PathVariable Long id) {
        test entity = testService.getById(id);
        return entity != null ? Result.success(entity) : Result.error("数据不存在");
    }

    /**
     * 新增
     * @param entity 实体
     * @return 是否成功
     */
    @PostMapping
    @Operation(summary = "新增测试")
    public Result<Boolean> save(@RequestBody test entity) {
        boolean result = testService.save(entity);
        return Result.success(result);
    }

    /**
     * 修改
     * @param entity 实体
     * @return 是否成功
     */
    @PutMapping
    @Operation(summary = "修改测试")
    public Result<Boolean> update(@RequestBody test entity) {
        boolean result = testService.updateById(entity);
        return Result.success(result);
    }

    /**
     * 删除
     * @param id 主键ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除测试")
    public Result<Boolean> delete(@PathVariable Long id) {
        boolean result = testService.removeById(id);
        return Result.success(result);
    }
} 