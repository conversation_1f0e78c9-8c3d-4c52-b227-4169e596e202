package com.dz.ms.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 心愿单DTO
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@Data
public class WishDTO implements Serializable {
    /** 会员ID */
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    /** 心愿内容 */
    @NotBlank(message = "心愿内容不能为空")
    private String content;

    /** 状态（0-无效 1-有效） */
    @NotNull(message = "状态不能为空")
    private Integer status;
} 