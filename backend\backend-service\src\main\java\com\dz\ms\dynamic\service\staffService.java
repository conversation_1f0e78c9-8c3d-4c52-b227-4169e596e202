package com.dz.ms.dynamic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.dynamic.entity.staff;

/**
 * staff Service接口
 * 由低代码平台自动生成
 */
public interface staffService extends IService<staff> {
    
    /**
     * 分页查询
     * @param page 页码
     * @param size 每页数量
     * @param keyword 关键字
     * @return 分页结果
     */
    IPage<staff> page(Integer page, Integer size, String keyword);
} 