import request from '../utils/request';

// 实体和字段的接口定义
export interface MetaField {
  id?: number;
  entityId?: number;
  fieldCode: string;
  fieldName: string;
  columnName?: string;
  dataType: string;
  javaType?: string;
  length?: number;
  required?: boolean;
  unique?: boolean;
  showInList?: boolean;
  showInForm?: boolean;
  searchable?: boolean;
  description?: string;
}

export interface MetaEntity {
  id?: number;
  entityCode: string;
  entityName: string;
  tableName: string;
  description?: string;
  published?: boolean;
  enabled?: boolean;
  fields?: MetaField[];
  createTime?: string;
  updateTime?: string;
}

export interface EntityPageQuery {
  page?: number;
  size?: number;
  keyword?: string;
}

// 元数据实体相关接口
export function getEntityPage(params: EntityPageQuery) {
  return request.get('/lowcode/meta/entity/page', { params });
}

export function getEntityById(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.get(`/lowcode/meta/entity/${id}`);
}

export function saveEntity(data: MetaEntity) {
  return request.post('/lowcode/meta/entity', data);
}

export function updateEntity(data: MetaEntity) {
  if (!data.id) return Promise.reject('ID不能为空');
  return request.put('/lowcode/meta/entity', data);
}

export function deleteEntity(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.delete(`/lowcode/meta/entity/${id}`);
}

export function publishEntity(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.post(`/lowcode/meta/entity/${id}/publish`);
}

export function unpublishEntity(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.post(`/lowcode/meta/entity/${id}/unpublish`);
}

// 数据类型选项
export const dataTypeOptions = [
  { label: '字符串', value: 'String' },
  { label: '整数', value: 'Integer' },
  { label: '长整数', value: 'Long' },
  { label: '小数', value: 'Double' },
  { label: '布尔值', value: 'Boolean' },
  { label: '日期时间', value: 'Date' },
  { label: '大数字', value: 'BigDecimal' },
  { label: '文本', value: 'Text' }
];

// 获取Java类型映射
export function getJavaType(dataType: string): string {
  const map: { [key: string]: string } = {
    'String': 'String',
    'Integer': 'Integer',
    'Long': 'Long',
    'Double': 'Double',
    'Boolean': 'Boolean',
    'Date': 'Date',
    'BigDecimal': 'BigDecimal',
    'Text': 'String'
  };
  return map[dataType] || 'String';
}

// 生成表结构变更SQL
export function generateAlterSql(entityId: number, fields: MetaField[]) {
  return request.post('/lowcode/meta/entity/generate-alter-sql', {
    entityId,
    fields
  });
}

// 执行SQL
export function executeSql(sql: string) {
  return request.post('/lowcode/meta/entity/execute-sql', { sql });
} 