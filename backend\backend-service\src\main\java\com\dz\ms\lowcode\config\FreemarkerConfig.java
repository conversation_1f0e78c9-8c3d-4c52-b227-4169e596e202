package com.dz.ms.lowcode.config;

import freemarker.template.Configuration;
import freemarker.template.TemplateExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;

/**
 * FreeMarker配置类
 * <AUTHOR>
 * @date 2023-07-01
 */
@Component
public class FreemarkerConfig {

    /**
     * 配置FreeMarker模板引擎
     * @return Configuration
     * @throws IOException 异常
     */
    @Bean
    @Primary
    public Configuration freemarkerConfiguration() throws IOException {
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_31);
        
        // 设置模板目录
        File templateDir = new File("src/main/resources/templates");
        if (!templateDir.exists()) {
            templateDir.mkdirs();
        }
        cfg.setDirectoryForTemplateLoading(templateDir);
        
        // 设置默认编码
        cfg.setDefaultEncoding("UTF-8");
        
        // 设置模板异常处理器
        cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        
        // 不使用本地化查找
        cfg.setLocalizedLookup(false);
        
        return cfg;
    }
} 