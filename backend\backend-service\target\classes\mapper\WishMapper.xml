<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dz.ms.mapper.WishMapper">
    <resultMap id="BaseResultMap" type="com.dz.ms.entity.Wish">
        <id property="id" column="id" />
        <result property="memberId" column="member_id" />
        <result property="content" column="content" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="status" column="status" />
    </resultMap>
    
    <resultMap id="WishVOResultMap" type="com.dz.ms.vo.WishVO">
        <id property="id" column="id" />
        <result property="memberId" column="member_id" />
        <result property="memberUsername" column="username" />
        <result property="memberNickname" column="nickname" />
        <result property="content" column="content" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="status" column="status" />
    </resultMap>
    
    <select id="pageWishWithMember" resultMap="WishVOResultMap">
        SELECT 
            w.id,
            w.member_id,
            w.content,
            w.create_time,
            w.update_time,
            w.status,
            m.username,
            m.nickname
        FROM 
            wish w
        LEFT JOIN 
            member m ON w.member_id = m.id
        <where>
            <if test="memberId != null">
                AND w.member_id = #{memberId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (m.username LIKE CONCAT('%', #{keyword}, '%') OR m.nickname LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null">
                AND w.status = #{status}
            </if>
        </where>
        ORDER BY 
            w.create_time DESC
    </select>
</mapper> 