-- RTM 系统测试数据初始化脚本
-- 注意：请先执行 init.sql 创建表结构

USE rtm;

-- 1. 插入管理员用户 (密码: admin，BCrypt加密)
INSERT INTO user (username, password, nickname, email, phone, status) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR8PQM6uOUYxjzJGfqfJb.a', '系统管理员', '<EMAIL>', '13800138000', 1);

-- 2. 插入管理员角色
INSERT INTO role (name, code, description, status) VALUES 
('超级管理员', 'ADMIN', '系统超级管理员，拥有所有权限', 1);

-- 3. 插入基础菜单
-- 3.1 欢迎页
INSERT INTO menu (parent_id, name, path, perms, type, icon, order_num, status) VALUES 
(0, '欢迎页', '/welcome', 'welcome:view', 1, 'House', 1, 1);

-- 3.2 系统管理主菜单
INSERT INTO menu (parent_id, name, path, perms, type, icon, order_num, status) VALUES 
(0, '系统管理', '/system', 'system', 1, 'Setting', 10, 1);

-- 获取系统管理菜单ID
SET @systemMenuId = LAST_INSERT_ID();

-- 3.3 用户管理
INSERT INTO menu (parent_id, name, path, perms, type, icon, order_num, status) VALUES 
(@systemMenuId, '用户管理', '/system/user', 'user:view', 1, 'User', 1, 1);

-- 3.4 角色管理
INSERT INTO menu (parent_id, name, path, perms, type, icon, order_num, status) VALUES 
(@systemMenuId, '角色管理', '/system/role', 'role:view', 1, 'UserFilled', 2, 1);

-- 3.5 菜单管理
INSERT INTO menu (parent_id, name, path, perms, type, icon, order_num, status) VALUES 
(@systemMenuId, '菜单管理', '/system/menu', 'menu:view', 1, 'Menu', 3, 1);

-- 4. 关联管理员用户和角色
INSERT INTO user_role (user_id, role_id) VALUES 
((SELECT id FROM user WHERE username = 'admin'), (SELECT id FROM role WHERE code = 'ADMIN'));

-- 5. 关联管理员角色和所有菜单
INSERT INTO role_menu (role_id, menu_id) 
SELECT 
    (SELECT id FROM role WHERE code = 'ADMIN') as role_id,
    id as menu_id 
FROM menu;

-- 6. 插入测试会员数据
INSERT INTO member (username, password, nickname, phone, email, status) VALUES 
('testmember', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR8PQM6uOUYxjzJGfqfJb.a', '测试会员', '13900139000', '<EMAIL>', 1);

-- 7. 插入测试心愿数据
INSERT INTO wish (member_id, content, status) VALUES 
((SELECT id FROM member WHERE username = 'testmember'), '希望系统功能完善', 1),
((SELECT id FROM member WHERE username = 'testmember'), '希望界面更加美观', 1);

-- 查询验证数据
SELECT '=== 用户数据 ===' as info;
SELECT id, username, nickname, status FROM user;

SELECT '=== 角色数据 ===' as info;
SELECT id, name, code, status FROM role;

SELECT '=== 菜单数据 ===' as info;
SELECT id, parent_id, name, path, perms, type, order_num FROM menu ORDER BY parent_id, order_num;

SELECT '=== 用户角色关联 ===' as info;
SELECT ur.id, u.username, r.name as role_name 
FROM user_role ur 
JOIN user u ON ur.user_id = u.id 
JOIN role r ON ur.role_id = r.id;

SELECT '=== 角色菜单关联 ===' as info;
SELECT rm.id, r.name as role_name, m.name as menu_name, m.perms 
FROM role_menu rm 
JOIN role r ON rm.role_id = r.id 
JOIN menu m ON rm.menu_id = m.id 
ORDER BY r.id, m.order_num;
