-- 元数据实体表
CREATE TABLE IF NOT EXISTS `lc_meta_entity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `entity_code` varchar(50) NOT NULL COMMENT '实体编码',
  `entity_name` varchar(50) NOT NULL COMMENT '实体名称',
  `description` varchar(255) DEFAULT NULL COMMENT '实体描述',
  `table_name` varchar(50) NOT NULL COMMENT '数据库表名',
  `is_published` tinyint(1) DEFAULT '0' COMMENT '是否已发布',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_entity_code` (`entity_code`),
  UNIQUE KEY `uk_table_name` (`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='元数据实体表';

-- 元数据字段表
CREATE TABLE IF NOT EXISTS `lc_meta_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `entity_id` bigint(20) NOT NULL COMMENT '所属实体ID',
  `field_code` varchar(50) NOT NULL COMMENT '字段编码',
  `field_name` varchar(50) NOT NULL COMMENT '字段名称',
  `description` varchar(255) DEFAULT NULL COMMENT '字段描述',
  `column_name` varchar(50) NOT NULL COMMENT '数据库字段名',
  `data_type` varchar(50) NOT NULL COMMENT '数据类型',
  `java_type` varchar(50) DEFAULT NULL COMMENT 'Java类型',
  `length` int(11) DEFAULT NULL COMMENT '字段长度',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必填',
  `is_unique` tinyint(1) DEFAULT '0' COMMENT '是否唯一',
  `default_value` varchar(255) DEFAULT NULL COMMENT '默认值',
  `validation_rules` varchar(500) DEFAULT NULL COMMENT '验证规则(JSON格式)',
  `show_in_list` tinyint(1) DEFAULT '1' COMMENT '是否在列表中显示',
  `show_in_form` tinyint(1) DEFAULT '1' COMMENT '是否在表单中显示',
  `is_searchable` tinyint(1) DEFAULT '0' COMMENT '是否可搜索',
  `sort_no` int(11) DEFAULT '0' COMMENT '排序号',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_entity_field` (`entity_id`,`field_code`),
  KEY `idx_entity_id` (`entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='元数据字段表'; 