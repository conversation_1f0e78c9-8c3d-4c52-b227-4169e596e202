<template>
  <div class="my-account-container">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="account-card">
          <template #header>
            <div class="card-header">
              <span>账号信息</span>
            </div>
          </template>
          <div class="account-info">
            <el-skeleton :rows="4" animated :loading="loading">
              <template #default>
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
                  <el-descriptions-item label="昵称">{{ userInfo.nickname || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="角色">
                    <el-tag v-for="role in userRoles" :key="role.id" class="role-tag">{{ role.name }}</el-tag>
                    <span v-if="!userRoles.length">-</span>
                  </el-descriptions-item>
                  <el-descriptions-item label="状态">
                    <el-tag :type="userInfo.status === 1 ? 'success' : 'info'">
                      {{ userInfo.status === 1 ? '正常' : '禁用' }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="手机号">{{ userInfo.phone || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="邮箱">{{ userInfo.email || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间">{{ formatDate(userInfo.createTime) }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-skeleton>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="edit-card">
          <template #header>
            <div class="card-header">
              <span>修改信息</span>
            </div>
          </template>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本信息" name="basic">
              <el-form 
                ref="basicFormRef" 
                :model="basicForm" 
                :rules="basicRules" 
                label-width="80px" 
                status-icon
              >
                <el-form-item label="昵称" prop="nickname">
                  <el-input v-model="basicForm.nickname" placeholder="请输入昵称" />
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="basicForm.phone" placeholder="请输入手机号" />
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="basicForm.email" placeholder="请输入邮箱" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="updateBasicInfo" :loading="basicSubmitting">保存修改</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            
            <el-tab-pane label="修改密码" name="password">
              <el-form 
                ref="passwordFormRef" 
                :model="passwordForm" 
                :rules="passwordRules" 
                label-width="100px"
                status-icon
              >
                <el-form-item label="原密码" prop="oldPassword">
                  <el-input 
                    v-model="passwordForm.oldPassword" 
                    type="password" 
                    placeholder="请输入原密码"
                    show-password
                  />
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                  <el-input 
                    v-model="passwordForm.newPassword" 
                    type="password" 
                    placeholder="请输入新密码"
                    show-password
                  />
                </el-form-item>
                <el-form-item label="确认新密码" prop="confirmPassword">
                  <el-input 
                    v-model="passwordForm.confirmPassword" 
                    type="password" 
                    placeholder="请再次输入新密码"
                    show-password
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="updatePassword" :loading="passwordSubmitting">修改密码</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus';
import { getCurrentUserInfo, getCurrentUserRoles, updateUser, updatePassword as apiUpdatePassword } from '../../api/user';
import type { User } from '../../api/user';;
import type { Role } from '../../api/role';

// 用户信息
const userInfo = ref<User>({
  username: '',
  status: 0
});

// 用户角色
const userRoles = ref<Role[]>([]);

// 加载状态
const loading = ref(true);
const basicSubmitting = ref(false);
const passwordSubmitting = ref(false);

// 当前激活的标签页
const activeTab = ref('basic');

// 表单引用
const basicFormRef = ref<FormInstance>();
const passwordFormRef = ref<FormInstance>();

// 基本信息表单
const basicForm = reactive({
  nickname: '',
  phone: '',
  email: ''
});

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 基本信息表单验证规则
const basicRules = reactive<FormRules>({
  nickname: [
    { max: 30, message: '昵称长度不能超过30个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
});

// 密码表单验证规则
const passwordRules = reactive<FormRules>({
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});

// 格式化日期
function formatDate(dateStr?: string): string {
  if (!dateStr) return '-';
  try {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (e) {
    return dateStr;
  }
}

// 获取用户信息
async function fetchUserInfo() {
  try {
    loading.value = true;
    const res = await getCurrentUserInfo();
    if (res && res.code === 0 && res.data) {
      userInfo.value = res.data;
      
      // 填充基本信息表单
      basicForm.nickname = res.data.nickname || '';
      basicForm.phone = res.data.phone || '';
      basicForm.email = res.data.email || '';
    } else {
      ElMessage.error(res?.msg || '获取用户信息失败');
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    ElMessage.error('获取用户信息失败');
  } finally {
    loading.value = false;
  }
}

// 获取用户角色
async function fetchUserRoles() {
  try {
    const res = await getCurrentUserRoles();
    if (res && res.code === 0 && res.data) {
      userRoles.value = res.data;
    } else {
      ElMessage.error(res?.msg || '获取用户角色失败');
    }
  } catch (error) {
    console.error('获取用户角色失败:', error);
    ElMessage.error('获取用户角色失败');
  }
}

// 更新基本信息
async function updateBasicInfo() {
  if (!basicFormRef.value) return;
  
  await basicFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    try {
      basicSubmitting.value = true;
      
      // 准备更新的数据
      const updateData = {
        ...userInfo.value,
        nickname: basicForm.nickname,
        phone: basicForm.phone,
        email: basicForm.email
      };
      
      // 确保有用户ID
      if (!updateData.id) {
        ElMessage.error('用户ID不存在，无法更新信息');
        return;
      }
      
      // 调用更新接口
      const res = await updateUser(updateData.id, updateData);
      
      if (res && res.code === 0) {
        ElMessage.success('基本信息更新成功');
        // 重新获取用户信息
        await fetchUserInfo();
      } else {
        ElMessage.error(res?.msg || '更新基本信息失败');
      }
    } catch (error) {
      console.error('更新基本信息失败:', error);
      ElMessage.error('更新基本信息失败');
    } finally {
      basicSubmitting.value = false;
    }
  });
}

// 更新密码
async function updatePassword() {
  if (!passwordFormRef.value) return;
  
  try {
    // 先进行表单验证
    const valid = await passwordFormRef.value.validate();
    if (!valid) return;
    
    console.log('表单验证通过，准备提交密码修改请求');
    passwordSubmitting.value = true;
    
    // 调用修改密码接口
    const res = await apiUpdatePassword({
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword
    });
    
    console.log('密码修改请求响应:', res);
    
    if (res && res.code === 0) {
      ElMessage.success('密码修改成功，请重新登录');
      // 清空表单
      passwordForm.oldPassword = '';
      passwordForm.newPassword = '';
      passwordForm.confirmPassword = '';
      // 可以选择跳转到登录页或其他操作
      // router.push('/login');
    } else {
      ElMessage.error(res?.msg || '密码修改失败');
    }
  } catch (error) {
    console.error('密码修改失败:', error);
    ElMessage.error('密码修改失败: ' + (error instanceof Error ? error.message : String(error)));
  } finally {
    passwordSubmitting.value = false;
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await fetchUserInfo();
  await fetchUserRoles();
});
</script>

<style scoped>
.my-account-container {
  padding: 20px;
}

.account-card,
.edit-card {
  margin-top: 20px;
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.account-info {
  margin-top: 20px;
}

.role-tag {
  margin-right: 5px;
}

.el-tabs {
  margin-top: 10px;
}
</style> 