import request from '../utils/request';

/**
 * 根据角色ID获取关联的菜单ID列表
 * @param roleId 角色ID
 * @returns 菜单ID列表的Promise
 */
export function getRoleMenuIds(roleId: number | string) {
  return request({
    url: `/role-menu/menuIds/${roleId}`,
    method: 'get',
  });
}

/**
 * 保存角色和菜单的关联关系
 * @param roleId 角色ID
 * @param menuIds 菜单ID列表
 * @returns 是否成功的Promise
 */
export function saveRoleMenus(roleId: number | string, menuIds: number[]) {
  return request({
    url: `/role-menu/save/${roleId}`,
    method: 'post',
    data: menuIds,
  });
} 