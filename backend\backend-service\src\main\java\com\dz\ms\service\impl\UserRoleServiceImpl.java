package com.dz.ms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.ms.entity.UserRole;
import com.dz.ms.mapper.UserRoleMapper;
import com.dz.ms.service.UserRoleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色关联Service实现类
 * <AUTHOR>
 * @date 2025-05-29
 * @version 1.0.0
 */
@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements UserRoleService {

    @Override
    public List<UserRole> getUserRoleByUserId(Long userId) {
        LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserRole::getUserId, userId);
        return list(queryWrapper);
    }

    @Override
    @Transactional // Ensure atomicity of delete and insert operations
    public boolean saveUserRoles(Long userId, List<Long> roleIds) {
        // First, delete existing user-role associations for this user
        LambdaUpdateWrapper<UserRole> deleteWrapper = new LambdaUpdateWrapper<>();
        deleteWrapper.eq(UserRole::getUserId, userId);
        remove(deleteWrapper);

        // Then, insert new associations
        if (roleIds != null && !roleIds.isEmpty()) {
            List<UserRole> userRolesToSave = roleIds.stream()
                    .map(roleId -> {
                        UserRole userRole = new UserRole();
                        userRole.setUserId(userId);
                        userRole.setRoleId(roleId);
                        return userRole;
                    })
                    .collect(Collectors.toList());
            return saveBatch(userRolesToSave);
        }

        return true; // Return true if no roles to save (effectively deleted all)
    }
} 