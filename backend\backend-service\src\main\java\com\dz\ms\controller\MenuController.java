package com.dz.ms.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;

import com.dz.ms.entity.Menu;
import com.dz.ms.service.MenuService;
import com.dz.ms.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 菜单管理
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
@RestController
@RequestMapping("/menu")
@Api(tags = "菜单管理")
@Validated
public class MenuController {

    @Autowired
    private MenuService menuService;

    @ApiOperation("获取菜单树")
    @GetMapping("/tree")
    public Result<List<Menu>> getMenuTree() {
        List<Menu> menuTree = menuService.getMenuTree();
        return Result.success(menuTree);
    }

    @ApiOperation("新增菜单")
    @PostMapping
    public Result<Boolean> add(@Valid @RequestBody Menu menu) {
        boolean saved = menuService.save(menu);
        return Result.success(saved);
    }

    @ApiOperation("编辑菜单")
    @PutMapping("/{id}")
    public Result<Boolean> update(@PathVariable Long id, @Valid @RequestBody Menu menu) {
        menu.setId(id);
        boolean updated = menuService.updateById(menu);
        return Result.success(updated);
    }

    @ApiOperation("删除菜单")
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        // TODO: Check if menu has children before deleting
        boolean removed = menuService.removeById(id);
        return Result.success(removed);
    }

    @ApiOperation("菜单详情")
    @GetMapping("/{id}")
    public Result<Menu> detail(@PathVariable Long id) {
        Menu menu = menuService.getById(id);
        return Result.success(menu);
    }
} 