# Nacos 中 Sentinel 规则配置指南

## 🎯 配置位置

在 Nacos 控制台的 **配置管理** → **配置列表** 中创建以下配置：

## 📋 需要创建的配置列表

### 1. 后端服务流控规则
- **Data ID**: `backend-service-flow-rules`
- **Group**: `SENTINEL_GROUP`
- **配置格式**: `JSON`

**配置内容**：
```json
[
  {
    "resource": "userController",
    "limitApp": "default",
    "grade": 1,
    "count": 10,
    "strategy": 0,
    "controlBehavior": 0,
    "clusterMode": false
  },
  {
    "resource": "memberController", 
    "limitApp": "default",
    "grade": 1,
    "count": 20,
    "strategy": 0,
    "controlBehavior": 0,
    "clusterMode": false
  },
  {
    "resource": "menuController",
    "limitApp": "default", 
    "grade": 1,
    "count": 50,
    "strategy": 0,
    "controlBehavior": 0,
    "clusterMode": false
  }
]
```

### 2. 后端服务熔断规则
- **Data ID**: `backend-service-degrade-rules`
- **Group**: `SENTINEL_GROUP`
- **配置格式**: `JSON`

**配置内容**：
```json
[
  {
    "resource": "userController",
    "grade": 2,
    "count": 0.5,
    "timeWindow": 10,
    "minRequestAmount": 5,
    "slowRatioThreshold": 0.5,
    "statIntervalMs": 1000
  },
  {
    "resource": "memberController",
    "grade": 0,
    "count": 1000,
    "timeWindow": 10,
    "minRequestAmount": 5,
    "slowRatioThreshold": 0.5,
    "statIntervalMs": 1000
  },
  {
    "resource": "menuController",
    "grade": 1,
    "count": 5,
    "timeWindow": 10,
    "minRequestAmount": 3,
    "slowRatioThreshold": 0.5,
    "statIntervalMs": 1000
  }
]
```

### 3. 后端服务系统规则
- **Data ID**: `backend-service-system-rules`
- **Group**: `SENTINEL_GROUP`
- **配置格式**: `JSON`

**配置内容**：
```json
[
  {
    "highestSystemLoad": 10.0,
    "avgRt": 2000,
    "maxThread": 200,
    "qps": 500,
    "highestCpuUsage": 0.8
  }
]
```

### 4. 网关流控规则
- **Data ID**: `gateway-service-gw-flow-rules`
- **Group**: `SENTINEL_GROUP`
- **配置格式**: `JSON`

**配置内容**：
```json
[
  {
    "resource": "backend-service",
    "resourceMode": 0,
    "grade": 1,
    "count": 100,
    "intervalSec": 1,
    "controlBehavior": 0,
    "burst": 0,
    "maxQueueingTimeMs": 500
  },
  {
    "resource": "user_api",
    "resourceMode": 1,
    "grade": 1,
    "count": 20,
    "intervalSec": 1,
    "controlBehavior": 0,
    "burst": 0,
    "maxQueueingTimeMs": 500
  },
  {
    "resource": "member_api",
    "resourceMode": 1,
    "grade": 1,
    "count": 30,
    "intervalSec": 1,
    "controlBehavior": 0,
    "burst": 0,
    "maxQueueingTimeMs": 500
  }
]
```

### 5. 网关API分组规则
- **Data ID**: `gateway-service-gw-api-group-rules`
- **Group**: `SENTINEL_GROUP`
- **配置格式**: `JSON`

**配置内容**：
```json
[
  {
    "apiName": "user_api",
    "predicateItems": [
      {
        "pattern": "/user/**",
        "matchStrategy": 1
      }
    ]
  },
  {
    "apiName": "member_api", 
    "predicateItems": [
      {
        "pattern": "/member/**",
        "matchStrategy": 1
      }
    ]
  },
  {
    "apiName": "menu_api",
    "predicateItems": [
      {
        "pattern": "/menu/**",
        "matchStrategy": 1
      }
    ]
  }
]
```

## 🔧 详细配置步骤

### 步骤 1：登录 Nacos 控制台
1. 访问 http://localhost:8848/nacos
2. 使用用户名 `nacos`，密码 `nacos` 登录

### 步骤 2：进入配置管理
1. 点击左侧菜单 **配置管理**
2. 点击 **配置列表**

### 步骤 3：创建配置
1. 点击右上角 **+** 按钮
2. 填写配置信息：
   - **Data ID**: 按上表填写
   - **Group**: `SENTINEL_GROUP`
   - **配置格式**: `JSON`
   - **配置内容**: 复制对应的 JSON 内容
3. 点击 **发布**

### 步骤 4：验证配置
1. 在配置列表中确认所有配置都已创建
2. 启动应用后，在 Sentinel 控制台中查看规则是否生效

## 🔄 动态调整规则

### 修改限流阈值示例
1. 在 Nacos 中找到 `backend-service-flow-rules` 配置
2. 点击 **编辑**
3. 修改 `count` 值（例如将用户接口从 10 改为 15）：
```json
{
  "resource": "userController",
  "limitApp": "default",
  "grade": 1,
  "count": 15,  // 从 10 改为 15
  "strategy": 0,
  "controlBehavior": 0,
  "clusterMode": false
}
```
4. 点击 **发布**
5. 规则会在几秒内自动生效，无需重启应用

## 📊 参数说明

### 流控规则参数
- **resource**: 资源名称
- **grade**: 限流阈值类型（1=QPS，0=线程数）
- **count**: 限流阈值
- **strategy**: 流控模式（0=直接，1=关联，2=链路）
- **controlBehavior**: 流控效果（0=快速失败，1=Warm Up，2=排队等待）

### 熔断规则参数
- **resource**: 资源名称
- **grade**: 熔断策略（0=慢调用比例，1=异常比例，2=异常数）
- **count**: 阈值
- **timeWindow**: 熔断时长（秒）
- **minRequestAmount**: 最小请求数

## 🎯 最佳实践

1. **分组管理**: 使用 `SENTINEL_GROUP` 统一管理 Sentinel 规则
2. **版本控制**: 在配置描述中记录变更原因和时间
3. **测试验证**: 修改规则后及时验证效果
4. **监控观察**: 通过 Sentinel 控制台观察规则效果
5. **备份配置**: 定期导出重要配置进行备份

## ⚠️ 注意事项

1. **配置格式**: 必须是有效的 JSON 格式
2. **Group 名称**: 必须与应用配置中的 `groupId` 一致
3. **Data ID**: 必须与应用配置中的 `dataId` 一致
4. **生效时间**: 配置修改后通常在 3-5 秒内生效
5. **应用重启**: 动态配置无需重启应用即可生效
