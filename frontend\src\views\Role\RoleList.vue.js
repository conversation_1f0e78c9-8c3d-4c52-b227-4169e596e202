/// <reference types="../../../node_modules/.vue-global-types/vue_3.5_0_0_0.d.ts" />
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getRolePage, addRole, updateRole, deleteRole, } from '../../api/role';
import { getRoleMenuIds, saveRoleMenus } from '../../api/role-menu';
import { getMenuTree } from '../../api/menu';
const searchForm = reactive({ name: '' });
const tableData = ref([]); // Replace 'any' with Role type later
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const form = reactive({ name: '', code: '', description: '' }); // Replace 'any' with Role type later
const formRef = ref();
// New state variables for menu assignment
const assignMenuDialogVisible = ref(false);
const currentRole = ref(null);
const menuTree = ref([]); // To store the menu tree data
const selectedMenuIds = ref([]); // To store selected menu IDs
const menuTreeRef = ref(null); // Ref for the ElTree component
const rules = {
    name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
    code: [{ required: true, message: '请输入角色编码', trigger: 'blur' }],
};
// Function to fetch role data
async function fetchData() {
    try {
        const params = {
            pageNum: pageNum.value,
            pageSize: pageSize.value,
            name: searchForm.name,
        };
        const res = await getRolePage(params);
        const { records, total: t } = res.data; // Assuming res.data has records and total
        tableData.value = records;
        total.value = t;
    }
    catch (error) {
        console.error("获取角色列表失败:", error);
        tableData.value = [];
        total.value = 0;
        ElMessage.error('获取角色列表失败');
    }
}
function openAdd() {
    dialogTitle.value = '新增角色';
    Object.assign(form, { id: undefined, name: '', code: '', description: '' });
    dialogVisible.value = true;
}
function openEdit(row) {
    dialogTitle.value = '编辑角色';
    Object.assign(form, row);
    dialogVisible.value = true;
}
async function handleSubmit() {
    formRef.value?.validate(async (valid) => {
        if (!valid)
            return;
        try {
            if (!form.id) {
                // Add role
                await addRole(form);
                ElMessage.success('新增成功');
            }
            else {
                // Update role
                await updateRole(form.id, form);
                ElMessage.success('编辑成功');
            }
            dialogVisible.value = false;
            fetchData();
        }
        catch (error) {
            console.error("提交角色信息失败:", error);
            ElMessage.error('操作失败');
        }
    });
}
async function handleDelete(id) {
    if (!id)
        return;
    ElMessageBox.confirm('确定要删除该角色吗？此操作不可恢复', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
        try {
            const res = await deleteRole(id);
            if (res && res.code === 0) {
                ElMessage.success('删除成功');
                fetchData();
            }
            else {
                ElMessage.error(res?.msg || '删除失败');
            }
        }
        catch (error) {
            console.error("删除角色失败:", error);
            ElMessage.error('删除失败');
        }
    })
        .catch(() => {
        // 用户取消删除操作
        ElMessage.info('已取消删除');
    });
}
// New methods to handle dialog open and close events
function handleAssignMenuDialogOpen() {
    // 弹窗打开时，加载数据
    if (currentRole.value) {
        loadRoleMenuData(currentRole.value);
    }
}
function handleAssignMenuDialogClose() {
    // 弹窗关闭时，重置树形控件状态
    if (menuTreeRef.value) {
        // 清空选中状态
        menuTreeRef.value.setCheckedKeys([]);
    }
    // 清空选中的菜单ID
    selectedMenuIds.value = [];
}
// 将原来handleAssignMenus方法中的数据加载逻辑抽取为单独的方法
async function loadRoleMenuData(role) {
    try {
        // 获取菜单树
        const menuTreeRes = await getMenuTree();
        console.log('menuTreeRes', menuTreeRes);
        if (menuTreeRes && menuTreeRes.code === 0 && menuTreeRes.data) {
            menuTree.value = menuTreeRes.data;
        }
        else {
            ElMessage.error(menuTreeRes.msg || '获取菜单树失败');
            menuTree.value = [];
        }
        // 获取角色已分配的菜单ID
        if (role.id) {
            const assignedMenuRes = await getRoleMenuIds(role.id);
            console.log('assignedMenuRes', assignedMenuRes);
            if (assignedMenuRes && assignedMenuRes.code === 0 && assignedMenuRes.data) {
                // 确保树形控件已经渲染完成后再设置选中状态
                setTimeout(() => {
                    selectedMenuIds.value = assignedMenuRes.data;
                    if (menuTreeRef.value) {
                        menuTreeRef.value.setCheckedKeys(assignedMenuRes.data);
                    }
                }, 100);
            }
            else {
                ElMessage.error(assignedMenuRes.msg || '获取已分配菜单失败');
                selectedMenuIds.value = [];
            }
        }
        else {
            selectedMenuIds.value = [];
        }
    }
    catch (error) {
        console.error("获取菜单数据失败:", error);
        ElMessage.error('获取菜单数据失败');
        menuTree.value = [];
        selectedMenuIds.value = [];
    }
}
// 修改handleAssignMenus方法
async function handleAssignMenus(row) {
    currentRole.value = row;
    assignMenuDialogVisible.value = true;
    // 数据加载逻辑已移至handleAssignMenuDialogOpen方法中
}
// New method to save assigned menus
async function saveAssignedMenus() {
    if (!currentRole.value || !currentRole.value.id) {
        ElMessage.warning('请选择要分配菜单的角色');
        return;
    }
    // Get checked menu keys from the tree (including half-checked nodes if needed)
    const checkedKeys = menuTreeRef.value.getCheckedKeys(); // Gets fully checked nodes
    // If you need half-checked nodes as well:
    // const halfCheckedKeys = menuTreeRef.value.getHalfCheckedKeys();
    // const menuIdsToSave = [...checkedKeys, ...halfCheckedKeys];
    const menuIdsToSave = checkedKeys; // Using only fully checked nodes for simplicity
    try {
        const res = await saveRoleMenus(currentRole.value.id, menuIdsToSave);
        if (res && res.code === 0) {
            ElMessage.success('分配菜单成功');
            assignMenuDialogVisible.value = false;
            // Optionally refresh the role list if needed
            // fetchData();
        }
        else {
            ElMessage.error(res.msg || '分配菜单失败');
        }
    }
    catch (error) {
        console.error("保存分配菜单失败:", error);
        ElMessage.error('保存分配菜单失败');
    }
}
onMounted(fetchData);
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['role-list']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "page-container" },
});
const __VLS_0 = {}.ElCard;
/** @type {[typeof __VLS_components.ElCard, typeof __VLS_components.elCard, typeof __VLS_components.ElCard, typeof __VLS_components.elCard, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({}));
const __VLS_2 = __VLS_1({}, ...__VLS_functionalComponentArgsRest(__VLS_1));
__VLS_3.slots.default;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ style: {} },
});
const __VLS_4 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
    modelValue: (__VLS_ctx.searchForm.name),
    placeholder: "搜索角色名称",
    ...{ style: {} },
    clearable: true,
}));
const __VLS_6 = __VLS_5({
    modelValue: (__VLS_ctx.searchForm.name),
    placeholder: "搜索角色名称",
    ...{ style: {} },
    clearable: true,
}, ...__VLS_functionalComponentArgsRest(__VLS_5));
const __VLS_8 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
    ...{ 'onClick': {} },
    type: "primary",
}));
const __VLS_10 = __VLS_9({
    ...{ 'onClick': {} },
    type: "primary",
}, ...__VLS_functionalComponentArgsRest(__VLS_9));
let __VLS_12;
let __VLS_13;
let __VLS_14;
const __VLS_15 = {
    onClick: (__VLS_ctx.fetchData)
};
__VLS_11.slots.default;
var __VLS_11;
const __VLS_16 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
    ...{ 'onClick': {} },
    type: "success",
}));
const __VLS_18 = __VLS_17({
    ...{ 'onClick': {} },
    type: "success",
}, ...__VLS_functionalComponentArgsRest(__VLS_17));
let __VLS_20;
let __VLS_21;
let __VLS_22;
const __VLS_23 = {
    onClick: (__VLS_ctx.openAdd)
};
__VLS_19.slots.default;
var __VLS_19;
const __VLS_24 = {}.ElTable;
/** @type {[typeof __VLS_components.ElTable, typeof __VLS_components.elTable, typeof __VLS_components.ElTable, typeof __VLS_components.elTable, ]} */ ;
// @ts-ignore
const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({
    data: (__VLS_ctx.tableData),
    ...{ style: {} },
}));
const __VLS_26 = __VLS_25({
    data: (__VLS_ctx.tableData),
    ...{ style: {} },
}, ...__VLS_functionalComponentArgsRest(__VLS_25));
__VLS_27.slots.default;
const __VLS_28 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
    prop: "id",
    label: "ID",
    width: "60",
}));
const __VLS_30 = __VLS_29({
    prop: "id",
    label: "ID",
    width: "60",
}, ...__VLS_functionalComponentArgsRest(__VLS_29));
const __VLS_32 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_33 = __VLS_asFunctionalComponent(__VLS_32, new __VLS_32({
    prop: "name",
    label: "角色名称",
}));
const __VLS_34 = __VLS_33({
    prop: "name",
    label: "角色名称",
}, ...__VLS_functionalComponentArgsRest(__VLS_33));
const __VLS_36 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_37 = __VLS_asFunctionalComponent(__VLS_36, new __VLS_36({
    prop: "description",
    label: "角色描述",
}));
const __VLS_38 = __VLS_37({
    prop: "description",
    label: "角色描述",
}, ...__VLS_functionalComponentArgsRest(__VLS_37));
const __VLS_40 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({
    prop: "createTime",
    label: "创建时间",
}));
const __VLS_42 = __VLS_41({
    prop: "createTime",
    label: "创建时间",
}, ...__VLS_functionalComponentArgsRest(__VLS_41));
const __VLS_44 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_45 = __VLS_asFunctionalComponent(__VLS_44, new __VLS_44({
    prop: "updateTime",
    label: "更新时间",
}));
const __VLS_46 = __VLS_45({
    prop: "updateTime",
    label: "更新时间",
}, ...__VLS_functionalComponentArgsRest(__VLS_45));
const __VLS_48 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_49 = __VLS_asFunctionalComponent(__VLS_48, new __VLS_48({
    label: "操作",
    width: "280",
}));
const __VLS_50 = __VLS_49({
    label: "操作",
    width: "280",
}, ...__VLS_functionalComponentArgsRest(__VLS_49));
__VLS_51.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_51.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_52 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_53 = __VLS_asFunctionalComponent(__VLS_52, new __VLS_52({
        ...{ 'onClick': {} },
        size: "small",
    }));
    const __VLS_54 = __VLS_53({
        ...{ 'onClick': {} },
        size: "small",
    }, ...__VLS_functionalComponentArgsRest(__VLS_53));
    let __VLS_56;
    let __VLS_57;
    let __VLS_58;
    const __VLS_59 = {
        onClick: (...[$event]) => {
            __VLS_ctx.openEdit(scope.row);
        }
    };
    __VLS_55.slots.default;
    var __VLS_55;
    const __VLS_60 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_61 = __VLS_asFunctionalComponent(__VLS_60, new __VLS_60({
        ...{ 'onClick': {} },
        size: "small",
        type: "danger",
    }));
    const __VLS_62 = __VLS_61({
        ...{ 'onClick': {} },
        size: "small",
        type: "danger",
    }, ...__VLS_functionalComponentArgsRest(__VLS_61));
    let __VLS_64;
    let __VLS_65;
    let __VLS_66;
    const __VLS_67 = {
        onClick: (...[$event]) => {
            __VLS_ctx.handleDelete(scope.row.id);
        }
    };
    __VLS_63.slots.default;
    var __VLS_63;
    const __VLS_68 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_69 = __VLS_asFunctionalComponent(__VLS_68, new __VLS_68({
        ...{ 'onClick': {} },
        size: "small",
    }));
    const __VLS_70 = __VLS_69({
        ...{ 'onClick': {} },
        size: "small",
    }, ...__VLS_functionalComponentArgsRest(__VLS_69));
    let __VLS_72;
    let __VLS_73;
    let __VLS_74;
    const __VLS_75 = {
        onClick: (...[$event]) => {
            __VLS_ctx.handleAssignMenus(scope.row);
        }
    };
    __VLS_71.slots.default;
    var __VLS_71;
}
var __VLS_51;
var __VLS_27;
const __VLS_76 = {}.ElPagination;
/** @type {[typeof __VLS_components.ElPagination, typeof __VLS_components.elPagination, ]} */ ;
// @ts-ignore
const __VLS_77 = __VLS_asFunctionalComponent(__VLS_76, new __VLS_76({
    ...{ 'onSizeChange': {} },
    ...{ 'onCurrentChange': {} },
    currentPage: (__VLS_ctx.pageNum),
    pageSize: (__VLS_ctx.pageSize),
    total: (__VLS_ctx.total),
    pageSizes: ([10, 20, 50]),
    layout: "total, sizes, prev, pager, next, jumper",
    ...{ style: {} },
}));
const __VLS_78 = __VLS_77({
    ...{ 'onSizeChange': {} },
    ...{ 'onCurrentChange': {} },
    currentPage: (__VLS_ctx.pageNum),
    pageSize: (__VLS_ctx.pageSize),
    total: (__VLS_ctx.total),
    pageSizes: ([10, 20, 50]),
    layout: "total, sizes, prev, pager, next, jumper",
    ...{ style: {} },
}, ...__VLS_functionalComponentArgsRest(__VLS_77));
let __VLS_80;
let __VLS_81;
let __VLS_82;
const __VLS_83 = {
    onSizeChange: (__VLS_ctx.fetchData)
};
const __VLS_84 = {
    onCurrentChange: (__VLS_ctx.fetchData)
};
var __VLS_79;
var __VLS_3;
const __VLS_85 = {}.ElDialog;
/** @type {[typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, ]} */ ;
// @ts-ignore
const __VLS_86 = __VLS_asFunctionalComponent(__VLS_85, new __VLS_85({
    modelValue: (__VLS_ctx.dialogVisible),
    title: (__VLS_ctx.dialogTitle),
    width: "400px",
}));
const __VLS_87 = __VLS_86({
    modelValue: (__VLS_ctx.dialogVisible),
    title: (__VLS_ctx.dialogTitle),
    width: "400px",
}, ...__VLS_functionalComponentArgsRest(__VLS_86));
__VLS_88.slots.default;
const __VLS_89 = {}.ElForm;
/** @type {[typeof __VLS_components.ElForm, typeof __VLS_components.elForm, typeof __VLS_components.ElForm, typeof __VLS_components.elForm, ]} */ ;
// @ts-ignore
const __VLS_90 = __VLS_asFunctionalComponent(__VLS_89, new __VLS_89({
    model: (__VLS_ctx.form),
    rules: (__VLS_ctx.rules),
    ref: "formRef",
    labelWidth: "80px",
}));
const __VLS_91 = __VLS_90({
    model: (__VLS_ctx.form),
    rules: (__VLS_ctx.rules),
    ref: "formRef",
    labelWidth: "80px",
}, ...__VLS_functionalComponentArgsRest(__VLS_90));
/** @type {typeof __VLS_ctx.formRef} */ ;
var __VLS_93 = {};
__VLS_92.slots.default;
const __VLS_95 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_96 = __VLS_asFunctionalComponent(__VLS_95, new __VLS_95({
    label: "角色名称",
    prop: "name",
}));
const __VLS_97 = __VLS_96({
    label: "角色名称",
    prop: "name",
}, ...__VLS_functionalComponentArgsRest(__VLS_96));
__VLS_98.slots.default;
const __VLS_99 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_100 = __VLS_asFunctionalComponent(__VLS_99, new __VLS_99({
    modelValue: (__VLS_ctx.form.name),
    autocomplete: "off",
}));
const __VLS_101 = __VLS_100({
    modelValue: (__VLS_ctx.form.name),
    autocomplete: "off",
}, ...__VLS_functionalComponentArgsRest(__VLS_100));
var __VLS_98;
const __VLS_103 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_104 = __VLS_asFunctionalComponent(__VLS_103, new __VLS_103({
    label: "角色编码",
    prop: "code",
}));
const __VLS_105 = __VLS_104({
    label: "角色编码",
    prop: "code",
}, ...__VLS_functionalComponentArgsRest(__VLS_104));
__VLS_106.slots.default;
const __VLS_107 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_108 = __VLS_asFunctionalComponent(__VLS_107, new __VLS_107({
    modelValue: (__VLS_ctx.form.code),
    autocomplete: "off",
}));
const __VLS_109 = __VLS_108({
    modelValue: (__VLS_ctx.form.code),
    autocomplete: "off",
}, ...__VLS_functionalComponentArgsRest(__VLS_108));
var __VLS_106;
const __VLS_111 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_112 = __VLS_asFunctionalComponent(__VLS_111, new __VLS_111({
    label: "角色描述",
    prop: "description",
}));
const __VLS_113 = __VLS_112({
    label: "角色描述",
    prop: "description",
}, ...__VLS_functionalComponentArgsRest(__VLS_112));
__VLS_114.slots.default;
const __VLS_115 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_116 = __VLS_asFunctionalComponent(__VLS_115, new __VLS_115({
    modelValue: (__VLS_ctx.form.description),
    type: "textarea",
}));
const __VLS_117 = __VLS_116({
    modelValue: (__VLS_ctx.form.description),
    type: "textarea",
}, ...__VLS_functionalComponentArgsRest(__VLS_116));
var __VLS_114;
var __VLS_92;
{
    const { footer: __VLS_thisSlot } = __VLS_88.slots;
    const __VLS_119 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_120 = __VLS_asFunctionalComponent(__VLS_119, new __VLS_119({
        ...{ 'onClick': {} },
    }));
    const __VLS_121 = __VLS_120({
        ...{ 'onClick': {} },
    }, ...__VLS_functionalComponentArgsRest(__VLS_120));
    let __VLS_123;
    let __VLS_124;
    let __VLS_125;
    const __VLS_126 = {
        onClick: (...[$event]) => {
            __VLS_ctx.dialogVisible = false;
        }
    };
    __VLS_122.slots.default;
    var __VLS_122;
    const __VLS_127 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_128 = __VLS_asFunctionalComponent(__VLS_127, new __VLS_127({
        ...{ 'onClick': {} },
        type: "primary",
    }));
    const __VLS_129 = __VLS_128({
        ...{ 'onClick': {} },
        type: "primary",
    }, ...__VLS_functionalComponentArgsRest(__VLS_128));
    let __VLS_131;
    let __VLS_132;
    let __VLS_133;
    const __VLS_134 = {
        onClick: (__VLS_ctx.handleSubmit)
    };
    __VLS_130.slots.default;
    var __VLS_130;
}
var __VLS_88;
const __VLS_135 = {}.ElDialog;
/** @type {[typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, ]} */ ;
// @ts-ignore
const __VLS_136 = __VLS_asFunctionalComponent(__VLS_135, new __VLS_135({
    ...{ 'onOpen': {} },
    ...{ 'onClose': {} },
    modelValue: (__VLS_ctx.assignMenuDialogVisible),
    title: "分配权限",
    width: "400px",
}));
const __VLS_137 = __VLS_136({
    ...{ 'onOpen': {} },
    ...{ 'onClose': {} },
    modelValue: (__VLS_ctx.assignMenuDialogVisible),
    title: "分配权限",
    width: "400px",
}, ...__VLS_functionalComponentArgsRest(__VLS_136));
let __VLS_139;
let __VLS_140;
let __VLS_141;
const __VLS_142 = {
    onOpen: (__VLS_ctx.handleAssignMenuDialogOpen)
};
const __VLS_143 = {
    onClose: (__VLS_ctx.handleAssignMenuDialogClose)
};
__VLS_138.slots.default;
const __VLS_144 = {}.ElTree;
/** @type {[typeof __VLS_components.ElTree, typeof __VLS_components.elTree, ]} */ ;
// @ts-ignore
const __VLS_145 = __VLS_asFunctionalComponent(__VLS_144, new __VLS_144({
    ref: "menuTreeRef",
    data: (__VLS_ctx.menuTree),
    showCheckbox: true,
    nodeKey: "id",
    defaultCheckedKeys: (__VLS_ctx.selectedMenuIds),
    props: ({ children: 'children', label: 'name' }),
}));
const __VLS_146 = __VLS_145({
    ref: "menuTreeRef",
    data: (__VLS_ctx.menuTree),
    showCheckbox: true,
    nodeKey: "id",
    defaultCheckedKeys: (__VLS_ctx.selectedMenuIds),
    props: ({ children: 'children', label: 'name' }),
}, ...__VLS_functionalComponentArgsRest(__VLS_145));
/** @type {typeof __VLS_ctx.menuTreeRef} */ ;
var __VLS_148 = {};
var __VLS_147;
{
    const { footer: __VLS_thisSlot } = __VLS_138.slots;
    const __VLS_150 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_151 = __VLS_asFunctionalComponent(__VLS_150, new __VLS_150({
        ...{ 'onClick': {} },
    }));
    const __VLS_152 = __VLS_151({
        ...{ 'onClick': {} },
    }, ...__VLS_functionalComponentArgsRest(__VLS_151));
    let __VLS_154;
    let __VLS_155;
    let __VLS_156;
    const __VLS_157 = {
        onClick: (...[$event]) => {
            __VLS_ctx.assignMenuDialogVisible = false;
        }
    };
    __VLS_153.slots.default;
    var __VLS_153;
    const __VLS_158 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_159 = __VLS_asFunctionalComponent(__VLS_158, new __VLS_158({
        ...{ 'onClick': {} },
        type: "primary",
    }));
    const __VLS_160 = __VLS_159({
        ...{ 'onClick': {} },
        type: "primary",
    }, ...__VLS_functionalComponentArgsRest(__VLS_159));
    let __VLS_162;
    let __VLS_163;
    let __VLS_164;
    const __VLS_165 = {
        onClick: (__VLS_ctx.saveAssignedMenus)
    };
    __VLS_161.slots.default;
    var __VLS_161;
}
var __VLS_138;
/** @type {__VLS_StyleScopedClasses['page-container']} */ ;
// @ts-ignore
var __VLS_94 = __VLS_93, __VLS_149 = __VLS_148;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            searchForm: searchForm,
            tableData: tableData,
            pageNum: pageNum,
            pageSize: pageSize,
            total: total,
            dialogVisible: dialogVisible,
            dialogTitle: dialogTitle,
            form: form,
            formRef: formRef,
            assignMenuDialogVisible: assignMenuDialogVisible,
            menuTree: menuTree,
            selectedMenuIds: selectedMenuIds,
            menuTreeRef: menuTreeRef,
            rules: rules,
            fetchData: fetchData,
            openAdd: openAdd,
            openEdit: openEdit,
            handleSubmit: handleSubmit,
            handleDelete: handleDelete,
            handleAssignMenuDialogOpen: handleAssignMenuDialogOpen,
            handleAssignMenuDialogClose: handleAssignMenuDialogClose,
            handleAssignMenus: handleAssignMenus,
            saveAssignedMenus: saveAssignedMenus,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
