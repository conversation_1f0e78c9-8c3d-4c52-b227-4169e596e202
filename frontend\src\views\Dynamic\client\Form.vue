<template>
  <div class="form-container">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="form"
    >
              <el-form-item label="客户名称" prop="name">
              <el-input
                v-model="formData.name"
                type="textarea"
                :rows="3"
                placeholder="请输入客户名称"
              />
        </el-form-item>
        <el-form-item label="执照编号" prop="zz_code">
              <el-input
                v-model="formData.zz_code"
                type="textarea"
                :rows="3"
                placeholder="请输入执照编号"
              />
        </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { saveclient, updateclient } from '@/api/client'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive(<client>{})

// 表单验证规则
const rules = reactive({
      name: [
        { required: true, message: '请输入客户名称', trigger: 'blur' }
      ],
      zz_code: [
        { required: true, message: '请输入执照编号', trigger: 'blur' }
      ]
})

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const isEdit = route.query.id
        const api = isEdit ? updateclient : saveclient
        const res = await api(formData)
        
        if (res.success) {
          ElMessage.success('保存成功')
          handleCancel()
        } else {
          ElMessage.error(res.message || '保存失败')
        }
      } catch (error) {
        ElMessage.error('操作失败')
      }
    }
  })
}

// 取消操作
const handleCancel = () => {
  router.back()
}

// 如果是编辑模式，加载数据
if (route.query.id) {
  // 这里需要调用获取详情的API
  // const res = await getclientDetail(route.query.id)
  // Object.assign(formData, res.data)
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}
.form {
  max-width: 800px;
  margin: 0 auto;
}
</style>