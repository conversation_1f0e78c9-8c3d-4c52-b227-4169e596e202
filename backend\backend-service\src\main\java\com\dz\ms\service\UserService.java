package com.dz.ms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.entity.User;
import com.dz.ms.entity.Role;
import java.util.List;

/**
 * 用户Service接口
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
public interface UserService extends IService<User> {

    boolean addUser(User user);
    boolean updateUser(User user);

    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户实体
     */
    User getByUsername(String username);
    
    /**
     * 获取用户的角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> getUserRoles(Long userId);
}