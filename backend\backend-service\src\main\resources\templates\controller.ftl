package ${basePackage}.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import ${basePackage}.entity.${entityCode};
import ${basePackage}.service.${entityCode}Service;
import com.dz.ms.lowcode.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * ${entityName}控制器
 * 由低代码平台自动生成
 */
@RestController
@RequestMapping("/api/${apiPath}")
@Tag(name = "${entityName}管理", description = "${entityName}相关接口")
public class ${entityCode}Controller {

    @Autowired
    private ${entityCode}Service ${entityCode?uncap_first}Service;

    /**
     * 分页查询
     * @param page 页码
     * @param size 每页数量
     * @param keyword 关键字
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询${entityName}")
    public Result<IPage<${entityCode}>> page(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        IPage<${entityCode}> result = ${entityCode?uncap_first}Service.page(page, size, keyword);
        return Result.success(result);
    }

    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 实体
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询${entityName}")
    public Result<${entityCode}> getById(@PathVariable Long id) {
        ${entityCode} entity = ${entityCode?uncap_first}Service.getById(id);
        return entity != null ? Result.success(entity) : Result.error("数据不存在");
    }

    /**
     * 新增
     * @param entity 实体
     * @return 是否成功
     */
    @PostMapping
    @Operation(summary = "新增${entityName}")
    public Result<Boolean> save(@RequestBody ${entityCode} entity) {
        boolean result = ${entityCode?uncap_first}Service.save(entity);
        return Result.success(result);
    }

    /**
     * 修改
     * @param entity 实体
     * @return 是否成功
     */
    @PutMapping
    @Operation(summary = "修改${entityName}")
    public Result<Boolean> update(@RequestBody ${entityCode} entity) {
        boolean result = ${entityCode?uncap_first}Service.updateById(entity);
        return Result.success(result);
    }

    /**
     * 删除
     * @param id 主键ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除${entityName}")
    public Result<Boolean> delete(@PathVariable Long id) {
        boolean result = ${entityCode?uncap_first}Service.removeById(id);
        return Result.success(result);
    }
} 