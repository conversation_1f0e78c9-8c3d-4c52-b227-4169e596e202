import request from '../utils/request';
export function getRolePage(params) {
    return request({
        url: '/role/page',
        method: 'get',
        params
    });
}
export function addRole(data) {
    return request({
        url: '/role',
        method: 'post',
        data
    });
}
export function updateRole(id, data) {
    return request({
        url: `/role/${id}`,
        method: 'put',
        data
    });
}
export function deleteRole(id) {
    return request({
        url: `/role/${id}`,
        method: 'delete'
    });
}
export function getRoleDetail(id) {
    return request({
        url: `/role/${id}`,
        method: 'get'
    });
}
