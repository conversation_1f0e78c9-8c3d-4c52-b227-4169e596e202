export interface staff {
    id?: number;
    createTime?: string;
    updateTime?: string;
}
export interface staffPageQuery {
    pageNum?: number;
    pageSize?: number;
}
export declare function getstaffList(params: staffPageQuery): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getAllstaffs(): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getstaffDetail(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function savestaff(data: staff): Promise<import("axios").AxiosResponse<any, any>>;
export declare function updatestaff(id: number, data: staff): Promise<import("axios").AxiosResponse<any, any>>;
export declare function deletestaff(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function batchDeletestaff(ids: number[]): Promise<import("axios").AxiosResponse<any, any>>;
export declare function exportstaff(params?: staffPageQuery): Promise<import("axios").AxiosResponse<any, any>>;
