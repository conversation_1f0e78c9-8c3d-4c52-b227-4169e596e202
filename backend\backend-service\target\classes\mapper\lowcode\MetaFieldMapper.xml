<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dz.ms.lowcode.mapper.MetaFieldMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dz.ms.lowcode.entity.MetaField">
        <id column="id" property="id" />
        <result column="entity_id" property="entityId" />
        <result column="field_code" property="fieldCode" />
        <result column="field_name" property="fieldName" />
        <result column="description" property="description" />
        <result column="column_name" property="columnName" />
        <result column="data_type" property="dataType" />
        <result column="java_type" property="javaType" />
        <result column="length" property="length" />
        <result column="is_required" property="required" />
        <result column="is_unique" property="unique" />
        <result column="default_value" property="defaultValue" />
        <result column="validation_rules" property="validationRules" />
        <result column="show_in_list" property="showInList" />
        <result column="show_in_form" property="showInForm" />
        <result column="is_searchable" property="searchable" />
        <result column="sort_no" property="sortNo" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, entity_id, field_code, field_name, description, column_name, data_type, java_type, 
        length, is_required, is_unique, default_value, validation_rules, show_in_list, 
        show_in_form, is_searchable, sort_no, creator, create_time, updater, update_time
    </sql>
    
    <!-- 根据实体ID查询字段列表 -->
    <select id="selectByEntityId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM lc_meta_field
        WHERE entity_id = #{entityId}
        ORDER BY sort_no ASC
    </select>
</mapper> 