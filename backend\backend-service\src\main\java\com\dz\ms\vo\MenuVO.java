package com.dz.ms.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 菜单VO
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@Data
public class MenuVO implements Serializable {
    /** 主键ID */
    private Long id;

    /** 父菜单ID */
    private Long parentId;

    /** 菜单名称 */
    private String name;

    /** 路由路径 */
    private String path;

    /** 权限标识 */
    private String perms;

    /** 类型 */
    private Integer type;

    /** 图标 */
    private String icon;

    /** 排序 */
    private Integer orderNum;

    /** 状态 */
    private Integer status;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;
} 