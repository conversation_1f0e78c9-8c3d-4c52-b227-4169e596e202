<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dz.ms.lowcode.mapper.MetaEntityMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dz.ms.lowcode.entity.MetaEntity">
        <id column="id" property="id" />
        <result column="entity_code" property="entityCode" />
        <result column="entity_name" property="entityName" />
        <result column="description" property="description" />
        <result column="table_name" property="tableName" />
        <result column="is_published" property="published" />
        <result column="is_enabled" property="enabled" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, entity_code, entity_name, description, table_name, is_published, is_enabled, 
        creator, create_time, updater, update_time
    </sql>
</mapper> 