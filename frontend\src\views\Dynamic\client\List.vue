<template>
  <div class="page-container">
    <el-card>
      <!-- 搜索表单 -->
      <div style="margin-bottom: 16px; display: flex; gap: 8px; align-items: center;">
                <el-button type="primary" @click="fetchData">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
        <el-button type="success" @click="openAdd">新增client</el-button>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
                <el-table-column prop="name" label="客户名称" width="255px" />
                <el-table-column prop="zzCode" label="执照编号" width="255px" />
                <el-table-column prop="city" label="城市" width="255px" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="openEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量删除按钮 -->
      <div style="margin-top: 16px;" v-if="showBatchDelete">
        <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
      </div>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pageNum"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchData"
        @current-change="fetchData"
        style="margin-top: 16px; text-align: right;"
      />
    </el-card>

    <!-- 编辑/新增对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        
        <!-- 根据验证规则自动生成表单项 -->
        <template v-for="(rule, field) in rules" :key="field">
          <!-- 移除了条件判断，确保所有规则中的字段都显示 -->
          <el-form-item :label="getFieldLabel(field)" :prop="field">
            <el-input v-model="form[field]" />
          </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { getclientList, getclientDetail, saveclient, updateclient, deleteclient, batchDeleteclient } from '../../../api/client';
import type { client, clientPageQuery } from '../../../api/client';

// 状态定义
const searchForm = reactive<Partial<clientPageQuery>>({});
const tableData = ref<client[]>([]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
const showBatchDelete = ref(false);
const selectedRows = ref<client[]>([]);

// 表单相关
const dialogVisible = ref(false);
const dialogTitle = ref('');
const form = reactive<any>({});
const formRef = ref<FormInstance>();

// 表单验证规则
const rules: FormRules = {
        name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        zzCode: [{ required: true, message: '请输入执照编号', trigger: 'blur' }],
};

// 获取字段标签
function getFieldLabel(field: string): string {
  // 将字段名转换为更友好的标签
  // 例如：user_name -> 用户名
  const labelMap: Record<string, string> = {
    // 可以在这里添加字段名到标签的映射
    // 例如：'user_name': '用户名'
  };
  
  // 如果有映射，使用映射的标签
  if (labelMap[field]) {
    return labelMap[field];
  }
  
  // 否则，将字段名转换为标题格式
  return field.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
}

// 获取数据
function fetchData() {
  getclientList({ 
    pageNum: pageNum.value, 
    pageSize: pageSize.value, 
    ...searchForm 
  }).then(res => {
    if (res.code === 0) {
      const { records, total: t } = res.data;
      tableData.value = records;
      total.value = t;
    } else {
      ElMessage.error(res.msg || '获取数据失败');
      tableData.value = [];
      total.value = 0;
    }
  }).catch(error => {
    console.error("获取client列表失败:", error);
    tableData.value = [];
    total.value = 0;
  });
}

// 重置搜索
function resetSearch() {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = undefined;
  });
  pageNum.value = 1;
  fetchData();
}

// 表格选择改变
function handleSelectionChange(rows: client[]) {
  selectedRows.value = rows;
  showBatchDelete.value = rows.length > 0;
}

// 初始化表单字段
function initFormFields() {
  // 确保表单包含验证规则中的所有字段
  Object.keys(rules).forEach(field => {
    if (!form.hasOwnProperty(field)) {
      form[field] = '';
    }
  });
}

// 打开新增对话框
function openAdd() {
  dialogTitle.value = '新增client';
  
  // 重置表单 - 创建一个新对象而不是修改现有对象
  const newForm: any = {};
  
  // 设置ID为undefined
  newForm.id = undefined;
  
  
  // 确保所有验证规则中的字段都被初始化
  Object.keys(rules).forEach(field => {
    if (!newForm.hasOwnProperty(field)) {
      newForm[field] = '';
    }
  });
  
  // 用新对象替换表单
  Object.keys(form).forEach(key => delete form[key]);
  Object.assign(form, newForm);
  
  dialogVisible.value = true;
}

// 打开编辑对话框
function openEdit(row: client) {
  dialogTitle.value = '编辑client';
  getclientDetail(row.id).then(res => {
    if (res.code === 0) {
      // 重置表单 - 创建一个新对象而不是修改现有对象
      const newForm: any = {};
      
      // 用API返回的数据填充表单
      Object.assign(newForm, res.data);
      
      // 确保所有验证规则中的字段都被初始化
      Object.keys(rules).forEach(field => {
        if (!newForm.hasOwnProperty(field)) {
          newForm[field] = '';
        }
      });
      
      // 用新对象替换表单
      Object.keys(form).forEach(key => delete form[key]);
      Object.assign(form, newForm);
      
      dialogVisible.value = true;
    } else {
      ElMessage.error(res.msg || '获取详情失败');
    }
  }).catch(error => {
    console.error("获取client详情失败:", error);
    ElMessage.error('获取详情失败');
  });
}

// 提交表单
async function handleSubmit() {
  formRef.value?.validate(async (valid) => {
    if (!valid) return;
    try {
      let res;
      if (!form.id) {
        res = await saveclient(form);
      } else {
        res = await updateclient(form.id, form);
      }
      
      if (res.code === 0) {
        ElMessage.success(form.id ? '编辑成功' : '新增成功');
        dialogVisible.value = false;
        fetchData();
      } else {
        ElMessage.error(res.msg || '操作失败');
      }
    } catch (error) {
      console.error("提交client信息失败:", error);
      ElMessage.error('操作失败');
    }
  });
}

// 删除
async function handleDelete(id?: number) {
  if (!id) return;
  
  ElMessageBox.confirm(
    '确定要删除该client吗？此操作不可恢复',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const res = await deleteclient(id);
        if (res.code === 0) {
          ElMessage.success('删除成功');
          fetchData();
        } else {
          ElMessage.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error("删除client失败:", error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 用户取消删除操作
      ElMessage.info('已取消删除');
    });
}

// 批量删除
async function handleBatchDelete() {
  if (!selectedRows.value.length) {
    ElMessage.warning('请选择要删除的记录');
    return;
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 条记录吗？此操作不可恢复`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const ids = selectedRows.value.map(row => row.id);
        const res = await batchDeleteclient(ids);
        if (res.code === 0) {
          ElMessage.success('批量删除成功');
          fetchData();
        } else {
          ElMessage.error(res.msg || '批量删除失败');
        }
      } catch (error) {
        console.error("批量删除client失败:", error);
        ElMessage.error('批量删除失败');
      }
    })
    .catch(() => {
      // 用户取消删除操作
      ElMessage.info('已取消删除');
    });
}

// 初始化
onMounted(() => {
  // 确保所有验证规则中的字段都被初始化
  initFormFields();
  fetchData();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}
</style>