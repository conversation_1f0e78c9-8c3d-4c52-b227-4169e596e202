import { defineStore } from 'pinia';

interface PermissionState {
  permissions: string[]; // 用户权限列表
  roles: string[]; // 用户角色列表
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    permissions: [],
    roles: []
  }),
  
  getters: {
    // 检查是否有某个权限
    hasPermission: (state) => (permission: string) => {
      return state.permissions.includes(permission);
    },
    // 检查是否有某个角色
    hasRole: (state) => (role: string) => {
      return state.roles.includes(role);
    }
  },
  
  actions: {
    // 设置权限
    setPermissions(permissions: string[]) {
      this.permissions = permissions;
    },
    // 设置角色
    setRoles(roles: string[]) {
      this.roles = roles;
    },
    // 清除权限
    clearPermissions() {
      this.permissions = [];
      this.roles = [];
    }
  }
}); 