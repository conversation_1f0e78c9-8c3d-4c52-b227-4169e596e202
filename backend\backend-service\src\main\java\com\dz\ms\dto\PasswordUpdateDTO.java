package com.dz.ms.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 密码更新DTO
 * <AUTHOR>
 * @date 2025-05-30
 * @version 1.0.0
 */
@Data
public class PasswordUpdateDTO implements Serializable {
    
    /** 原密码 */
    @NotBlank(message = "原密码不能为空")
    private String oldPassword;
    
    /** 新密码 */
    @NotBlank(message = "新密码不能为空")
    private String newPassword;
} 