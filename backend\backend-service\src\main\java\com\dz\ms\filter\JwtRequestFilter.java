package com.dz.ms.filter;

import com.dz.ms.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.SignatureException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * JWT认证过滤器
 * 拦截请求，验证JWT Token，设置用户认证状态
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
@Component
public class JwtRequestFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(JwtRequestFilter.class);

    @Autowired
    private UserDetailsService userDetailsService;

    // JwtUtil目前是静态方法，无需Autowired，但如果改为非静态则需要
    // @Autowired
    // private JwtUtil jwtUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain chain) throws ServletException, IOException {

        final String requestURI = request.getRequestURI();
        // 如果是登录、Swagger或Sentinel测试相关请求，则跳过Token验证
        if (requestURI.contains("/auth/login") ||
            requestURI.contains("/swagger-ui") ||
            requestURI.contains("/v3/api-docs") ||
            requestURI.contains("/auth/logout") ||
            requestURI.contains("/sentinel-test/")) {
            chain.doFilter(request, response);
            return;
        }

        final String authorizationHeader = request.getHeader("Authorization");

        String username = null;
        String jwt = null;

        logger.debug("Processing request for URI: {}", requestURI);

        // 从Authorization头中提取Token
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            logger.debug("Authorization header found");
            jwt = authorizationHeader.substring(7);
            try {
                username = JwtUtil.extractUsername(jwt);
                logger.debug("Username extracted from token: {}", username);
            } catch (ExpiredJwtException e) {
                logger.warn("JWT Token已过期", e);
                // 客户端可能需要刷新Token
            } catch (SignatureException e) {
                logger.warn("JWT签名验证失败", e);
                // 可能是伪造的Token
            } catch (MalformedJwtException e) {
                logger.warn("JWT Token格式错误", e);
                // Token结构错误
            } catch (UnsupportedJwtException e) {
                logger.warn("不支持的JWT Token", e);
                // Token版本或算法不支持
            } catch (Exception e) {
                logger.error("解析JWT Token时发生错误", e);
                // 其他未知异常
            }
        } else {
            logger.debug("Authorization header not found or does not start with Bearer");
        }

        // 如果提取到用户名且当前没有认证信息
        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            logger.debug("Username found and authentication is null, attempting to authenticate");

            UserDetails userDetails = null;
            try {
                logger.debug("Attempting to load UserDetails for user: {}", username);
                userDetails = this.userDetailsService.loadUserByUsername(username);
                logger.debug("UserDetails loaded successfully");
            } catch (UsernameNotFoundException e) {
                 logger.warn("User not found when loading UserDetails for {}", username);
                 // Allow filter chain to continue, authentication will fail later
            }

            // 验证Token有效性 (仅在UserDetails加载成功时进行)
            if (userDetails != null && JwtUtil.validateToken(jwt, userDetails)) {
                logger.debug("JWT Token is valid");

                // 构建认证对象
                UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken =
                        new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                usernamePasswordAuthenticationToken
                        .setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                // 设置认证信息到Spring Security上下文
                SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
                logger.debug("Authentication set in SecurityContextHolder");
            } else if (userDetails != null) { // UserDetails found but token invalid
                 logger.debug("JWT Token is invalid for user: {}", username);
            }
        } else if (username == null) {
             logger.debug("Username is null, cannot attempt authentication");
        } else { // SecurityContextHolder.getContext().getAuthentication() is not null
             logger.debug("SecurityContextHolder already has authentication: {}", SecurityContextHolder.getContext().getAuthentication().getName());
        }

        // 继续过滤器链
        chain.doFilter(request, response);
    }
} 