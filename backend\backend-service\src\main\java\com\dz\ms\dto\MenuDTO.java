package com.dz.ms.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 菜单DTO
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@Data
public class MenuDTO implements Serializable {
    /** 父菜单ID */
    @NotNull(message = "父菜单ID不能为空")
    private Long parentId;

    /** 菜单名称 */
    @NotBlank(message = "菜单名称不能为空")
    private String name;

    /** 路由路径 */
    private String path;

    /** 权限标识 */
    private String perms;

    /** 类型 */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /** 图标 */
    private String icon;

    /** 排序 */
    private Integer orderNum;

    /** 状态 */
    @NotNull(message = "状态不能为空")
    private Integer status;
} 