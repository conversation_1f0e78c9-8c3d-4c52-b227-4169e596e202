<template>
  <div class="entity-form">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <!-- 基本信息 -->
      <el-divider>基本信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="实体编码" prop="entityCode">
            <el-input 
              v-model="form.entityCode" 
              :disabled="formType === 'edit'"
              placeholder="请输入实体编码，如：Customer"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实体名称" prop="entityName">
            <el-input 
              v-model="form.entityName" 
              placeholder="请输入实体名称，如：客户"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="表名" prop="tableName">
            <el-input 
              v-model="form.tableName" 
              placeholder="请输入数据库表名，如：lc_customer"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="描述" prop="description">
            <el-input 
              v-model="form.description" 
              placeholder="请输入实体描述"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 字段列表 -->
      <el-divider>字段信息</el-divider>
      <div class="field-list-toolbar">
        <el-button type="primary" @click="addField">
          <el-icon><Plus /></el-icon>
          添加字段
        </el-button>
      </div>
      <el-table :data="form.fields" border style="width: 100%">
        <el-table-column type="index" width="50" label="#" />
        <el-table-column label="字段编码" min-width="120">
          <template #default="scope">
            <el-form-item 
              :prop="'fields.' + scope.$index + '.fieldCode'"
              :rules="fieldRules.fieldCode"
              class="no-margin" 
            >
              <el-input v-model="scope.row.fieldCode" placeholder="如：name" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="字段名称" min-width="120">
          <template #default="scope">
            <el-form-item 
              :prop="'fields.' + scope.$index + '.fieldName'"
              :rules="fieldRules.fieldName"
              class="no-margin"
            >
              <el-input v-model="scope.row.fieldName" placeholder="如：姓名" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="字段描述" min-width="120">
          <template #default="scope">
            <el-form-item 
              :prop="'fields.' + scope.$index + '.description'"
              class="no-margin"
            >
              <el-input v-model="scope.row.description" placeholder="如：身份证号" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="数据类型" width="120">
          <template #default="scope">
            <el-form-item 
              :prop="'fields.' + scope.$index + '.dataType'"
              :rules="fieldRules.dataType"
              class="no-margin"
            >
              <el-select 
                v-model="scope.row.dataType" 
                placeholder="请选择" 
                style="width: 100%"
                @change="handleDataTypeChange(scope.row)"
              >
                <el-option 
                  v-for="item in dataTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="长度" width="120">
          <template #default="scope">
            <el-form-item 
              :prop="'fields.' + scope.$index + '.length'"
              class="no-margin"
            >
              <el-input-number 
                v-model="scope.row.length" 
                :min="0" 
                :disabled="!['String', 'Text'].includes(scope.row.dataType)"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="必填" width="70">
          <template #default="scope">
            <el-checkbox v-model="scope.row.required" />
          </template>
        </el-table-column>
        <el-table-column label="唯一" width="70">
          <template #default="scope">
            <el-checkbox v-model="scope.row.unique" />
          </template>
        </el-table-column>
        <el-table-column label="列表显示" width="90">
          <template #default="scope">
            <el-checkbox v-model="scope.row.showInList" />
          </template>
        </el-table-column>
        <el-table-column label="表单显示" width="90">
          <template #default="scope">
            <el-checkbox v-model="scope.row.showInForm" />
          </template>
        </el-table-column>
        <el-table-column label="可搜索" width="80">
          <template #default="scope">
            <el-checkbox v-model="scope.row.searchable" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-button 
              type="danger" 
              size="small" 
              link
              @click="removeField(scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 表单按钮 -->
      <div class="form-actions">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-form>
    <!-- SQL弹窗 -->
    <el-dialog v-model="sqlDialogVisible" title="表结构变更SQL" width="700px">
      <el-input
        type="textarea"
        v-model="alterSql"
        :rows="8"
        style="font-family: monospace;"
      />
      <template #footer>
        <el-button @click="sqlDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="sqlExecLoading" @click="handleExecuteSql">执行SQL</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, defineProps, defineEmits, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
// 图标已全局注册;
import { saveEntity, updateEntity, getEntityById, dataTypeOptions, getJavaType, generateAlterSql, executeSql } from '@/api/lowcode';

// 定义属性
const props = defineProps({
  entityData: {
    type: Object,
    default: () => null
  },
  formType: {
    type: String,
    default: 'add'
  }
});

// 定义事件
const emit = defineEmits(['success', 'cancel']);

// 表单引用
const formRef = ref();

// 表单数据
const form = reactive({
  id: undefined,
  entityCode: '',
  entityName: '',
  tableName: '',
  description: '',
  fields: [] as any[]
});

// 表单验证规则
const rules = {
  entityCode: [
    { required: true, message: '请输入实体编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '实体编码只能包含字母、数字和下划线，且必须以字母开头', trigger: 'blur' },
    { min: 1, max: 50, message: '实体编码长度必须在1-50之间', trigger: 'blur' }
  ],
  entityName: [
    { required: true, message: '请输入实体名称', trigger: 'blur' },
    { min: 1, max: 50, message: '实体名称长度必须在1-50之间', trigger: 'blur' }
  ],
  tableName: [
    { required: true, message: '请输入表名', trigger: 'blur' }
  ]
};

// 字段验证规则
const fieldRules = {
  fieldCode: [
    { required: true, message: '请输入字段编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段编码只能包含字母、数字和下划线，且必须以字母开头', trigger: 'blur' },
    { min: 1, max: 50, message: '字段编码长度必须在1-50之间', trigger: 'blur' }
  ],
  fieldName: [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { min: 1, max: 50, message: '字段名称长度必须在1-50之间', trigger: 'blur' }
  ],
  dataType: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ]
};

// SQL弹窗相关
const sqlDialogVisible = ref(false);
const alterSql = ref('');
const sqlExecLoading = ref(false);

// 初始化表单数据
const initForm = async () => {
  if (props.formType === 'edit' && props.entityData) {
    try {
      const res = await getEntityById(props.entityData.id);
      if (res.success) {
        const entityData = res.data;
        Object.assign(form, {
          id: entityData.id,
          entityCode: entityData.entityCode,
          entityName: entityData.entityName,
          tableName: entityData.tableName,
          description: entityData.description,
          fields: entityData.fields || []
        });
      } else {
        ElMessage.error(res.message || '获取实体详情失败');
      }
    } catch (error) {
      console.error(error);
      ElMessage.error('获取实体详情失败');
    }
  } else {
    Object.assign(form, {
      id: undefined,
      entityCode: '',
      entityName: '',
      tableName: '',
      description: '',
      fields: []
    });
  }
};

// 监听表单类型和数据变化
watch(() => props.formType, initForm, { immediate: true });
watch(() => props.entityData, initForm, { immediate: true });

// 监听实体编码变化，自动生成表名
watch(() => form.entityCode, (val) => {
  if (val && !form.tableName) {
    form.tableName = `lc_${val.toLowerCase()}`;
  }
});

// 添加字段
const addField = () => {
  form.fields.push({
    fieldCode: '',
    fieldName: '',
    dataType: 'String',
    javaType: 'String',
    length: 255,
    required: false,
    unique: false,
    showInList: true,
    showInForm: true,
    searchable: false
  });
};

// 移除字段
const removeField = (index: number) => {
  form.fields.splice(index, 1);
};

// 处理数据类型变更
const handleDataTypeChange = (row: any) => {
  row.javaType = getJavaType(row.dataType);
  
  // 根据数据类型设置默认长度
  if (row.dataType === 'String') {
    row.length = 255;
  } else if (row.dataType === 'Text') {
    row.length = 0;
  }
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    // 为每个字段设置列名
    form.fields.forEach(field => {
      if (!field.columnName) {
        field.columnName = field.fieldCode.toLowerCase();
      }
    });
    // 1. 先生成ALTER SQL
    if (form.id) {
      // 编辑模式，生成ALTER SQL
      const res = await generateAlterSql(form.id, form.fields);
      alterSql.value = res.data || '';
      sqlDialogVisible.value = true;
    } else {
      // 新增直接保存
      const api = saveEntity;
      const res = await api(form);
      if (res.success) {
        ElMessage.success('保存成功');
        emit('success');
      } else {
        ElMessage.error(res.message || '保存失败');
      }
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('表单验证失败，请检查输入');
  }
};

// 执行SQL
const handleExecuteSql = async () => {
  sqlExecLoading.value = true;
  try {
    const res = await executeSql(alterSql.value);
    if (res.success) {
      ElMessage.success('SQL执行成功');
      sqlDialogVisible.value = false;
      // 执行完毕后再保存字段信息
      const api = updateEntity;
      const saveRes = await api(form);
      if (saveRes.success) {
        ElMessage.success('保存成功');
        emit('success');
      } else {
        ElMessage.error(saveRes.message || '保存失败');
      }
    } else {
      ElMessage.error(res.message || 'SQL执行失败');
    }
  } catch (e) {
    ElMessage.error('SQL执行异常');
  } finally {
    sqlExecLoading.value = false;
  }
};

// 取消
const cancel = () => {
  emit('cancel');
};
</script>

<style scoped>
.entity-form {
  padding: 10px;
}

.field-list-toolbar {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-start;
}

.form-actions {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

:deep(.no-margin) {
  margin: 0;
}

/* 调整表格内输入框样式 */
:deep(.el-table .el-input__wrapper) {
  width: 100%;
  box-sizing: border-box;
  padding: 0 8px;
}

:deep(.el-table .el-input) {
  width: 100%;
  display: flex;
  align-items: center;
}

:deep(.el-table .el-input__inner) {
  text-align: center;
  width: 100%;
  padding: 0;
  height: 32px;
  line-height: 32px;
}

:deep(.el-table .el-form-item) {
  width: 100%;
  margin: 0;
  display: flex;
  align-items: center;
  height: 100%;
}

:deep(.el-table .el-form-item__content) {
  width: 100%;
  margin: 0 !important;
}

:deep(.el-table .el-select) {
  width: 100%;
}

:deep(.el-table-column--default .cell) {
  padding: 4px;
}
</style> 