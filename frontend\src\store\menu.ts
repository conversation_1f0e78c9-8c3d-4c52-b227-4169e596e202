import { defineStore } from 'pinia';
import type { Menu } from '../api/menu'; // Import Menu interface

interface MenuState {
  menuTree: Menu[];
  // 可以添加其他菜单相关的状态，例如扁平化菜单列表等
}

export const useMenuStore = defineStore('menu', {
  state: (): MenuState => ({
    menuTree: [],
  }),
  actions: {
    setMenuTree(menuTree: Menu[]) {
      this.menuTree = menuTree;
    },
    // 可以添加获取菜单的异步action
  },
}); 