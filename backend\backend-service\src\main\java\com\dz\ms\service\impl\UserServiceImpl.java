package com.dz.ms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dz.ms.entity.User;
import com.dz.ms.entity.Role;
import com.dz.ms.entity.UserRole;
import com.dz.ms.mapper.UserMapper;
import com.dz.ms.mapper.RoleMapper;
import com.dz.ms.service.UserService;
import com.dz.ms.service.UserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户Service实现类
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private UserRoleService userRoleService;
    
    @Autowired
    private RoleMapper roleMapper;

    @Override
    public boolean addUser(User user) {
        // Encrypt the password before saving
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        return save(user);
    }

    @Override
    public boolean updateUser(User user) {
        UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", user.getId());

        // Only update fields if they are not null or empty (for strings)
        if (user.getUsername() != null && !user.getUsername().isEmpty()) {
            updateWrapper.set("username", user.getUsername());
        }
        // Check if password field is present and not empty
        if (user.getPassword() != null && !user.getPassword().isEmpty()) {
            // Encrypt the new password
            updateWrapper.set("password", passwordEncoder.encode(user.getPassword()));
        }
        if (user.getNickname() != null && !user.getNickname().isEmpty()) {
            updateWrapper.set("nickname", user.getNickname());
        }
         if (user.getPhone() != null && !user.getPhone().isEmpty()) {
            updateWrapper.set("phone", user.getPhone());
        }
         if (user.getEmail() != null && !user.getEmail().isEmpty()) {
            updateWrapper.set("email", user.getEmail());
        }

        // Status is an Integer, check for null
        if (user.getStatus() != null) {
             updateWrapper.set("status", user.getStatus());
        }

        // Use update method with wrapper
        return update(updateWrapper);
    }

    @Override
    public User getByUsername(String username) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, username);
        return baseMapper.selectOne(queryWrapper);
    }
    
    @Override
    public List<Role> getUserRoles(Long userId) {
        // 获取用户角色关联
        List<UserRole> userRoles = userRoleService.getUserRoleByUserId(userId);
        
        if (userRoles.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 提取角色ID
        List<Long> roleIds = userRoles.stream()
                .map(UserRole::getRoleId)
                .collect(Collectors.toList());
        
        // 查询角色信息
        return roleMapper.selectBatchIds(roleIds);
    }
} 