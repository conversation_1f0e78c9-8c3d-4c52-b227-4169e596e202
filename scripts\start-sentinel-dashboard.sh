#!/bin/bash

# Sentinel 控制台启动脚本
# 作者: AI
# 日期: 2025-06-29

# 配置参数
SENTINEL_VERSION="1.8.6"
SENTINEL_JAR="sentinel-dashboard-${SENTINEL_VERSION}.jar"
SENTINEL_PORT="8080"
SENTINEL_USERNAME="sentinel"
SENTINEL_PASSWORD="sentinel123"
LOG_FILE="logs/sentinel-dashboard.log"

# 创建日志目录
mkdir -p logs

# 检查 JAR 文件是否存在
if [ ! -f "$SENTINEL_JAR" ]; then
    echo "Sentinel JAR 文件不存在，正在下载..."
    wget "https://github.com/alibaba/Sentinel/releases/download/${SENTINEL_VERSION}/${SENTINEL_JAR}"
    
    if [ $? -ne 0 ]; then
        echo "下载失败，请手动下载 ${SENTINEL_JAR}"
        exit 1
    fi
fi

# 检查端口是否被占用
if lsof -Pi :$SENTINEL_PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "端口 $SENTINEL_PORT 已被占用，请检查是否已有 Sentinel 控制台在运行"
    exit 1
fi

echo "正在启动 Sentinel 控制台..."
echo "端口: $SENTINEL_PORT"
echo "用户名: $SENTINEL_USERNAME"
echo "密码: $SENTINEL_PASSWORD"
echo "日志文件: $LOG_FILE"

# 启动 Sentinel 控制台
nohup java -Dserver.port=$SENTINEL_PORT \
           -Dcsp.sentinel.dashboard.server=localhost:$SENTINEL_PORT \
           -Dproject.name=sentinel-dashboard \
           -Dsentinel.dashboard.auth.username=$SENTINEL_USERNAME \
           -Dsentinel.dashboard.auth.password=$SENTINEL_PASSWORD \
           -Xms512m \
           -Xmx1024m \
           -jar $SENTINEL_JAR > $LOG_FILE 2>&1 &

# 获取进程ID
PID=$!
echo $PID > sentinel-dashboard.pid

echo "Sentinel 控制台启动中，进程ID: $PID"
echo "请等待几秒钟后访问: http://localhost:$SENTINEL_PORT"
echo "查看日志: tail -f $LOG_FILE"

# 等待服务启动
sleep 5

# 检查服务是否启动成功
if ps -p $PID > /dev/null; then
    echo "Sentinel 控制台启动成功！"
    echo "访问地址: http://localhost:$SENTINEL_PORT"
    echo "用户名: $SENTINEL_USERNAME"
    echo "密码: $SENTINEL_PASSWORD"
else
    echo "Sentinel 控制台启动失败，请查看日志: $LOG_FILE"
    exit 1
fi
