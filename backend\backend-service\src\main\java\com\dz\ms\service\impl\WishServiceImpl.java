package com.dz.ms.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.ms.entity.Wish;
import com.dz.ms.mapper.WishMapper;
import com.dz.ms.service.WishService;
import com.dz.ms.vo.WishVO;
import org.springframework.stereotype.Service;

/**
 * 心愿单Service实现类
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@Service
public class WishServiceImpl extends ServiceImpl<WishMapper, Wish> implements WishService {
    
    @Override
    public IPage<WishVO> pageWishWithMember(Page<Wish> page, Long memberId, String keyword, Integer status) {
        return baseMapper.pageWishWithMember(page, memberId, keyword, status);
    }
} 