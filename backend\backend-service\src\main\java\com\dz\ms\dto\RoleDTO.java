package com.dz.ms.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 角色DTO
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@Data
public class RoleDTO implements Serializable {
    /** 角色名称 */
    @NotBlank(message = "角色名称不能为空")
    private String name;

    /** 角色编码 */
    @NotBlank(message = "角色编码不能为空")
    private String code;

    /** 描述 */
    private String description;

    /** 状态 */
    @NotNull(message = "状态不能为空")
    private Integer status;
} 