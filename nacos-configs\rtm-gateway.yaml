spring:
  cloud:
    gateway:
      routes:
        # 后端服务路由 - 将/api/**请求转发到后端服务
        - id: backend-service
          uri: http://**********:9091
          predicates:
            - Path=/api/**
          filters:
            # 去掉/api前缀，因为后端服务的接口路径不包含/api
            - StripPrefix=1
            # 添加请求头
            - AddRequestHeader=X-Gateway-Source, rtm-gateway
          metadata:
            # Sentinel资源名称
            resource-name: backend-service
            
    # Nacos服务发现配置
    nacos:
      discovery:
        server-addr: **********:8848
        enabled: true
        service: rtm-gateway
        
    # Sentinel配置
    sentinel:
      transport:
        # sentinel控制台地址
        dashboard: localhost:8080
        # 指定应用与Sentinel控制台交互的端口
        port: 8719
      http-method-specify: true
      eager: true
      
      # 数据源配置 - 从Nacos读取规则
      datasource:
        # 网关流控规则
        gw-flow:
          nacos:
            server-addr: **********:8848
            data-id: gateway-service-gw-flow-rules
            group-id: SENTINEL_GROUP
            data-type: json
            rule-type: gw-flow
            
        # 网关API分组规则  
        gw-api-group:
          nacos:
            server-addr: **********:8848
            data-id: gateway-service-gw-api-group-rules
            group-id: SENTINEL_GROUP
            data-type: json
            rule-type: gw-api-group

# 日志配置
logging:
  level:
    com.dz.ms.gateway: DEBUG
    org.springframework.cloud.gateway: DEBUG
    
# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
