<template>
  <div class="form-container">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="form"
    >
      <#list fields as field>
        <el-form-item label="${field.fieldName}" prop="${field.fieldCode}">
          <#if field.dataType == "String">
            <#if field.length?? && field.length gt 200>
              <el-input
                v-model="formData.${field.fieldCode}"
                type="textarea"
                :rows="3"
                placeholder="请输入${field.fieldName}"
              />
            <#else>
              <el-input
                v-model="formData.${field.fieldCode}"
                placeholder="请输入${field.fieldName}"
              />
            </#if>
          <#elseif field.dataType == "Integer" || field.dataType == "Long">
            <el-input-number
              v-model="formData.${field.fieldCode}"
              :precision="0"
              placeholder="请输入${field.fieldName}"
            />
          <#elseif field.dataType == "Double" || field.dataType == "BigDecimal">
            <el-input-number
              v-model="formData.${field.fieldCode}"
              :precision="2"
              placeholder="请输入${field.fieldName}"
            />
          <#elseif field.dataType == "Boolean">
            <el-switch
              v-model="formData.${field.fieldCode}"
              active-text="是"
              inactive-text="否"
            />
          <#elseif field.dataType == "Date">
            <el-date-picker
              v-model="formData.${field.fieldCode}"
              type="datetime"
              placeholder="请选择${field.fieldName}"
            />
          <#else>
            <el-input
              v-model="formData.${field.fieldCode}"
              placeholder="请输入${field.fieldName}"
            />
          </#if>
        </el-form-item>
      </#list>

      <el-form-item>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { save${entityCode}, update${entityCode} } from '@/api/${entityCode?lower_case}'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive(<${entityCode}>{})

// 表单验证规则
const rules = reactive({
  <#list fields as field>
    <#if field.required>
      ${field.fieldCode}: [
        { required: true, message: '请输入${field.fieldName}', trigger: 'blur' }
      ]<#if field_has_next>,</#if>
    </#if>
  </#list>
})

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const isEdit = route.query.id
        const api = isEdit ? update${entityCode} : save${entityCode}
        const res = await api(formData)
        
        if (res.success) {
          ElMessage.success('保存成功')
          handleCancel()
        } else {
          ElMessage.error(res.message || '保存失败')
        }
      } catch (error) {
        ElMessage.error('操作失败')
      }
    }
  })
}

// 取消操作
const handleCancel = () => {
  router.back()
}

// 如果是编辑模式，加载数据
if (route.query.id) {
  // 这里需要调用获取详情的API
  // const res = await get${entityCode}Detail(route.query.id)
  // Object.assign(formData, res.data)
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}
.form {
  max-width: 800px;
  margin: 0 auto;
}
</style>