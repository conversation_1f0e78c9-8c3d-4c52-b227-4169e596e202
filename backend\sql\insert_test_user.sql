// TODO: 插入测试用户的SQL脚本 (密码为'password', BCrypt加密)
// 请在您的数据库中执行以下SQL语句：
/*
INSERT INTO `user` (`username`, `password`, `nickname`, `email`, `phone`, `avatar`, `status`, `create_time`, `update_time`, `deleted`)
VALUES (
    'test', -- 用户名
    '$2a$10$e.bN8bO.R.H/c1l6.p.ZgO9k0D9qO.t.Q.R.T.V.X.Y/', -- 密码 (password的BCrypt加密)
    '测试用户', -- 昵称
    '<EMAIL>', -- 邮箱
    '13812345678', -- 手机号
    NULL, -- 头像
    1, -- 状态 (1为启用)
    NOW(), -- 创建时间
    NOW(), -- 更新时间
    0 -- 软删除标记 (0为未删除)
);
*/ 