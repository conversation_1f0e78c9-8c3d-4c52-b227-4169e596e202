<template>
  <div class="entity-manage">
    <div class="toolbar">
      <el-button type="primary" @click="openEntityForm()">
        <el-icon><Plus /></el-icon>
        新建实体
      </el-button>
      <el-input
        v-model="queryParams.keyword"
        placeholder="请输入关键字搜索"
        class="search-input"
        clearable
        @keyup.enter="handleSearch"
      >
        <template #suffix>
          <el-icon class="el-input__icon" @click="handleSearch">
            <Search />
          </el-icon>
        </template>
      </el-input>
    </div>

    <el-table
      v-loading="loading"
      :data="entityList"
      border
      style="width: 100%"
    >
      <el-table-column prop="entityCode" label="实体编码" min-width="120" />
      <el-table-column prop="entityName" label="实体名称" min-width="120" />
      <el-table-column prop="tableName" label="表名" min-width="120" />
      <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
      <el-table-column label="状态" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.published ? 'success' : 'info'">
            {{ scope.row.published ? '已发布' : '未发布' }}
          </el-tag>
          <el-tag :type="scope.row.enabled ? 'success' : 'danger'" style="margin-left: 5px">
            {{ scope.row.enabled ? '启用' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220" fixed="right">
        <template #default="scope">
          <el-button 
            type="primary" 
            size="small" 
            link
            @click="openEntityForm(scope.row)"
            v-if="!scope.row.published"
          >
            编辑
          </el-button>
          <el-button 
            v-if="!scope.row.published" 
            type="success" 
            size="small" 
            link
            @click="handlePublish(scope.row.id)"
          >
            发布
          </el-button>
          <el-button 
            v-else 
            type="warning" 
            size="small" 
            link
            @click="handleUnpublish(scope.row.id)"
          >
            取消发布
          </el-button>
          <el-button 
            v-if="!scope.row.published" 
            type="danger" 
            size="small" 
            link
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.size"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 实体表单对话框 -->
    <el-dialog
      v-model="entityFormVisible"
      :title="formType === 'add' ? '新建实体' : '编辑实体'"
      width="60%"
      destroy-on-close
    >
      <entity-form 
        :entity-data="entityFormData" 
        :form-type="formType" 
        @success="handleFormSuccess" 
        @cancel="entityFormVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标已全局注册;
import { getEntityPage, deleteEntity, publishEntity, unpublishEntity } from '@/api/lowcode';
import EntityForm from './components/EntityForm.vue';

// 查询参数
const queryParams = reactive({
  page: 1,
  size: 10,
  keyword: ''
});

// 数据列表
const entityList = ref([]);
const total = ref(0);
const loading = ref(false);

// 表单相关
const entityFormVisible = ref(false);
const entityFormData = ref<any>(null);
const formType = ref<'add' | 'edit'>('add');

// 初始化加载数据
onMounted(() => {
  getList();
});

// 获取实体列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await getEntityPage(queryParams);
    entityList.value = res.data.records;
    total.value = res.data.total;
  } catch (error) {
    console.error(error);
    ElMessage.error('获取实体列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  queryParams.page = 1;
  getList();
};

// 处理页面大小变化
const handleSizeChange = (size: number) => {
  queryParams.size = size;
  getList();
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.page = page;
  getList();
};

// 打开实体表单
const openEntityForm = (row?: any) => {
  if (row) {
    formType.value = 'edit';
    entityFormData.value = { ...row };
  } else {
    formType.value = 'add';
    entityFormData.value = null;
  }
  entityFormVisible.value = true;
};

// 处理表单提交成功
const handleFormSuccess = () => {
  entityFormVisible.value = false;
  getList();
};

// 处理删除
const handleDelete = (id: number) => {
  ElMessageBox.confirm('确定要删除该实体吗？删除后不可恢复！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteEntity(id);
      if (res.data.success) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(res.data.message || '删除失败');
      }
    } catch (error) {
      console.error(error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {});
};

// 处理发布
const handlePublish = (id: number) => {
  ElMessageBox.confirm('确定要发布该实体吗？发布后将生成相应的数据库表和代码。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await publishEntity(id);
      if (res.success) {
        ElMessage.success('发布成功');
        getList();
      } else {
        ElMessage.error(res.message || '发布失败');
      }
    } catch (error) {
      console.error(error);
      ElMessage.error('发布失败');
    }
  }).catch(() => {});
};

// 处理取消发布
const handleUnpublish = (id: number) => {
  ElMessageBox.confirm('确定要取消发布该实体吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await unpublishEntity(id);
      if (res.success) {
        ElMessage.success('取消发布成功');
        getList();
      } else {
        ElMessage.error(res.message || '取消发布失败');
      }
    } catch (error) {
      console.error(error);
      ElMessage.error('取消发布失败');
    }
  }).catch(() => {});
};
</script>

<style scoped>
.entity-manage {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-input {
  width: 300px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 