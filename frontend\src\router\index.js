import { createRouter, createWebHistory } from 'vue-router';
import MainLayout from '../layouts/MainLayout.vue'; // 导入主布局组件
import { useMenuStore } from '../store/menu';
import { usePermissionStore } from '../store/permission';
import { getUserMenus } from '../api/user';
import { ElMessage } from 'element-plus';
const modules = import.meta.glob('../views/**/*.vue');
// 公共路由 - 所有用户都可以访问
const publicRoutes = [
    {
        path: '/',
        redirect: '/login'
    },
    {
        path: '/login',
        name: 'Login',
        component: () => import('../views/Auth/Login.vue')
    },
    {
        path: '/403',
        name: 'Forbidden',
        component: () => import('../views/Auth/Forbidden.vue')
    },
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: () => import('../views/Auth/NotFound.vue')
    }
];
// 基础路由 - 需要认证但不需要权限的路由
const baseRoutes = [
    {
        path: '/',
        component: MainLayout,
        children: [
            {
                path: '/welcome',
                name: 'Welcome',
                component: () => import('../views/Welcome/Welcome.vue'),
                meta: { title: '欢迎页' }
            }
        ]
    }
];
// 创建路由器实例
const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [...publicRoutes, ...baseRoutes]
});
export function loadDynamicRoutes(menus) {
    menus.forEach((menu) => {
        if (menu.type === 'C' && menu.path && menu.component) {
            let compPath = menu.component.startsWith('/') ? menu.component.slice(1) : menu.component;
            const filePath = `../views/${compPath}.vue`;
            const route = {
                path: '/',
                component: MainLayout,
                children: [
                    {
                        path: menu.path.startsWith('/') ? menu.path : '/' + menu.path,
                        name: menu.name || (menu.path.startsWith('/') ? menu.path.slice(1) : menu.path),
                        component: modules[filePath],
                        meta: { title: menu.name, permission: menu.permission, ...menu.meta }
                    }
                ]
            };
            router.addRoute('/', route);
        }
        if (menu.children && menu.children.length > 0) {
            loadDynamicRoutes(menu.children);
        }
    });
}
export function loadDynamicRoutes0(menus) {
    menus.forEach((menu) => {
        if (menu.type === 'C' && menu.path && menu.component) {
            let compPath = menu.component.startsWith('/') ? menu.component.slice(1) : menu.component;
            const filePath = `../views/${compPath}.vue`;
            const route = {
                path: menu.path.startsWith('/') ? menu.path : '/' + menu.path,
                name: menu.name || (menu.path.startsWith('/') ? menu.path.slice(1) : menu.path),
                component: modules[filePath],
                meta: { title: menu.name, permission: menu.permission, ...menu.meta }
            };
            router.addRoute('/', route);
        }
        if (menu.children && menu.children.length > 0) {
            loadDynamicRoutes(menu.children);
        }
    });
}
// 全局前置守卫
router.beforeEach(async (to, from, next) => {
    const publicPages = ['/login', '/403', '/404'];
    const authRequired = !publicPages.includes(to.path);
    const token = localStorage.getItem('token');
    if (!authRequired) {
        return next();
    }
    if (authRequired && !token) {
        return next('/login');
    }
    if (token && to.path === '/login') {
        return next('/welcome');
    }
    if (token && authRequired) {
        const permissionStore = usePermissionStore();
        const menuStore = useMenuStore();
        if (permissionStore.permissions.length === 0) {
            try {
                const menuRes = await getUserMenus();
                if (menuRes && menuRes.code === 0 && menuRes.data) {
                    menuStore.setMenuTree(menuRes.data);
                    // 动态注册路由
                    loadDynamicRoutes(menuRes.data);
                    // 提取权限
                    const permissions = [];
                    const extractPermissions = (menus) => {
                        menus.forEach((menu) => {
                            if (menu.permission)
                                permissions.push(menu.permission);
                            if (menu.children && menu.children.length > 0)
                                extractPermissions(menu.children);
                        });
                    };
                    extractPermissions(menuRes.data);
                    permissionStore.setPermissions(permissions);
                    if (to.matched.length === 0) {
                        next({ ...to, replace: true });
                        return;
                    }
                }
            }
            catch (error) {
                localStorage.removeItem('token');
                permissionStore.clearPermissions();
                menuStore.setMenuTree([]);
                ElMessage.error('获取用户权限失败，请重新登录');
                next('/login');
                return;
            }
        }
        // 权限校验
        const permission = to.meta.permission;
        if (permission && !permissionStore.hasPermission(permission)) {
            next('/403');
            return;
        }
    }
    next();
});
export default router;
