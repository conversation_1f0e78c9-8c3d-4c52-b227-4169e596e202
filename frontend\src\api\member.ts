import request from '../utils/request';

export interface Member {
  id?: number;
  username: string;
  password?: string;
  nickname?: string;
  phone?: string;
  email?: string;
  status: number;
  createTime?: string;
  updateTime?: string;
}

export interface MemberPageQuery {
  pageNum?: number;
  pageSize?: number;
  username?: string;
}

export function getMemberPage(params: MemberPageQuery) {
  return request.get('/member/page', { params });
}

export function getAllMembers() {
  return request.get('/member/list');
}

export function addMember(data: Member) {
  return request.post('/member', data);
}

export function updateMember(id: number, data: Member) {
  return request.put(`/member/${id}`, data);
}

export function deleteMember(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.delete(`/member/${id}`);
}

export function getMemberDetail(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.get(`/member/${id}`);
}