import request from '../utils/request';
// 元数据实体相关接口
export function getEntityPage(params) {
    return request.get('/lowcode/meta/entity/page', { params });
}
export function getEntityById(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.get(`/lowcode/meta/entity/${id}`);
}
export function saveEntity(data) {
    return request.post('/lowcode/meta/entity', data);
}
export function updateEntity(data) {
    if (!data.id)
        return Promise.reject('ID不能为空');
    return request.put('/lowcode/meta/entity', data);
}
export function deleteEntity(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.delete(`/lowcode/meta/entity/${id}`);
}
export function publishEntity(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.post(`/lowcode/meta/entity/${id}/publish`);
}
export function unpublishEntity(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.post(`/lowcode/meta/entity/${id}/unpublish`);
}
// 数据类型选项
export const dataTypeOptions = [
    { label: '字符串', value: 'String' },
    { label: '整数', value: 'Integer' },
    { label: '长整数', value: 'Long' },
    { label: '小数', value: 'Double' },
    { label: '布尔值', value: 'Boolean' },
    { label: '日期时间', value: 'Date' },
    { label: '大数字', value: 'BigDecimal' },
    { label: '文本', value: 'Text' }
];
// 获取Java类型映射
export function getJavaType(dataType) {
    const map = {
        'String': 'String',
        'Integer': 'Integer',
        'Long': 'Long',
        'Double': 'Double',
        'Boolean': 'Boolean',
        'Date': 'Date',
        'BigDecimal': 'BigDecimal',
        'Text': 'String'
    };
    return map[dataType] || 'String';
}
// 生成表结构变更SQL
export function generateAlterSql(entityId, fields) {
    return request.post('/lowcode/meta/entity/generate-alter-sql', {
        entityId,
        fields
    });
}
// 执行SQL
export function executeSql(sql) {
    return request.post('/lowcode/meta/entity/execute-sql', { sql });
}
