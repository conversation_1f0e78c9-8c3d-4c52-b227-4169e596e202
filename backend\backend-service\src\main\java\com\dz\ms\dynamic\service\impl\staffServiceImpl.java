package com.dz.ms.dynamic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.ms.dynamic.entity.staff;
import com.dz.ms.dynamic.mapper.staffMapper;
import com.dz.ms.dynamic.service.staffService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * staff Service实现类
 * 由低代码平台自动生成
 */
@Service
public class staffServiceImpl extends ServiceImpl<staffMapper, staff> implements staffService {

    @Override
    public IPage<staff> page(Integer page, Integer size, String keyword) {
        Page<staff> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<staff> wrapper = new LambdaQueryWrapper<>();
        
        // TODO: 根据实际业务修改查询条件
        
        // 按更新时间倒序排序
        wrapper.orderByDesc(staff::getUpdateTime);
        
        return page(pageParam, wrapper);
    }
}