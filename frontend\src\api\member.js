import request from '../utils/request';
export function getMemberPage(params) {
    return request.get('/member/page', { params });
}
export function getAllMembers() {
    return request.get('/member/list');
}
export function addMember(data) {
    return request.post('/member', data);
}
export function updateMember(id, data) {
    return request.put(`/member/${id}`, data);
}
export function deleteMember(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.delete(`/member/${id}`);
}
export function getMemberDetail(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.get(`/member/${id}`);
}
