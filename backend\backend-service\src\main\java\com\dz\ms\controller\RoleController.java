package com.dz.ms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.ms.entity.Role;
import com.dz.ms.service.RoleService;
import com.dz.ms.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 角色管理
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
@RestController
@RequestMapping("/role")
@Api(tags = "角色管理")
@Validated
public class RoleController {

    @Autowired
    private RoleService roleService;

    @ApiOperation("分页查询角色")
    @GetMapping("/page")
    public Result<IPage<Role>> page(@RequestParam(defaultValue = "1") long pageNum,
                                    @RequestParam(defaultValue = "10") long pageSize,
                                    @RequestParam(required = false) String name) {
        Page<Role> page = new Page<>(pageNum, pageSize);
        IPage<Role> rolePage = roleService.lambdaQuery()
                .like(name != null && !name.isEmpty(), Role::getName, name)
                .orderByDesc(Role::getCreateTime)
                .page(page);
        return Result.success(rolePage);
    }

    @ApiOperation("新增角色")
    @PostMapping
    public Result<Boolean> add(@Valid @RequestBody Role role) {
        boolean saved = roleService.save(role);
        return Result.success(saved);
    }

    @ApiOperation("编辑角色")
    @PutMapping("/{id}")
    public Result<Boolean> update(@PathVariable Long id, @Valid @RequestBody Role role) {
        role.setId(id);
        boolean updated = roleService.updateById(role);
        return Result.success(updated);
    }

    @ApiOperation("删除角色")
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        boolean removed = roleService.removeById(id);
        return Result.success(removed);
    }

    @ApiOperation("角色详情")
    @GetMapping("/{id}")
    public Result<Role> detail(@PathVariable Long id) {
        Role role = roleService.getById(id);
        return Result.success(role);
    }
} 