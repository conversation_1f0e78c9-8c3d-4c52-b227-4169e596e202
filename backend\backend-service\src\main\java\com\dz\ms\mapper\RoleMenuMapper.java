package com.dz.ms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.entity.RoleMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色-菜单关联Mapper接口
 * <AUTHOR>
 * @date 2025-05-29
 * @version 1.0.0
 */
@Mapper
public interface RoleMenuMapper extends BaseMapper<RoleMenu> {

    /**
     * 根据角色ID列表获取关联的菜单ID列表
     * @param roleIds 角色ID列表
     * @return 菜单ID列表
     */
    List<Long> selectMenuIdsByRoleIds(@Param("roleIds") List<Long> roleIds);
} 