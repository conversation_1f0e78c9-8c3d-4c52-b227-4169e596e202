package com.dz.ms.lowcode.service.generator.impl;

import com.dz.ms.lowcode.entity.MetaEntity;
import com.dz.ms.lowcode.entity.MetaField;
import com.dz.ms.lowcode.dto.MetaFieldDTO;
import com.dz.ms.lowcode.service.MetaFieldService;
import com.dz.ms.lowcode.service.generator.CodeGeneratorService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代码生成器实现类
 * <AUTHOR>
 * @date 2023-07-01
 */
@Service
@Slf4j
public class CodeGeneratorServiceImpl implements CodeGeneratorService {

    @Autowired
    private Configuration freemarkerConfig;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private MetaFieldService metaFieldService;

    // @Autowired
    // private FreemarkerConfig freemarkerConfig; // 假设已注入Freemarker配置

    @Override
    public void generateCode(MetaEntity entity, List<MetaField> fields) {
        // 1. 生成数据库表
        String tableSql = generateTable(entity, fields);
        if (StringUtils.hasText(tableSql)) {
            jdbcTemplate.execute(tableSql);
        }
        
        // 2. 生成Java代码
        String basePackage = "com.dz.ms.dynamic";
        String targetPath = "src/main/java/com/dz/ms/dynamic";
        
        // 生成实体类
        String entityCode = generateEntity(entity, fields);
        saveFile(targetPath + "/entity/" + entity.getEntityCode() + ".java", entityCode);
        
        // 生成Mapper接口
        String mapperCode = generateMapper(entity, fields);
        saveFile(targetPath + "/mapper/" + entity.getEntityCode() + "Mapper.java", mapperCode);
        
        // 生成Service接口
        String serviceCode = generateService(entity, fields);
        saveFile(targetPath + "/service/" + entity.getEntityCode() + "Service.java", serviceCode);
        
        // 生成Service实现类
        String serviceImplCode = generateServiceImpl(entity, fields);
        saveFile(targetPath + "/service/impl/" + entity.getEntityCode() + "ServiceImpl.java", serviceImplCode);
        
        // 生成Controller
        String controllerCode = generateController(entity, fields);
        saveFile(targetPath + "/controller/" + entity.getEntityCode() + "Controller.java", controllerCode);
    
        // 3. 生成前端页面（如果需要）
        generateVueList(entity, fields);
        //generateVueForm(entity, fields);
        generateApiService(entity);
    }

    @Override
    public String generateTable(MetaEntity entity, List<MetaField> fields) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE IF NOT EXISTS `").append(entity.getTableName()).append("` (");
        sql.append("`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',");
        
        // 添加字段
        for (MetaField field : fields) {
            sql.append("`").append(field.getColumnName()).append("` ");
            
            // 根据数据类型生成对应的SQL类型
            String sqlType = getSqlType(field);
            sql.append(sqlType);
            
            // 是否必填
            if (field.getRequired() != null && field.getRequired()) {
                sql.append(" NOT NULL");
            } else {
                sql.append(" DEFAULT NULL");
            }
            
            // 字段注释
            if (StringUtils.hasText(field.getFieldName())) {
                sql.append(" COMMENT '").append(field.getFieldName()).append("'");
            }
            
            sql.append(",");
        }
        
        // 添加基础字段
        sql.append("`creator` varchar(64) DEFAULT NULL COMMENT '创建人',");
        sql.append("`create_time` datetime DEFAULT NULL COMMENT '创建时间',");
        sql.append("`updater` varchar(64) DEFAULT NULL COMMENT '更新人',");
        sql.append("`update_time` datetime DEFAULT NULL COMMENT '更新时间',");
        
        // 添加主键
        sql.append("PRIMARY KEY (`id`)");
        
        // 添加唯一索引
        for (MetaField field : fields) {
            if (field.getUnique() != null && field.getUnique()) {
                sql.append(",UNIQUE KEY `uk_").append(field.getColumnName()).append("` (`").append(field.getColumnName()).append("`)");
            }
        }
        
        sql.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='").append(entity.getEntityName()).append("';");
        
        return sql.toString();
    }

    @Override
    public String generateEntity(MetaEntity entity, List<MetaField> fields) {
        try {
            Template template = freemarkerConfig.getTemplate("entity.ftl");
            Map<String, Object> data = new HashMap<>();
            data.put("basePackage", "com.dz.ms.dynamic");
            data.put("entityCode", entity.getEntityCode());
            data.put("entityName", entity.getEntityName());
            data.put("tableName", entity.getTableName());
            data.put("fields", fields);
            
            StringWriter writer = new StringWriter();
            template.process(data, writer);
            return writer.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String generateMapper(MetaEntity entity, List<MetaField> fields) {
        try {
            Template template = freemarkerConfig.getTemplate("mapper.ftl");
            Map<String, Object> data = new HashMap<>();
            data.put("basePackage", "com.dz.ms.dynamic");
            data.put("entityCode", entity.getEntityCode());
            
            StringWriter writer = new StringWriter();
            template.process(data, writer);
            return writer.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String generateService(MetaEntity entity, List<MetaField> fields) {
        try {
            Template template = freemarkerConfig.getTemplate("service.ftl");
            Map<String, Object> data = new HashMap<>();
            data.put("basePackage", "com.dz.ms.dynamic");
            data.put("entityCode", entity.getEntityCode());
            
            StringWriter writer = new StringWriter();
            template.process(data, writer);
            return writer.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String generateServiceImpl(MetaEntity entity, List<MetaField> fields) {
        try {
            Template template = freemarkerConfig.getTemplate("serviceImpl.ftl");
            Map<String, Object> data = new HashMap<>();
            data.put("basePackage", "com.dz.ms.dynamic");
            data.put("entityCode", entity.getEntityCode());
            
            StringWriter writer = new StringWriter();
            template.process(data, writer);
            return writer.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String generateController(MetaEntity entity, List<MetaField> fields) {
        try {
            Template template = freemarkerConfig.getTemplate("controller.ftl");
            Map<String, Object> data = new HashMap<>();
            data.put("basePackage", "com.dz.ms.dynamic");
            data.put("entityCode", entity.getEntityCode());
            data.put("entityName", entity.getEntityName());
            data.put("apiPath", entity.getEntityCode().toLowerCase());
            
            StringWriter writer = new StringWriter();
            template.process(data, writer);
            return writer.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 根据字段类型获取SQL类型
     * @param field 字段
     * @return SQL类型
     */
    private String getSqlType(MetaField field) {
        String dataType = field.getDataType();
        Integer length = field.getLength();
        
        if ("String".equals(dataType)) {
            if (length == null || length <= 0) {
                length = 255;
            }
            return "varchar(" + length + ")";
        } else if ("Integer".equals(dataType)) {
            return "int(11)";
        } else if ("Long".equals(dataType)) {
            return "bigint(20)";
        } else if ("Double".equals(dataType)) {
            return "double";
        } else if ("Boolean".equals(dataType)) {
            return "tinyint(1)";
        } else if ("Date".equals(dataType)) {
            return "datetime";
        } else if ("BigDecimal".equals(dataType)) {
            return "decimal(19,2)";
        } else if ("Text".equals(dataType)) {
            return "text";
        } else {
            return "varchar(255)";
        }
    }
    
    /**
     * 保存文件
     * @param filePath 文件路径
     * @param content 文件内容
     */
    private void saveFile(String filePath, String content) {
        if (!StringUtils.hasText(content)) {
            return;
        }

        File file = new File(filePath);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }

        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(filePath), StandardCharsets.UTF_8))) {
            writer.write(content);
            log.info("====保存文件成功: {}", filePath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    ////////////////////////// 前端生成 /////////////////////////////

    /**
     * 生成Vue列表页
     */
    private void generateVueList(MetaEntity entity, List<MetaField> fields) {
        try {
            Template template = freemarkerConfig.getTemplate("vue/list.vue.ftl");
            Map<String, Object> data = new HashMap<>();
            data.put("entity", entity);
            data.put("fields", fields);
            data.put("entityCode", entity.getEntityCode());

            StringWriter writer = new StringWriter();
            template.process(data, writer);
            
            // 保存路径：/frontend/src/views/Dynamic/{entityCode}/List.vue
            String filePath = "../frontend/src/views/Dynamic/" + entity.getEntityCode() + "/List.vue";
            saveFile(filePath, writer.toString());
        } catch (TemplateException | IOException e) {
            log.error("生成Vue列表页失败: {}", e.getMessage());
        }
    }

    /**
     * 生成Vue表单页（类似列表页实现）
     */
    private void generateVueForm(MetaEntity entity, List<MetaField> fields) {
        try {
            Template template = freemarkerConfig.getTemplate("vue/form.vue.ftl");
            Map<String, Object> data = new HashMap<>();
            data.put("entity", entity);
            data.put("fields", fields);
            data.put("entityCode", entity.getEntityCode());

            StringWriter writer = new StringWriter();
            template.process(data, writer);
            
            // 保存路径：/frontend/src/views/Dynamic/{entityCode}/Form.vue
            String filePath = "../frontend/src/views/Dynamic/" + entity.getEntityCode() + "/Form.vue";
            saveFile(filePath, writer.toString());
        } catch (TemplateException | IOException e) {
            log.error("生成Vue表单页失败: {}", e.getMessage());
        }
    }

    /**
     * 生成前端API接口文件
     */
    private void generateApiService(MetaEntity entity) {
        try {
            Template template = freemarkerConfig.getTemplate("vue/api.ts.ftl");
            Map<String, Object> data = new HashMap<>();
            data.put("entityCode", entity.getEntityCode());
            data.put("apiPath", entity.getEntityCode().toLowerCase());

            StringWriter writer = new StringWriter();
            template.process(data, writer);
            
            // 保存路径：/frontend/src/api/{entityCode?lower_case}.ts
            String filePath = "../frontend/src/api/" + entity.getEntityCode().toLowerCase() + ".ts";
            saveFile(filePath, writer.toString());
        } catch (TemplateException | IOException e) {
            log.error("生成前端API失败: {}", e.getMessage());
        }
    }

    /**
     * 生成ALTER TABLE SQL，对比旧字段和新字段
     */
    public String generateAlterTableSql(Long entityId, List<MetaFieldDTO> newFieldDTOs) {
        MetaEntity entity = null;
        if (entityId != null) {
            entity = jdbcTemplate.queryForObject(
                "SELECT * FROM lc_meta_entity WHERE id = ?", 
                (rs, rowNum) -> {
                    MetaEntity e = new MetaEntity();
                    e.setId(rs.getLong("id"));
                    e.setTableName(rs.getString("table_name"));
                    e.setEntityName(rs.getString("entity_name"));
                    return e;
                },
                entityId
            );
        }
        if (entity == null) return "-- 未找到实体信息";
        String tableName = entity.getTableName();
        // 检查表是否存在
        String checkSql = "SHOW TABLES LIKE ?";
        java.util.List<java.util.Map<String, Object>> tables = jdbcTemplate.queryForList(checkSql, tableName);
        if (tables == null || tables.isEmpty()) {
            return "-- 表【" + tableName + "】尚未创建，请先发布实体";
        }
        List<MetaField> oldFields = metaFieldService.getFieldsByEntityId(entityId);
        // 转换新字段
        List<MetaField> newFields = new java.util.ArrayList<>();
        for (MetaFieldDTO dto : newFieldDTOs) {
            MetaField f = new MetaField();
            BeanUtils.copyProperties(dto, f);
            newFields.add(f);
        }
        // 以columnName为key
        java.util.Map<String, MetaField> oldMap = new java.util.HashMap<>();
        for (MetaField f : oldFields) oldMap.put(f.getColumnName(), f);
        java.util.Map<String, MetaField> newMap = new java.util.HashMap<>();
        for (MetaField f : newFields) newMap.put(f.getColumnName(), f);
        StringBuilder sql = new StringBuilder();
        sql.append("ALTER TABLE `").append(tableName).append("` ");
        java.util.List<String> ops = new java.util.ArrayList<>();
        // 新增字段
        for (MetaField f : newFields) {
            if (!oldMap.containsKey(f.getColumnName())) {
                StringBuilder add = new StringBuilder();
                add.append("ADD COLUMN `").append(f.getColumnName()).append("` ")
                   .append(getSqlType(f));
                if (Boolean.TRUE.equals(f.getRequired())) {
                    add.append(" NOT NULL");
                } else {
                    add.append(" DEFAULT NULL");
                }
                if (org.springframework.util.StringUtils.hasText(f.getFieldName())) {
                    add.append(" COMMENT '").append(f.getFieldName()).append("'");
                }
                ops.add(add.toString());
            }
        }
        // 删除字段
        for (MetaField f : oldFields) {
            if (!newMap.containsKey(f.getColumnName())) {
                ops.add("DROP COLUMN `" + f.getColumnName() + "`");
            }
        }
        // 修改字段
        for (MetaField f : newFields) {
            if (oldMap.containsKey(f.getColumnName())) {
                MetaField old = oldMap.get(f.getColumnName());
                // 判断类型、长度、必填、注释等是否变化
                boolean changed = false;
                if (!getSqlType(f).equalsIgnoreCase(getSqlType(old))) changed = true;
                if (!Boolean.valueOf(f.getRequired()).equals(Boolean.valueOf(old.getRequired()))) changed = true;
                if (!org.springframework.util.StringUtils.hasText(f.getFieldName())) {
                    if (org.springframework.util.StringUtils.hasText(old.getFieldName())) changed = true;
                } else if (!f.getFieldName().equals(old.getFieldName())) changed = true;
                if (changed) {
                    StringBuilder mod = new StringBuilder();
                    mod.append("MODIFY COLUMN `").append(f.getColumnName()).append("` ")
                       .append(getSqlType(f));
                    if (Boolean.TRUE.equals(f.getRequired())) {
                        mod.append(" NOT NULL");
                    } else {
                        mod.append(" DEFAULT NULL");
                    }
                    if (org.springframework.util.StringUtils.hasText(f.getFieldName())) {
                        mod.append(" COMMENT '").append(f.getFieldName()).append("'");
                    }
                    ops.add(mod.toString());
                }
            }
        }
        if (ops.isEmpty()) return "-- 无需变更";
        sql.append(String.join(", ", ops)).append(";");
        return sql.toString();
    }

}