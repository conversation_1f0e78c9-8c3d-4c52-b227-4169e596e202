import type { Role } from './role';
import type { Menu } from './menu';
export interface User {
    id?: number;
    username: string;
    password?: string;
    nickname?: string;
    phone?: string;
    email?: string;
    status: number;
    createTime?: string;
    updateTime?: string;
}
export interface UserPageParams {
    pageNum: number;
    pageSize: number;
    username?: string;
}
export interface PasswordUpdateParams {
    oldPassword: string;
    newPassword: string;
}
interface Result<T> {
    code: number;
    msg: string;
    data: T;
}
export interface UserInfo {
    permissions: string[];
}
export declare function getUserPage(params: UserPageParams): Promise<Result<{
    records: User[];
    total: number;
}>>;
export declare function addUser(data: User): Promise<Result<number>>;
export declare function updateUser(id: number, data: User): Promise<Result<any>>;
export declare function deleteUser(id: number): Promise<Result<any>>;
export declare function getRoleIdsByUserId(userId: number): Promise<Result<number[]>>;
export declare function saveUserRoles(userId: number, roleIds: number[]): Promise<Result<any>>;
export declare function getUserMenus(): Promise<Result<Menu[]>>;
export declare function getUserDetail(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getCurrentUserInfo(): Promise<Result<User>>;
export declare function getCurrentUserRoles(): Promise<Result<Role[]>>;
export declare function updatePassword(data: PasswordUpdateParams): Promise<Result<any>>;
export {};
