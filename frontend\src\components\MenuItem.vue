<template>
  <!-- 目录类型且有子菜单，渲染为ElSubMenu -->
  <el-sub-menu v-if="menu.type === 'M' && menu.children && menu.children.length > 0" :index="menu.path || String(menu.id)">
    <template #title>
      <!-- 菜单图标 -->
      <el-icon v-if="menu.icon"><component :is="menu.icon" /></el-icon>
      <span>{{ menu.name }}</span>
    </template>
    <!-- 递归渲染子菜单 -->
    <template v-for="child in menu.children" :key="child.id">
      <menu-item :menu="child" />
    </template>
  </el-sub-menu>

  <!-- 菜单类型，渲染为ElMenuItem -->
  <el-menu-item v-else-if="menu.type === 'C'" :index="menu.path || String(menu.id)">
    <!-- 菜单图标 -->
    <el-icon v-if="menu.icon"><component :is="menu.icon" /></el-icon>
    <span>{{ menu.name }}</span>
  </el-menu-item>

  <!-- 其他类型 (如按钮F) 不在侧边栏渲染 -->
</template>

<script setup lang="ts">
import { ElSubMenu, ElMenuItem, ElIcon } from 'element-plus';
import type { Menu } from '../api/menu';

defineProps({
  menu: {
    type: Object as () => Menu,
    required: true
  }
});
</script>

<script lang="ts">
// 注册组件自身为递归组件
import { defineComponent } from 'vue';
export default defineComponent({
  name: 'MenuItem'
});
</script> 