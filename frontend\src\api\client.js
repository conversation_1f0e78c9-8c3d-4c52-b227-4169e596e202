import request from '../utils/request';
// 获取分页列表
export function getclientList(params) {
    if (!params.pageNum)
        params.pageNum = 1;
    if (!params.pageSize)
        params.pageSize = 10;
    return request.get('/api/client/page', { params });
}
// 获取所有数据
export function getAllclients() {
    return request.get('/api/client/list');
}
// 获取详情
export function getclientDetail(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.get(`/api/client/${id}`);
}
// 新增
export function saveclient(data) {
    return request.post('/api/client', data);
}
// 更新
export function updateclient(id, data) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.put(`/api/client/${id}`, data);
}
// 删除
export function deleteclient(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.delete(`/api/client/${id}`);
}
// 批量删除
export function batchDeleteclient(ids) {
    if (!ids || ids.length === 0)
        return Promise.reject('ID列表不能为空');
    return request.delete('/api/client/batch', { data: { ids } });
}
// 导出数据
export function exportclient(params) {
    return request.get('/api/client/export', {
        params,
        responseType: 'blob'
    });
}
