package com.dz.ms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色-菜单关联实体
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@Data
@TableName("role_menu")
public class RoleMenu implements Serializable {
    /** 主键ID */
    @TableId
    private Long id;

    /** 角色ID */
    @TableField("role_id")
    private Long roleId;

    /** 菜单ID */
    @TableField("menu_id")
    private Long menuId;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;
} 