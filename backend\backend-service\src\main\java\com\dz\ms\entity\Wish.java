package com.dz.ms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 心愿单实体
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@Data
@TableName("wish")
public class Wish implements Serializable {
    /** 主键ID */
    @TableId
    private Long id;

    /** 会员ID */
    @TableField("member_id")
    private Long memberId;

    /** 心愿内容 */
    @TableField("content")
    private String content;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;

    /** 状态（0-无效 1-有效） */
    @TableField("status")
    private Integer status;
} 