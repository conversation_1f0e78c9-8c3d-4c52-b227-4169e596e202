package com.dz.ms.common;

/**
 * 业务异常
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
public class BizException extends RuntimeException {
    private int code = 1;
    private String msg;

    public BizException(String msg) {
        super(msg);
        this.msg = msg;
    }

    public BizException(int code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
} 