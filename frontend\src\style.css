/* 全局样式 */

html,
body {
  margin: 0;
  padding: 0;
  height: 100%; /* 确保html和body高度为100% */
  width: 100%; /* 确保html和body宽度为100% */
  overflow: hidden; /* 避免出现滚动条 */
}

/* 其他全局样式 */

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  /* 移除或调整与铺满屏幕冲突的样式 */
  /* display: flex; */ /* 移除此行 */
  /* place-items: center; */ /* 移除此行 */
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  /* 移除或调整与铺满屏幕冲突的样式 */
  max-width: none; /* 移除最大宽度限制 */
  margin: 0; /* 移除自动边距 */
  padding: 0; /* 移除内边距 */
  width: 100%; /* 设置宽度为100% */
  height: 100%; /* 设置高度为100% */
  text-align: left; /* 将文本对齐方式改回左对齐 */
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* 页面通用容器样式 */
.page-container {
  padding: 4px;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
