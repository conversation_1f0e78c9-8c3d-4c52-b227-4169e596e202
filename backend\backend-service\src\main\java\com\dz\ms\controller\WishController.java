package com.dz.ms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.ms.dto.WishDTO;
import com.dz.ms.entity.Wish;
import com.dz.ms.service.WishService;
import com.dz.ms.vo.WishVO;
import com.dz.ms.common.Result;
import com.dz.ms.common.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 心愿单管理
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
@RestController
@RequestMapping("/wish")
@Api(tags = "心愿单管理")
@Validated
public class WishController {

    @Autowired
    private WishService wishService;

    /**
     * 新增心愿单
     * @param dto 心愿单DTO
     * @return Result<Boolean>
     */
    @PostMapping
    @ApiOperation("新增心愿单")
    @PreAuthorize("hasAuthority('wish:add')")
    public Result<Boolean> addWish(@Valid @RequestBody WishDTO dto) {
        Wish wish = new Wish();
        BeanUtils.copyProperties(dto, wish);
        boolean saved = wishService.save(wish);
        return Result.success(saved);
    }

    /**
     * 编辑心愿单
     * @param id 心愿单ID
     * @param dto 心愿单DTO
     * @return Result<Boolean>
     */
    @PutMapping("/{id}")
    @ApiOperation("编辑心愿单")
    @PreAuthorize("hasAuthority('wish:edit')")
    public Result<Boolean> updateWish(@PathVariable Long id, @Valid @RequestBody WishDTO dto) {
        Wish wish = new Wish();
        BeanUtils.copyProperties(dto, wish);
        wish.setId(id);
        boolean updated = wishService.updateById(wish);
        return Result.success(updated);
    }

    /**
     * 删除心愿单
     * @param id 心愿单ID
     * @return Result<Boolean>
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除心愿单")
    @PreAuthorize("hasAuthority('wish:delete')")
    public Result<Boolean> deleteWish(@PathVariable Long id) {
        boolean removed = wishService.removeById(id);
        return Result.success(removed);
    }

    /**
     * 分页查询心愿单
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @param memberId 会员ID（可选）
     * @param keyword 会员用户名或昵称关键字（可选）
     * @param status 状态（可选）
     * @return Result<IPage<WishVO>>
     */
    @GetMapping("/page")
    @ApiOperation("分页查询心愿单")
    @PreAuthorize("hasAuthority('wish:view')")
    public Result<IPage<WishVO>> pageWish(@RequestParam(defaultValue = "1") int pageNum,
                                          @RequestParam(defaultValue = "10") int pageSize,
                                          @RequestParam(required = false) Long memberId,
                                          @RequestParam(required = false) String keyword,
                                          @RequestParam(required = false) Integer status) {
        Page<Wish> page = new Page<>(pageNum, pageSize);
        IPage<WishVO> voPage = wishService.pageWishWithMember(page, memberId, keyword, status);
        return Result.success(voPage);
    }

    /**
     * 心愿单详情
     * @param id 心愿单ID
     * @return Result<WishVO>
     */
    @GetMapping("/{id}")
    @ApiOperation("心愿单详情")
    @PreAuthorize("hasAuthority('wish:view')")
    public Result<WishVO> getWish(@PathVariable Long id) {
        Wish wish = wishService.getById(id);
        if (wish == null) {
            throw new BizException("心愿单不存在");
        }
        WishVO vo = new WishVO();
        BeanUtils.copyProperties(wish, vo);
        return Result.success(vo);
    }
} 