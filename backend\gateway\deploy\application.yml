server:
  port: 9090


spring:
  application:
    name: rtm-gateway
  config:
    import: "optional:nacos:rtm-gateway.yaml"
  cloud:
    gateway:
      routes:
        # 后端服务路由 - 将/api/**请求转发到后端服务
        - id: backend-service
          uri: http://rtm-backend-service:9091
          predicates:
            - Path=/api/**
          filters:
            # 去掉/api前缀，因为后端服务的接口路径不包含/api
            - StripPrefix=1
            # 添加请求头
            - AddRequestHeader=X-Gateway-Source, rtm-gateway
          metadata:
            # Sentinel资源名称
            resource-name: backend-service
            
    # Nacos服务发现配置
    nacos:
      server-addr: nacos-service:8848 
      discovery:
        # 临时关闭服务注册
        register-enabled: false  
        
    # Sentinel配置
    sentinel:
      transport:
        # sentinel控制台地址
        dashboard: sentinel-dashboard-service:8858
        # 指定应用与Sentinel控制台交互的端口
        port: 8719
      http-method-specify: true
      eager: true
      
      

# 日志配置
logging:
  level:
    com.dz.ms.gateway: DEBUG
    org.springframework.cloud.gateway: DEBUG
    
# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
