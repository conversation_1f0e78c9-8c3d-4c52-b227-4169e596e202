package com.dz.ms.lowcode.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.lowcode.entity.MetaField;

import java.util.List;

/**
 * 元数据字段Service接口
 * <AUTHOR>
 * @date 2023-07-01
 */
public interface MetaFieldService extends IService<MetaField> {
    
    /**
     * 根据实体ID查询字段列表
     * @param entityId 实体ID
     * @return 字段列表
     */
    List<MetaField> getFieldsByEntityId(Long entityId);
    
    /**
     * 批量保存字段
     * @param fields 字段列表
     * @param entityId 实体ID
     * @return 是否成功
     */
    boolean saveFields(List<MetaField> fields, Long entityId);
    
    /**
     * 批量更新字段
     * @param fields 字段列表
     * @param entityId 实体ID
     * @return 是否成功
     */
    boolean updateFields(List<MetaField> fields, Long entityId);
    
    /**
     * 根据实体ID删除所有字段
     * @param entityId 实体ID
     * @return 是否成功
     */
    boolean deleteByEntityId(Long entityId);
} 