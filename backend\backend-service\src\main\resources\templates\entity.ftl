package ${basePackage}.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
<#if fields?? && fields?size gt 0>
    <#list fields as field>
        <#if field.javaType == "BigDecimal">
import java.math.BigDecimal;
        </#if>
    </#list>
</#if>

/**
 * ${entityName}实体类
 * 由低代码平台自动生成
 */
@Data
@TableName("${tableName}")
public class ${entityCode} implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
<#if fields?? && fields?size gt 0>
    <#list fields as field>
    /** ${field.fieldName} */
    @TableField("${field.columnName}")
    private ${field.javaType} ${field.fieldCode};
    
    </#list>
</#if>
    /** 创建人 */
    @TableField("creator")
    private String creator;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 更新人 */
    @TableField("updater")
    private String updater;

    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;
} 