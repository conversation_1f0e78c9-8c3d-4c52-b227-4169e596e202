package com.dz.ms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.entity.Wish;
import com.dz.ms.vo.WishVO;

/**
 * 心愿单Service接口
 * <AUTHOR>
 * @date 2025-05-27
 * @version 1.0.0
 */
public interface WishService extends IService<Wish> {
    
    /**
     * 分页查询心愿单，关联会员信息
     * @param page 分页参数
     * @param memberId 会员ID，可为null
     * @param keyword 会员用户名或昵称关键字，可为null
     * @param status 状态，可为null
     * @return 分页结果
     */
    IPage<WishVO> pageWishWithMember(Page<Wish> page, Long memberId, String keyword, Integer status);
} 