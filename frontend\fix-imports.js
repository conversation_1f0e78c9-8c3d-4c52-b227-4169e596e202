import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 要修复的文件列表
const filesToFix = [
  'src/views/Wish/WishList.vue',
  'src/views/User/UserList.vue',
  'src/views/Role/RoleList.vue',
  'src/views/My/MyAccount.vue',
  'src/views/Menu/MenuList.vue',
  'src/views/Member/MemberList.vue'
];

// 对每个文件进行修复
filesToFix.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  
  // 读取文件内容
  let content = fs.readFileSync(fullPath, 'utf8');
  
  // 修改导入语句
  content = content.replace(
    /import\s*{([^}]*)FormInstance([^}]*),([^}]*)FormRules([^}]*)}(\s*)from(\s*)'element-plus'/g,
    (match, before, afterFormInstance, beforeFormRules, afterFormRules, spaces, spacesAfter) => {
      // 移除 FormInstance 和 FormRules
      let newImport = `import {${before}${afterFormInstance},${beforeFormRules}${afterFormRules}}${spaces}from${spacesAfter}'element-plus'`;
      
      // 如果导入只剩下花括号，则修复
      newImport = newImport.replace(/import\s*{\s*,\s*}\s*from\s*'element-plus'/, `import ${spaces}from${spacesAfter}'element-plus'`);
      
      // 添加类型导入
      newImport += `\nimport type { FormInstance, FormRules } from 'element-plus';`;
      
      return newImport;
    }
  );
  
  // 修复可能被过度处理的导入语句
  content = content.replace(/import\s+from\s+'element-plus'/, "import { ElMessage, ElMessageBox } from 'element-plus'");
  
  // 写回文件
  fs.writeFileSync(fullPath, content, 'utf8');
  console.log(`Fixed imports in ${filePath}`);
});

console.log('All files fixed successfully!'); 