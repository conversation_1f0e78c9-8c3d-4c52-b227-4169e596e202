
FROM openjdk:17-jdk-bullseye

# 安装基础依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    libfreetype6 \
    fontconfig \
    wget \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# 手动安装DejaVu字体（添加调试）
RUN mkdir -p /usr/share/fonts/truetype/dejavu

# 下载字体文件
RUN wget -O /tmp/dejavu-fonts.zip \
    https://github.com/dejavu-fonts/dejavu-fonts/releases/download/version_2_37/dejavu-fonts-ttf-2.37.zip

# 解压并查看目录结构
RUN unzip /tmp/dejavu-fonts.zip -d /tmp && \
    echo "=== 解压后的目录结构 ===" && \
    ls -alhR /tmp && \
    echo "=== TTF文件位置 ===" && \
    find /tmp -name "*.ttf"

# 移动字体文件（根据实际结构调整）
RUN find /tmp -name "*.ttf" -exec mv {} /usr/share/fonts/truetype/dejavu/ \; && \
    rm -rf /tmp/*

# 刷新字体缓存
RUN fc-cache -f -v
	
	

WORKDIR /app
COPY backend-service-1.0.0.jar /app/
# 复制配置文件到容器内
COPY application.yml /app/config/




# 指定配置文件位置和Java系统属性
CMD ["java", "-jar", "-Djava.awt.headless=true", "-Dspring.config.location=file:/app/config/application.yml", "backend-service-1.0.0.jar"]

EXPOSE 9091