export interface MetaField {
    id?: number;
    entityId?: number;
    fieldCode: string;
    fieldName: string;
    columnName?: string;
    dataType: string;
    javaType?: string;
    length?: number;
    required?: boolean;
    unique?: boolean;
    showInList?: boolean;
    showInForm?: boolean;
    searchable?: boolean;
    description?: string;
}
export interface MetaEntity {
    id?: number;
    entityCode: string;
    entityName: string;
    tableName: string;
    description?: string;
    published?: boolean;
    enabled?: boolean;
    fields?: MetaField[];
    createTime?: string;
    updateTime?: string;
}
export interface EntityPageQuery {
    page?: number;
    size?: number;
    keyword?: string;
}
export declare function getEntityPage(params: EntityPageQuery): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getEntityById(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function saveEntity(data: MetaEntity): Promise<import("axios").AxiosResponse<any, any>>;
export declare function updateEntity(data: MetaEntity): Promise<import("axios").AxiosResponse<any, any>>;
export declare function deleteEntity(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function publishEntity(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function unpublishEntity(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare const dataTypeOptions: {
    label: string;
    value: string;
}[];
export declare function getJavaType(dataType: string): string;
export declare function generateAlterSql(entityId: number, fields: MetaField[]): Promise<import("axios").AxiosResponse<any, any>>;
export declare function executeSql(sql: string): Promise<import("axios").AxiosResponse<any, any>>;
