<template>
  <div class="page-container">
    <el-card>
      <div style="margin-bottom: 16px; display: flex; gap: 8px; align-items: center;">
        <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px">
          <el-option :value="1" label="有效" />
          <el-option :value="0" label="无效" />
        </el-select>
        <el-input v-model="searchForm.keyword" placeholder="会员用户名/昵称" style="width: 200px" clearable />
        <el-button type="primary" @click="fetchData">搜索</el-button>
        <el-button type="success" @click="openAdd">新增心愿</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="memberId" label="会员ID" width="80" />
        <el-table-column prop="memberUsername" label="会员用户名" width="120" />
        <el-table-column prop="memberNickname" label="会员昵称" width="120" />
        <el-table-column prop="content" label="心愿内容" />
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="openEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="pageNum"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchData"
        @current-change="fetchData"
        style="margin-top: 16px; text-align: right;"
      />
    </el-card>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="400px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="会员" prop="memberId">
          <el-select 
            v-model="form.memberId" 
            filterable 
            placeholder="请选择会员"
            style="width: 100%">
            <el-option 
              v-for="item in memberList" 
              :key="item.id" 
              :label="`${item.id} - ${item.username}${item.nickname ? ' (' + item.nickname + ')' : ''}`" 
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="心愿内容" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status">
            <el-option :value="1" label="有效" />
            <el-option :value="0" label="无效" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage,ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus';
import { getWishPage, addWish, updateWish, deleteWish } from '../../api/wish';
import type { Wish } from '../../api/wish';;
import { getAllMembers } from '../../api/member';
import type { Member } from '../../api/member';;

const searchForm = reactive({ keyword: '', status: '' as any });
const tableData = ref<Wish[]>([]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);

const dialogVisible = ref(false);
const dialogTitle = ref('');
const form = reactive<Wish>({ content: '', status: 1 });
const formRef = ref<FormInstance>();
const memberList = ref<Member[]>([]);

const rules: FormRules = {
  memberId: [{ required: true, message: '请选择会员', trigger: 'change' }],
  content: [{ required: true, message: '请输入心愿内容', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
};

// 获取会员列表
function fetchMemberList() {
  getAllMembers().then(res => {
    if (res && res.code === 0) {
      memberList.value = res.data || [];
    }
  }).catch(error => {
    console.error("获取会员列表失败:", error);
    memberList.value = [];
  });
}

function fetchData() {
  const params: any = {
    pageNum: pageNum.value,
    pageSize: pageSize.value
  };
  
  if (searchForm.keyword) {
    params.keyword = searchForm.keyword;
  }
  
  if (searchForm.status !== '') {
    params.status = searchForm.status;
  }
  
  getWishPage(params).then(res => {
    const { records, total: t } = res.data;
    tableData.value = records;
    total.value = t;
  }).catch(error => {
    console.error("获取心愿列表失败:", error);
    tableData.value = [];
    total.value = 0;
  });
}

function openAdd() {
  dialogTitle.value = '新增心愿';
  Object.assign(form, { id: undefined, memberId: undefined, content: '', status: 1 });
  dialogVisible.value = true;
}

function openEdit(row: Wish) {
  dialogTitle.value = '编辑心愿';
  Object.assign(form, row);
  dialogVisible.value = true;
}

async function handleSubmit() {
  formRef.value?.validate(async (valid) => {
    if (!valid) return;
    try {
      if (!form.id) {
        await addWish(form);
        ElMessage.success('新增成功');
      } else {
        await updateWish(form.id, form);
        ElMessage.success('编辑成功');
      }
      dialogVisible.value = false;
      fetchData();
    } catch (error) {
      console.error("提交心愿信息失败:", error);
      ElMessage.error('操作失败');
    }
  });
}

async function handleDelete(id?: number) {
  if (!id) return;
  
  ElMessageBox.confirm(
    '确定要删除该心愿吗？此操作不可恢复',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const res = await deleteWish(id);
        if (res && res.code === 0) {
          ElMessage.success('删除成功');
          fetchData();
        } else {
          ElMessage.error(res?.msg || '删除失败');
        }
      } catch (error) {
        console.error("删除心愿失败:", error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 用户取消删除操作
      ElMessage.info('已取消删除');
    });
}

onMounted(() => {
  fetchData();
  fetchMemberList();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}
</style> 