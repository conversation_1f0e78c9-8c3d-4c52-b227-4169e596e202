import type { Router } from 'vue-router';
declare module 'vue-router' {
    interface RouteMeta {
        title?: string;
        permission?: string;
    }
}
declare const router: Router;
export interface MenuItem {
    type: string;
    path: string;
    component?: string;
    name?: string;
    permission?: string;
    meta?: Record<string, any>;
    children?: MenuItem[];
}
export declare function loadDynamicRoutes(menus: MenuItem[]): void;
export declare function loadDynamicRoutes0(menus: MenuItem[]): void;
export default router;
