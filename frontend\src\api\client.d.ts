export interface client {
    id?: number;
    createTime?: string;
    updateTime?: string;
}
export interface clientPageQuery {
    pageNum?: number;
    pageSize?: number;
}
export declare function getclientList(params: clientPageQuery): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getAllclients(): Promise<import("axios").AxiosResponse<any, any>>;
export declare function getclientDetail(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function saveclient(data: client): Promise<import("axios").AxiosResponse<any, any>>;
export declare function updateclient(id: number, data: client): Promise<import("axios").AxiosResponse<any, any>>;
export declare function deleteclient(id: number): Promise<import("axios").AxiosResponse<any, any>>;
export declare function batchDeleteclient(ids: number[]): Promise<import("axios").AxiosResponse<any, any>>;
export declare function exportclient(params?: clientPageQuery): Promise<import("axios").AxiosResponse<any, any>>;
