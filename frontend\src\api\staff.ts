import request from '../utils/request';

// 实体接口定义
export interface staff {
  id?: number;
  createTime?: string;
  updateTime?: string;
}

// 分页查询参数接口
export interface staffPageQuery {
  pageNum?: number;
  pageSize?: number;
}

// 获取分页列表
export function getstaffList(params: staffPageQuery) {
  if (!params.pageNum) params.pageNum = 1;
  if (!params.pageSize) params.pageSize = 10;
  return request.get('/api/staff/page', { params });
}

// 获取所有数据
export function getAllstaffs() {
  return request.get('/api/staff/list');
}

// 获取详情
export function getstaffDetail(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.get(`/api/staff/${id}`);
}

// 新增
export function savestaff(data: staff) {
  return request.post('/api/staff', data);
}

// 更新
export function updatestaff(id: number, data: staff) {
  if (!id) return Promise.reject('ID不能为空');
  return request.put(`/api/staff/${id}`, data);
}

// 删除
export function deletestaff(id: number) {
  if (!id) return Promise.reject('ID不能为空');
  return request.delete(`/api/staff/${id}`);
}

// 批量删除
export function batchDeletestaff(ids: number[]) {
  if (!ids || ids.length === 0) return Promise.reject('ID列表不能为空');
  return request.delete('/api/staff/batch', { data: { ids } });
}

// 导出数据
export function exportstaff(params?: staffPageQuery) {
  return request.get('/api/staff/export', { 
    params,
    responseType: 'blob'
  });
}