import request from '../utils/request';
// 分页查询用户
// request工具函数会返回Result对象，我们需要从data中提取分页数据
export async function getUserPage(params) {
    return request({
        url: '/user/page',
        method: 'get',
        params
    });
}
// 新增用户
// request工具函数会返回Result对象，我们可能只需要判断code或获取data中的boolean/id
export async function addUser(data) {
    return request({
        url: '/user',
        method: 'post',
        data
    });
}
// 编辑用户
// request工具函数会返回Result对象，我们可能只需要判断code或获取data中的boolean
export async function updateUser(id, data) {
    return request({
        url: `/user/${id}`,
        method: 'put',
        data
    });
}
// 删除用户
// request工具函数会返回Result对象，我们可能只需要判断code或获取data中的boolean
export async function deleteUser(id) {
    return request({
        url: `/user/${id}`,
        method: 'delete'
    });
}
// 根据用户ID获取角色ID列表
// request工具函数会返回Result对象，我们需要从data中提取number[]
export async function getRoleIdsByUserId(userId) {
    return request({
        url: `/user-role/roleIds/${userId}`,
        method: 'get',
    });
}
// 保存用户角色关联
// request工具函数会返回Result对象，我们可能只需要判断code或获取data中的boolean
export async function saveUserRoles(userId, roleIds) {
    return request({
        url: `/user-role/save/${userId}`,
        method: 'post',
        data: roleIds
    });
}
// 假设后端有一个接口 /user/menus 用于获取当前用户的菜单树
// request工具函数会返回Result对象，我们需要从data中提取菜单树数据
export async function getUserMenus() {
    return request({
        url: '/user/menus', // 假设的后端接口地址
        method: 'get',
    });
}
export function getUserDetail(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.get(`/user/${id}`);
}
// 获取当前登录用户信息
export async function getCurrentUserInfo() {
    return request({
        url: '/user/current',
        method: 'get'
    });
}
// 获取当前登录用户的角色
export async function getCurrentUserRoles() {
    return request({
        url: '/user/current/roles',
        method: 'get'
    });
}
// 修改密码
export async function updatePassword(data) {
    // 获取当前用户ID
    return request({
        url: '/user/updatePassword', // 修改为正确的API路径，基于后端实际接口
        method: 'post', // 修改为post方法，通常密码更新使用post
        data
    });
}
