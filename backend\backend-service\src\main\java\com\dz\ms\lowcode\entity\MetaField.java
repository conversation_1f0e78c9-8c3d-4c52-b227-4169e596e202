package com.dz.ms.lowcode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 元数据字段类，用于存储用户自定义实体的字段定义
 * <AUTHOR>
 * @date 2023-07-01
 */
@Data
@TableName("lc_meta_field")
public class MetaField implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 所属实体ID */
    @TableField("entity_id")
    private Long entityId;

    /** 字段编码 */
    @TableField("field_code")
    private String fieldCode;

    /** 字段名称 */
    @TableField("field_name")
    private String fieldName;

    /** 字段描述 */
    @TableField("description")
    private String description;

    /** 数据库字段名 */
    @TableField("column_name")
    private String columnName;

    /** 数据类型 */
    @TableField("data_type")
    private String dataType;

    /** Java类型 */
    @TableField("java_type")
    private String javaType;

    /** 字段长度 */
    @TableField("length")
    private Integer length;

    /** 是否必填 */
    @TableField("is_required")
    private Boolean required;

    /** 是否唯一 */
    @TableField("is_unique")
    private Boolean unique;

    /** 默认值 */
    @TableField("default_value")
    private String defaultValue;

    /** 验证规则(JSON格式) */
    @TableField("validation_rules")
    private String validationRules;

    /** 是否在列表中显示 */
    @TableField("show_in_list")
    private Boolean showInList;

    /** 是否在表单中显示 */
    @TableField("show_in_form")
    private Boolean showInForm;

    /** 是否可搜索 */
    @TableField("is_searchable")
    private Boolean searchable;

    /** 排序号 */
    @TableField("sort_no")
    private Integer sortNo;

    /** 创建人 */
    @TableField("creator")
    private String creator;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 更新人 */
    @TableField("updater")
    private String updater;

    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;
} 