@echo off
echo 正在重新部署前端项目...

echo 1. 停止并删除现有容器...
docker stop rtm-web 2>nul
docker rm rtm-web 2>nul

echo 2. 删除旧镜像...
docker rmi rtm-web 2>nul

echo 3. 构建前端项目...
call npm run build
if %errorlevel% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo 4. 构建Docker镜像...
docker build -t rtm-web .
if %errorlevel% neq 0 (
    echo Docker镜像构建失败！
    pause
    exit /b 1
)

echo 5. 启动容器...
docker run -d -p 80:80 --name rtm-web rtm-web
if %errorlevel% neq 0 (
    echo 容器启动失败！
    pause
    exit /b 1
)

echo 6. 检查容器状态...
docker ps | findstr rtm-web

echo.
echo 部署完成！
echo 前端访问地址: http://localhost
echo API会自动转发到: http://**********:9090
echo.
echo 查看容器日志: docker logs rtm-web
echo 进入容器: docker exec -it rtm-web sh
echo.
pause
