/// <reference types="../../../../node_modules/.vue-global-types/vue_3.5_0_0_0.d.ts" />
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标已全局注册;
import { getEntityPage, deleteEntity, publishEntity, unpublishEntity } from '@/api/lowcode';
import EntityForm from './components/EntityForm.vue';
// 查询参数
const queryParams = reactive({
    page: 1,
    size: 10,
    keyword: ''
});
// 数据列表
const entityList = ref([]);
const total = ref(0);
const loading = ref(false);
// 表单相关
const entityFormVisible = ref(false);
const entityFormData = ref(null);
const formType = ref('add');
// 初始化加载数据
onMounted(() => {
    getList();
});
// 获取实体列表
const getList = async () => {
    loading.value = true;
    try {
        const res = await getEntityPage(queryParams);
        entityList.value = res.data.records;
        total.value = res.data.total;
    }
    catch (error) {
        console.error(error);
        ElMessage.error('获取实体列表失败');
    }
    finally {
        loading.value = false;
    }
};
// 处理搜索
const handleSearch = () => {
    queryParams.page = 1;
    getList();
};
// 处理页面大小变化
const handleSizeChange = (size) => {
    queryParams.size = size;
    getList();
};
// 处理页码变化
const handleCurrentChange = (page) => {
    queryParams.page = page;
    getList();
};
// 打开实体表单
const openEntityForm = (row) => {
    if (row) {
        formType.value = 'edit';
        entityFormData.value = { ...row };
    }
    else {
        formType.value = 'add';
        entityFormData.value = null;
    }
    entityFormVisible.value = true;
};
// 处理表单提交成功
const handleFormSuccess = () => {
    entityFormVisible.value = false;
    getList();
};
// 处理删除
const handleDelete = (id) => {
    ElMessageBox.confirm('确定要删除该实体吗？删除后不可恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            const res = await deleteEntity(id);
            if (res.data.success) {
                ElMessage.success('删除成功');
                getList();
            }
            else {
                ElMessage.error(res.data.message || '删除失败');
            }
        }
        catch (error) {
            console.error(error);
            ElMessage.error('删除失败');
        }
    }).catch(() => { });
};
// 处理发布
const handlePublish = (id) => {
    ElMessageBox.confirm('确定要发布该实体吗？发布后将生成相应的数据库表和代码。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            const res = await publishEntity(id);
            if (res.success) {
                ElMessage.success('发布成功');
                getList();
            }
            else {
                ElMessage.error(res.message || '发布失败');
            }
        }
        catch (error) {
            console.error(error);
            ElMessage.error('发布失败');
        }
    }).catch(() => { });
};
// 处理取消发布
const handleUnpublish = (id) => {
    ElMessageBox.confirm('确定要取消发布该实体吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            const res = await unpublishEntity(id);
            if (res.success) {
                ElMessage.success('取消发布成功');
                getList();
            }
            else {
                ElMessage.error(res.message || '取消发布失败');
            }
        }
        catch (error) {
            console.error(error);
            ElMessage.error('取消发布失败');
        }
    }).catch(() => { });
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "entity-manage" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "toolbar" },
});
const __VLS_0 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
    ...{ 'onClick': {} },
    type: "primary",
}));
const __VLS_2 = __VLS_1({
    ...{ 'onClick': {} },
    type: "primary",
}, ...__VLS_functionalComponentArgsRest(__VLS_1));
let __VLS_4;
let __VLS_5;
let __VLS_6;
const __VLS_7 = {
    onClick: (...[$event]) => {
        __VLS_ctx.openEntityForm();
    }
};
__VLS_3.slots.default;
const __VLS_8 = {}.ElIcon;
/** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
// @ts-ignore
const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({}));
const __VLS_10 = __VLS_9({}, ...__VLS_functionalComponentArgsRest(__VLS_9));
__VLS_11.slots.default;
const __VLS_12 = {}.Plus;
/** @type {[typeof __VLS_components.Plus, ]} */ ;
// @ts-ignore
const __VLS_13 = __VLS_asFunctionalComponent(__VLS_12, new __VLS_12({}));
const __VLS_14 = __VLS_13({}, ...__VLS_functionalComponentArgsRest(__VLS_13));
var __VLS_11;
var __VLS_3;
const __VLS_16 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
    ...{ 'onKeyup': {} },
    modelValue: (__VLS_ctx.queryParams.keyword),
    placeholder: "请输入关键字搜索",
    ...{ class: "search-input" },
    clearable: true,
}));
const __VLS_18 = __VLS_17({
    ...{ 'onKeyup': {} },
    modelValue: (__VLS_ctx.queryParams.keyword),
    placeholder: "请输入关键字搜索",
    ...{ class: "search-input" },
    clearable: true,
}, ...__VLS_functionalComponentArgsRest(__VLS_17));
let __VLS_20;
let __VLS_21;
let __VLS_22;
const __VLS_23 = {
    onKeyup: (__VLS_ctx.handleSearch)
};
__VLS_19.slots.default;
{
    const { suffix: __VLS_thisSlot } = __VLS_19.slots;
    const __VLS_24 = {}.ElIcon;
    /** @type {[typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, typeof __VLS_components.ElIcon, typeof __VLS_components.elIcon, ]} */ ;
    // @ts-ignore
    const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({
        ...{ 'onClick': {} },
        ...{ class: "el-input__icon" },
    }));
    const __VLS_26 = __VLS_25({
        ...{ 'onClick': {} },
        ...{ class: "el-input__icon" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_25));
    let __VLS_28;
    let __VLS_29;
    let __VLS_30;
    const __VLS_31 = {
        onClick: (__VLS_ctx.handleSearch)
    };
    __VLS_27.slots.default;
    const __VLS_32 = {}.Search;
    /** @type {[typeof __VLS_components.Search, ]} */ ;
    // @ts-ignore
    const __VLS_33 = __VLS_asFunctionalComponent(__VLS_32, new __VLS_32({}));
    const __VLS_34 = __VLS_33({}, ...__VLS_functionalComponentArgsRest(__VLS_33));
    var __VLS_27;
}
var __VLS_19;
const __VLS_36 = {}.ElTable;
/** @type {[typeof __VLS_components.ElTable, typeof __VLS_components.elTable, typeof __VLS_components.ElTable, typeof __VLS_components.elTable, ]} */ ;
// @ts-ignore
const __VLS_37 = __VLS_asFunctionalComponent(__VLS_36, new __VLS_36({
    data: (__VLS_ctx.entityList),
    border: true,
    ...{ style: {} },
}));
const __VLS_38 = __VLS_37({
    data: (__VLS_ctx.entityList),
    border: true,
    ...{ style: {} },
}, ...__VLS_functionalComponentArgsRest(__VLS_37));
__VLS_asFunctionalDirective(__VLS_directives.vLoading)(null, { ...__VLS_directiveBindingRestFields, value: (__VLS_ctx.loading) }, null, null);
__VLS_39.slots.default;
const __VLS_40 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({
    prop: "entityCode",
    label: "实体编码",
    minWidth: "120",
}));
const __VLS_42 = __VLS_41({
    prop: "entityCode",
    label: "实体编码",
    minWidth: "120",
}, ...__VLS_functionalComponentArgsRest(__VLS_41));
const __VLS_44 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_45 = __VLS_asFunctionalComponent(__VLS_44, new __VLS_44({
    prop: "entityName",
    label: "实体名称",
    minWidth: "120",
}));
const __VLS_46 = __VLS_45({
    prop: "entityName",
    label: "实体名称",
    minWidth: "120",
}, ...__VLS_functionalComponentArgsRest(__VLS_45));
const __VLS_48 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_49 = __VLS_asFunctionalComponent(__VLS_48, new __VLS_48({
    prop: "tableName",
    label: "表名",
    minWidth: "120",
}));
const __VLS_50 = __VLS_49({
    prop: "tableName",
    label: "表名",
    minWidth: "120",
}, ...__VLS_functionalComponentArgsRest(__VLS_49));
const __VLS_52 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_53 = __VLS_asFunctionalComponent(__VLS_52, new __VLS_52({
    prop: "description",
    label: "描述",
    minWidth: "150",
    showOverflowTooltip: true,
}));
const __VLS_54 = __VLS_53({
    prop: "description",
    label: "描述",
    minWidth: "150",
    showOverflowTooltip: true,
}, ...__VLS_functionalComponentArgsRest(__VLS_53));
const __VLS_56 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_57 = __VLS_asFunctionalComponent(__VLS_56, new __VLS_56({
    label: "状态",
    width: "120",
}));
const __VLS_58 = __VLS_57({
    label: "状态",
    width: "120",
}, ...__VLS_functionalComponentArgsRest(__VLS_57));
__VLS_59.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_59.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_60 = {}.ElTag;
    /** @type {[typeof __VLS_components.ElTag, typeof __VLS_components.elTag, typeof __VLS_components.ElTag, typeof __VLS_components.elTag, ]} */ ;
    // @ts-ignore
    const __VLS_61 = __VLS_asFunctionalComponent(__VLS_60, new __VLS_60({
        type: (scope.row.published ? 'success' : 'info'),
    }));
    const __VLS_62 = __VLS_61({
        type: (scope.row.published ? 'success' : 'info'),
    }, ...__VLS_functionalComponentArgsRest(__VLS_61));
    __VLS_63.slots.default;
    (scope.row.published ? '已发布' : '未发布');
    var __VLS_63;
    const __VLS_64 = {}.ElTag;
    /** @type {[typeof __VLS_components.ElTag, typeof __VLS_components.elTag, typeof __VLS_components.ElTag, typeof __VLS_components.elTag, ]} */ ;
    // @ts-ignore
    const __VLS_65 = __VLS_asFunctionalComponent(__VLS_64, new __VLS_64({
        type: (scope.row.enabled ? 'success' : 'danger'),
        ...{ style: {} },
    }));
    const __VLS_66 = __VLS_65({
        type: (scope.row.enabled ? 'success' : 'danger'),
        ...{ style: {} },
    }, ...__VLS_functionalComponentArgsRest(__VLS_65));
    __VLS_67.slots.default;
    (scope.row.enabled ? '启用' : '停用');
    var __VLS_67;
}
var __VLS_59;
const __VLS_68 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_69 = __VLS_asFunctionalComponent(__VLS_68, new __VLS_68({
    label: "操作",
    width: "220",
    fixed: "right",
}));
const __VLS_70 = __VLS_69({
    label: "操作",
    width: "220",
    fixed: "right",
}, ...__VLS_functionalComponentArgsRest(__VLS_69));
__VLS_71.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_71.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    if (!scope.row.published) {
        const __VLS_72 = {}.ElButton;
        /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
        // @ts-ignore
        const __VLS_73 = __VLS_asFunctionalComponent(__VLS_72, new __VLS_72({
            ...{ 'onClick': {} },
            type: "primary",
            size: "small",
            link: true,
        }));
        const __VLS_74 = __VLS_73({
            ...{ 'onClick': {} },
            type: "primary",
            size: "small",
            link: true,
        }, ...__VLS_functionalComponentArgsRest(__VLS_73));
        let __VLS_76;
        let __VLS_77;
        let __VLS_78;
        const __VLS_79 = {
            onClick: (...[$event]) => {
                if (!(!scope.row.published))
                    return;
                __VLS_ctx.openEntityForm(scope.row);
            }
        };
        __VLS_75.slots.default;
        var __VLS_75;
    }
    if (!scope.row.published) {
        const __VLS_80 = {}.ElButton;
        /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
        // @ts-ignore
        const __VLS_81 = __VLS_asFunctionalComponent(__VLS_80, new __VLS_80({
            ...{ 'onClick': {} },
            type: "success",
            size: "small",
            link: true,
        }));
        const __VLS_82 = __VLS_81({
            ...{ 'onClick': {} },
            type: "success",
            size: "small",
            link: true,
        }, ...__VLS_functionalComponentArgsRest(__VLS_81));
        let __VLS_84;
        let __VLS_85;
        let __VLS_86;
        const __VLS_87 = {
            onClick: (...[$event]) => {
                if (!(!scope.row.published))
                    return;
                __VLS_ctx.handlePublish(scope.row.id);
            }
        };
        __VLS_83.slots.default;
        var __VLS_83;
    }
    else {
        const __VLS_88 = {}.ElButton;
        /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
        // @ts-ignore
        const __VLS_89 = __VLS_asFunctionalComponent(__VLS_88, new __VLS_88({
            ...{ 'onClick': {} },
            type: "warning",
            size: "small",
            link: true,
        }));
        const __VLS_90 = __VLS_89({
            ...{ 'onClick': {} },
            type: "warning",
            size: "small",
            link: true,
        }, ...__VLS_functionalComponentArgsRest(__VLS_89));
        let __VLS_92;
        let __VLS_93;
        let __VLS_94;
        const __VLS_95 = {
            onClick: (...[$event]) => {
                if (!!(!scope.row.published))
                    return;
                __VLS_ctx.handleUnpublish(scope.row.id);
            }
        };
        __VLS_91.slots.default;
        var __VLS_91;
    }
    if (!scope.row.published) {
        const __VLS_96 = {}.ElButton;
        /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
        // @ts-ignore
        const __VLS_97 = __VLS_asFunctionalComponent(__VLS_96, new __VLS_96({
            ...{ 'onClick': {} },
            type: "danger",
            size: "small",
            link: true,
        }));
        const __VLS_98 = __VLS_97({
            ...{ 'onClick': {} },
            type: "danger",
            size: "small",
            link: true,
        }, ...__VLS_functionalComponentArgsRest(__VLS_97));
        let __VLS_100;
        let __VLS_101;
        let __VLS_102;
        const __VLS_103 = {
            onClick: (...[$event]) => {
                if (!(!scope.row.published))
                    return;
                __VLS_ctx.handleDelete(scope.row.id);
            }
        };
        __VLS_99.slots.default;
        var __VLS_99;
    }
}
var __VLS_71;
var __VLS_39;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "pagination" },
});
const __VLS_104 = {}.ElPagination;
/** @type {[typeof __VLS_components.ElPagination, typeof __VLS_components.elPagination, ]} */ ;
// @ts-ignore
const __VLS_105 = __VLS_asFunctionalComponent(__VLS_104, new __VLS_104({
    ...{ 'onSizeChange': {} },
    ...{ 'onCurrentChange': {} },
    currentPage: (__VLS_ctx.queryParams.page),
    pageSize: (__VLS_ctx.queryParams.size),
    pageSizes: ([10, 20, 50, 100]),
    layout: "total, sizes, prev, pager, next, jumper",
    total: (__VLS_ctx.total),
}));
const __VLS_106 = __VLS_105({
    ...{ 'onSizeChange': {} },
    ...{ 'onCurrentChange': {} },
    currentPage: (__VLS_ctx.queryParams.page),
    pageSize: (__VLS_ctx.queryParams.size),
    pageSizes: ([10, 20, 50, 100]),
    layout: "total, sizes, prev, pager, next, jumper",
    total: (__VLS_ctx.total),
}, ...__VLS_functionalComponentArgsRest(__VLS_105));
let __VLS_108;
let __VLS_109;
let __VLS_110;
const __VLS_111 = {
    onSizeChange: (__VLS_ctx.handleSizeChange)
};
const __VLS_112 = {
    onCurrentChange: (__VLS_ctx.handleCurrentChange)
};
var __VLS_107;
const __VLS_113 = {}.ElDialog;
/** @type {[typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, ]} */ ;
// @ts-ignore
const __VLS_114 = __VLS_asFunctionalComponent(__VLS_113, new __VLS_113({
    modelValue: (__VLS_ctx.entityFormVisible),
    title: (__VLS_ctx.formType === 'add' ? '新建实体' : '编辑实体'),
    width: "60%",
    destroyOnClose: true,
}));
const __VLS_115 = __VLS_114({
    modelValue: (__VLS_ctx.entityFormVisible),
    title: (__VLS_ctx.formType === 'add' ? '新建实体' : '编辑实体'),
    width: "60%",
    destroyOnClose: true,
}, ...__VLS_functionalComponentArgsRest(__VLS_114));
__VLS_116.slots.default;
/** @type {[typeof EntityForm, ]} */ ;
// @ts-ignore
const __VLS_117 = __VLS_asFunctionalComponent(EntityForm, new EntityForm({
    ...{ 'onSuccess': {} },
    ...{ 'onCancel': {} },
    entityData: (__VLS_ctx.entityFormData),
    formType: (__VLS_ctx.formType),
}));
const __VLS_118 = __VLS_117({
    ...{ 'onSuccess': {} },
    ...{ 'onCancel': {} },
    entityData: (__VLS_ctx.entityFormData),
    formType: (__VLS_ctx.formType),
}, ...__VLS_functionalComponentArgsRest(__VLS_117));
let __VLS_120;
let __VLS_121;
let __VLS_122;
const __VLS_123 = {
    onSuccess: (__VLS_ctx.handleFormSuccess)
};
const __VLS_124 = {
    onCancel: (...[$event]) => {
        __VLS_ctx.entityFormVisible = false;
    }
};
var __VLS_119;
var __VLS_116;
/** @type {__VLS_StyleScopedClasses['entity-manage']} */ ;
/** @type {__VLS_StyleScopedClasses['toolbar']} */ ;
/** @type {__VLS_StyleScopedClasses['search-input']} */ ;
/** @type {__VLS_StyleScopedClasses['el-input__icon']} */ ;
/** @type {__VLS_StyleScopedClasses['pagination']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            EntityForm: EntityForm,
            queryParams: queryParams,
            entityList: entityList,
            total: total,
            loading: loading,
            entityFormVisible: entityFormVisible,
            entityFormData: entityFormData,
            formType: formType,
            handleSearch: handleSearch,
            handleSizeChange: handleSizeChange,
            handleCurrentChange: handleCurrentChange,
            openEntityForm: openEntityForm,
            handleFormSuccess: handleFormSuccess,
            handleDelete: handleDelete,
            handlePublish: handlePublish,
            handleUnpublish: handleUnpublish,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
