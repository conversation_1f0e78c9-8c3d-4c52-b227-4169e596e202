import request from '../utils/request'; // 导入axios实例

/**
 * 获取验证码图片
 * @param captchaId 验证码ID
 * @returns Promise<Blob> 验证码图片二进制数据
 */
export function getCaptcha(captchaId: string): Promise<Blob> {
  return request({
    url: `/auth/captcha?id=${captchaId}&t=${new Date().getTime()}`,
    method: 'get',
    responseType: 'blob'
  });
}


/**
 * 用户登录
 * @param data 登录请求体
 * @returns Promise<string> JWT Token
 */
export function login(data: any): Promise<any> {
  return request({
    url: '/auth/login', // 注意这里使用了Vite代理后的路径
    method: 'post',
    data
  });
}

/**
 * 用户退出登录
 * @returns Promise<void>
 */
export function logout(): Promise<any> {
    return request({
      url: '/auth/logout', // 退出登录接口路径
      method: 'post'
    });
  }