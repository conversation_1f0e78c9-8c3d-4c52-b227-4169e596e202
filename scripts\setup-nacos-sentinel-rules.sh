#!/bin/bash

# Nacos Sentinel 规则自动配置脚本
# 作者: AI
# 日期: 2025-06-29

NACOS_SERVER="http://localhost:8848"
NACOS_USERNAME="nacos"
NACOS_PASSWORD="nacos"
GROUP="SENTINEL_GROUP"

echo "开始配置 Nacos Sentinel 规则..."

# 获取访问令牌
echo "正在获取 Nacos 访问令牌..."
TOKEN_RESPONSE=$(curl -s -X POST "${NACOS_SERVER}/nacos/v1/auth/login" \
  -d "username=${NACOS_USERNAME}&password=${NACOS_PASSWORD}")

if [[ $TOKEN_RESPONSE == *"accessToken"* ]]; then
    ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)
    echo "获取访问令牌成功"
else
    echo "获取访问令牌失败，请检查 Nacos 服务是否启动"
    exit 1
fi

# 配置后端服务流控规则
echo "配置后端服务流控规则..."
curl -s -X POST "${NACOS_SERVER}/nacos/v1/cs/configs" \
  -d "dataId=backend-service-flow-rules" \
  -d "group=${GROUP}" \
  -d "content=[
  {
    \"resource\": \"userController\",
    \"limitApp\": \"default\",
    \"grade\": 1,
    \"count\": 10,
    \"strategy\": 0,
    \"controlBehavior\": 0,
    \"clusterMode\": false
  },
  {
    \"resource\": \"memberController\", 
    \"limitApp\": \"default\",
    \"grade\": 1,
    \"count\": 20,
    \"strategy\": 0,
    \"controlBehavior\": 0,
    \"clusterMode\": false
  },
  {
    \"resource\": \"menuController\",
    \"limitApp\": \"default\", 
    \"grade\": 1,
    \"count\": 50,
    \"strategy\": 0,
    \"controlBehavior\": 0,
    \"clusterMode\": false
  }
]" \
  -d "accessToken=${ACCESS_TOKEN}"

# 配置后端服务熔断规则
echo "配置后端服务熔断规则..."
curl -s -X POST "${NACOS_SERVER}/nacos/v1/cs/configs" \
  -d "dataId=backend-service-degrade-rules" \
  -d "group=${GROUP}" \
  -d "content=[
  {
    \"resource\": \"userController\",
    \"grade\": 2,
    \"count\": 0.5,
    \"timeWindow\": 10,
    \"minRequestAmount\": 5,
    \"slowRatioThreshold\": 0.5,
    \"statIntervalMs\": 1000
  },
  {
    \"resource\": \"memberController\",
    \"grade\": 0,
    \"count\": 1000,
    \"timeWindow\": 10,
    \"minRequestAmount\": 5,
    \"slowRatioThreshold\": 0.5,
    \"statIntervalMs\": 1000
  },
  {
    \"resource\": \"menuController\",
    \"grade\": 1,
    \"count\": 5,
    \"timeWindow\": 10,
    \"minRequestAmount\": 3,
    \"slowRatioThreshold\": 0.5,
    \"statIntervalMs\": 1000
  }
]" \
  -d "accessToken=${ACCESS_TOKEN}"

# 配置后端服务系统规则
echo "配置后端服务系统规则..."
curl -s -X POST "${NACOS_SERVER}/nacos/v1/cs/configs" \
  -d "dataId=backend-service-system-rules" \
  -d "group=${GROUP}" \
  -d "content=[
  {
    \"highestSystemLoad\": 10.0,
    \"avgRt\": 2000,
    \"maxThread\": 200,
    \"qps\": 500,
    \"highestCpuUsage\": 0.8
  }
]" \
  -d "accessToken=${ACCESS_TOKEN}"

# 配置网关流控规则
echo "配置网关流控规则..."
curl -s -X POST "${NACOS_SERVER}/nacos/v1/cs/configs" \
  -d "dataId=gateway-service-gw-flow-rules" \
  -d "group=${GROUP}" \
  -d "content=[
  {
    \"resource\": \"backend-service\",
    \"resourceMode\": 0,
    \"grade\": 1,
    \"count\": 100,
    \"intervalSec\": 1,
    \"controlBehavior\": 0,
    \"burst\": 0,
    \"maxQueueingTimeMs\": 500
  },
  {
    \"resource\": \"user_api\",
    \"resourceMode\": 1,
    \"grade\": 1,
    \"count\": 20,
    \"intervalSec\": 1,
    \"controlBehavior\": 0,
    \"burst\": 0,
    \"maxQueueingTimeMs\": 500
  },
  {
    \"resource\": \"member_api\",
    \"resourceMode\": 1,
    \"grade\": 1,
    \"count\": 30,
    \"intervalSec\": 1,
    \"controlBehavior\": 0,
    \"burst\": 0,
    \"maxQueueingTimeMs\": 500
  }
]" \
  -d "accessToken=${ACCESS_TOKEN}"

# 配置网关API分组规则
echo "配置网关API分组规则..."
curl -s -X POST "${NACOS_SERVER}/nacos/v1/cs/configs" \
  -d "dataId=gateway-service-gw-api-group-rules" \
  -d "group=${GROUP}" \
  -d "content=[
  {
    \"apiName\": \"user_api\",
    \"predicateItems\": [
      {
        \"pattern\": \"/user/**\",
        \"matchStrategy\": 1
      }
    ]
  },
  {
    \"apiName\": \"member_api\", 
    \"predicateItems\": [
      {
        \"pattern\": \"/member/**\",
        \"matchStrategy\": 1
      }
    ]
  },
  {
    \"apiName\": \"menu_api\",
    \"predicateItems\": [
      {
        \"pattern\": \"/menu/**\",
        \"matchStrategy\": 1
      }
    ]
  }
]" \
  -d "accessToken=${ACCESS_TOKEN}"

echo "Nacos Sentinel 规则配置完成！"
echo "请访问 Nacos 控制台查看配置：${NACOS_SERVER}/nacos"
