interface PermissionState {
    permissions: string[];
    roles: string[];
}
export declare const usePermissionStore: import("pinia").StoreDefinition<"permission", PermissionState, {
    hasPermission: (state: {
        permissions: string[];
        roles: string[];
    } & import("pinia").PiniaCustomStateProperties<PermissionState>) => (permission: string) => boolean;
    hasRole: (state: {
        permissions: string[];
        roles: string[];
    } & import("pinia").PiniaCustomStateProperties<PermissionState>) => (role: string) => boolean;
}, {
    setPermissions(permissions: string[]): void;
    setRoles(roles: string[]): void;
    clearPermissions(): void;
}>;
export {};
