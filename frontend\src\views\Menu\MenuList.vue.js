/// <reference types="../../../node_modules/.vue-global-types/vue_3.5_0_0_0.d.ts" />
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElTreeSelect, ElMessageBox } from 'element-plus';
import { // Import menu API functions later
getMenuTree, addMenu, updateMenu, deleteMenu } from '../../api/menu';
; // Import Menu interface
const tableData = ref([]);
const menuTreeData = ref([]);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const form = reactive({
    parentId: 0, // Root level parentId
    name: '',
    path: '',
    component: '',
    icon: '',
    type: 'M', // Default to Directory
    permission: '',
    orderNum: 0,
    visible: 1,
    status: 1,
});
const formRef = ref();
const rules = {
    name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择菜单类型', trigger: 'change' }],
    path: [{ required: true, message: '请输入路由路径', trigger: 'blur', trigger: 'change' }],
    permission: [{ required: true, message: '请输入权限标识', trigger: 'blur' }],
    // Add more rules based on type if needed (e.g., component required for type C)
};
// Function to fetch menu data (tree structure)
async function fetchData() {
    try {
        // TODO: Replace with actual getMenuTree API call
        console.log("Fetching menu tree...");
        const res = await getMenuTree(); // Use actual API call
        tableData.value = res.data;
        menuTreeData.value = buildTreeSelectData(res.data);
    }
    catch (error) {
        console.error("获取菜单列表失败:", error);
        tableData.value = [];
        menuTreeData.value = [];
        ElMessage.error('获取菜单列表失败');
    }
}
// Helper function to build data for el-tree-select (flattened with hierarchy indication)
function buildTreeSelectData(menuList) {
    const result = [];
    function flatten(menus, level = 0, prefix = '') {
        menus.forEach(menu => {
            const currentName = prefix + menu.name;
            // Add current menu item
            result.push({ ...menu, name: currentName });
            // Recursively add children
            if (menu.children && menu.children.length > 0) {
                flatten(menu.children, level + 1, prefix + '  '); // Add indentation
            }
        });
    }
    // Assuming top-level menus have parentId 0 or null
    const topLevelMenus = menuList.filter(menu => menu.parentId === 0 || menu.parentId === null);
    flatten(topLevelMenus);
    return result;
}
function openAdd(row) {
    dialogTitle.value = '新增菜单';
    // Reset form and set parentId if adding a child menu
    Object.assign(form, {
        id: undefined,
        parentId: row ? row.id : 0, // Default to root if no row provided
        name: '',
        path: '',
        component: '',
        icon: '',
        type: 'M', // Default type for new menu
        permission: '',
        orderNum: 0,
        visible: 1,
        status: 1,
    });
    dialogVisible.value = true;
}
function openEdit(row) {
    dialogTitle.value = '编辑菜单';
    // Assign row data to form, handle parentId 0/null for root
    Object.assign(form, { ...row, parentId: row.parentId === null ? 0 : row.parentId });
    // Ensure type is correctly assigned if needed, although Object.assign should handle this
    dialogVisible.value = true;
}
async function handleSubmit() {
    formRef.value?.validate(async (valid) => {
        if (!valid)
            return;
        try {
            if (!form.id) {
                // Add menu
                // TODO: Replace with actual addMenu API call
                await addMenu(form); // Use actual API call
                ElMessage.success('新增成功');
            }
            else {
                // Update menu
                // TODO: Replace with actual updateMenu API call
                await updateMenu(form.id, form); // Use actual API call
                ElMessage.success('编辑成功');
            }
            dialogVisible.value = false;
            fetchData(); // Refresh list
        }
        catch (error) {
            console.error("提交菜单信息失败:", error);
            ElMessage.error('操作失败');
        }
    });
}
async function handleDelete(id) {
    if (!id)
        return;
    ElMessageBox.confirm('确定要删除该菜单吗？如果存在子菜单，将无法删除', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
        try {
            const res = await deleteMenu(id);
            if (res && res.code === 0) {
                ElMessage.success('删除成功');
                fetchData(); // Refresh list
            }
            else {
                ElMessage.error(res?.msg || '删除失败，可能存在子菜单');
            }
        }
        catch (error) {
            console.error("删除菜单失败:", error);
            ElMessage.error('删除失败');
        }
    })
        .catch(() => {
        // 用户取消删除操作
        ElMessage.info('已取消删除');
    });
}
onMounted(fetchData);
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "page-container" },
});
const __VLS_0 = {}.ElCard;
/** @type {[typeof __VLS_components.ElCard, typeof __VLS_components.elCard, typeof __VLS_components.ElCard, typeof __VLS_components.elCard, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({}));
const __VLS_2 = __VLS_1({}, ...__VLS_functionalComponentArgsRest(__VLS_1));
__VLS_3.slots.default;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ style: {} },
});
const __VLS_4 = {}.ElButton;
/** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
// @ts-ignore
const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
    ...{ 'onClick': {} },
    type: "success",
}));
const __VLS_6 = __VLS_5({
    ...{ 'onClick': {} },
    type: "success",
}, ...__VLS_functionalComponentArgsRest(__VLS_5));
let __VLS_8;
let __VLS_9;
let __VLS_10;
const __VLS_11 = {
    onClick: (__VLS_ctx.openAdd)
};
__VLS_7.slots.default;
var __VLS_7;
const __VLS_12 = {}.ElTable;
/** @type {[typeof __VLS_components.ElTable, typeof __VLS_components.elTable, typeof __VLS_components.ElTable, typeof __VLS_components.elTable, ]} */ ;
// @ts-ignore
const __VLS_13 = __VLS_asFunctionalComponent(__VLS_12, new __VLS_12({
    data: (__VLS_ctx.tableData),
    ...{ style: {} },
    rowKey: "id",
    defaultExpandAll: true,
    treeProps: ({ children: 'children', hasChildren: 'hasChildren' }),
}));
const __VLS_14 = __VLS_13({
    data: (__VLS_ctx.tableData),
    ...{ style: {} },
    rowKey: "id",
    defaultExpandAll: true,
    treeProps: ({ children: 'children', hasChildren: 'hasChildren' }),
}, ...__VLS_functionalComponentArgsRest(__VLS_13));
__VLS_15.slots.default;
const __VLS_16 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
    prop: "name",
    label: "菜单名称",
    width: "180",
}));
const __VLS_18 = __VLS_17({
    prop: "name",
    label: "菜单名称",
    width: "180",
}, ...__VLS_functionalComponentArgsRest(__VLS_17));
const __VLS_20 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({
    prop: "type",
    label: "类型",
    width: "80",
}));
const __VLS_22 = __VLS_21({
    prop: "type",
    label: "类型",
    width: "80",
}, ...__VLS_functionalComponentArgsRest(__VLS_21));
__VLS_23.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_23.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    if (scope.row.type === 'M') {
        const __VLS_24 = {}.ElTag;
        /** @type {[typeof __VLS_components.ElTag, typeof __VLS_components.elTag, typeof __VLS_components.ElTag, typeof __VLS_components.elTag, ]} */ ;
        // @ts-ignore
        const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({}));
        const __VLS_26 = __VLS_25({}, ...__VLS_functionalComponentArgsRest(__VLS_25));
        __VLS_27.slots.default;
        var __VLS_27;
    }
    else if (scope.row.type === 'C') {
        const __VLS_28 = {}.ElTag;
        /** @type {[typeof __VLS_components.ElTag, typeof __VLS_components.elTag, typeof __VLS_components.ElTag, typeof __VLS_components.elTag, ]} */ ;
        // @ts-ignore
        const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
            type: "success",
        }));
        const __VLS_30 = __VLS_29({
            type: "success",
        }, ...__VLS_functionalComponentArgsRest(__VLS_29));
        __VLS_31.slots.default;
        var __VLS_31;
    }
    else if (scope.row.type === 'F') {
        const __VLS_32 = {}.ElTag;
        /** @type {[typeof __VLS_components.ElTag, typeof __VLS_components.elTag, typeof __VLS_components.ElTag, typeof __VLS_components.elTag, ]} */ ;
        // @ts-ignore
        const __VLS_33 = __VLS_asFunctionalComponent(__VLS_32, new __VLS_32({
            type: "info",
        }));
        const __VLS_34 = __VLS_33({
            type: "info",
        }, ...__VLS_functionalComponentArgsRest(__VLS_33));
        __VLS_35.slots.default;
        var __VLS_35;
    }
}
var __VLS_23;
const __VLS_36 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_37 = __VLS_asFunctionalComponent(__VLS_36, new __VLS_36({
    prop: "icon",
    label: "图标",
    width: "80",
}));
const __VLS_38 = __VLS_37({
    prop: "icon",
    label: "图标",
    width: "80",
}, ...__VLS_functionalComponentArgsRest(__VLS_37));
const __VLS_40 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({
    prop: "permission",
    label: "权限标识",
}));
const __VLS_42 = __VLS_41({
    prop: "permission",
    label: "权限标识",
}, ...__VLS_functionalComponentArgsRest(__VLS_41));
const __VLS_44 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_45 = __VLS_asFunctionalComponent(__VLS_44, new __VLS_44({
    prop: "path",
    label: "路由路径",
}));
const __VLS_46 = __VLS_45({
    prop: "path",
    label: "路由路径",
}, ...__VLS_functionalComponentArgsRest(__VLS_45));
const __VLS_48 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_49 = __VLS_asFunctionalComponent(__VLS_48, new __VLS_48({
    prop: "component",
    label: "组件路径",
}));
const __VLS_50 = __VLS_49({
    prop: "component",
    label: "组件路径",
}, ...__VLS_functionalComponentArgsRest(__VLS_49));
const __VLS_52 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_53 = __VLS_asFunctionalComponent(__VLS_52, new __VLS_52({
    prop: "orderNum",
    label: "排序",
    width: "80",
}));
const __VLS_54 = __VLS_53({
    prop: "orderNum",
    label: "排序",
    width: "80",
}, ...__VLS_functionalComponentArgsRest(__VLS_53));
const __VLS_56 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_57 = __VLS_asFunctionalComponent(__VLS_56, new __VLS_56({
    prop: "visible",
    label: "显示",
}));
const __VLS_58 = __VLS_57({
    prop: "visible",
    label: "显示",
}, ...__VLS_functionalComponentArgsRest(__VLS_57));
__VLS_59.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_59.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    (scope.row.visible === 1 ? '显示' : '隐藏');
}
var __VLS_59;
const __VLS_60 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_61 = __VLS_asFunctionalComponent(__VLS_60, new __VLS_60({
    prop: "status",
    label: "状态",
}));
const __VLS_62 = __VLS_61({
    prop: "status",
    label: "状态",
}, ...__VLS_functionalComponentArgsRest(__VLS_61));
__VLS_63.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_63.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    const __VLS_64 = {}.ElTag;
    /** @type {[typeof __VLS_components.ElTag, typeof __VLS_components.elTag, typeof __VLS_components.ElTag, typeof __VLS_components.elTag, ]} */ ;
    // @ts-ignore
    const __VLS_65 = __VLS_asFunctionalComponent(__VLS_64, new __VLS_64({
        type: (scope.row.status === 1 ? 'success' : 'info'),
    }));
    const __VLS_66 = __VLS_65({
        type: (scope.row.status === 1 ? 'success' : 'info'),
    }, ...__VLS_functionalComponentArgsRest(__VLS_65));
    __VLS_67.slots.default;
    (scope.row.status === 1 ? '正常' : '停用');
    var __VLS_67;
}
var __VLS_63;
const __VLS_68 = {}.ElTableColumn;
/** @type {[typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, typeof __VLS_components.ElTableColumn, typeof __VLS_components.elTableColumn, ]} */ ;
// @ts-ignore
const __VLS_69 = __VLS_asFunctionalComponent(__VLS_68, new __VLS_68({
    label: "操作",
    width: "200",
}));
const __VLS_70 = __VLS_69({
    label: "操作",
    width: "200",
}, ...__VLS_functionalComponentArgsRest(__VLS_69));
__VLS_71.slots.default;
{
    const { default: __VLS_thisSlot } = __VLS_71.slots;
    const [scope] = __VLS_getSlotParams(__VLS_thisSlot);
    if (scope.row.type === 'M' || scope.row.type === 'C') {
        const __VLS_72 = {}.ElButton;
        /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
        // @ts-ignore
        const __VLS_73 = __VLS_asFunctionalComponent(__VLS_72, new __VLS_72({
            ...{ 'onClick': {} },
            size: "small",
        }));
        const __VLS_74 = __VLS_73({
            ...{ 'onClick': {} },
            size: "small",
        }, ...__VLS_functionalComponentArgsRest(__VLS_73));
        let __VLS_76;
        let __VLS_77;
        let __VLS_78;
        const __VLS_79 = {
            onClick: (...[$event]) => {
                if (!(scope.row.type === 'M' || scope.row.type === 'C'))
                    return;
                __VLS_ctx.openAdd(scope.row);
            }
        };
        __VLS_75.slots.default;
        var __VLS_75;
    }
    const __VLS_80 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_81 = __VLS_asFunctionalComponent(__VLS_80, new __VLS_80({
        ...{ 'onClick': {} },
        size: "small",
    }));
    const __VLS_82 = __VLS_81({
        ...{ 'onClick': {} },
        size: "small",
    }, ...__VLS_functionalComponentArgsRest(__VLS_81));
    let __VLS_84;
    let __VLS_85;
    let __VLS_86;
    const __VLS_87 = {
        onClick: (...[$event]) => {
            __VLS_ctx.openEdit(scope.row);
        }
    };
    __VLS_83.slots.default;
    var __VLS_83;
    const __VLS_88 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_89 = __VLS_asFunctionalComponent(__VLS_88, new __VLS_88({
        ...{ 'onClick': {} },
        size: "small",
        type: "danger",
    }));
    const __VLS_90 = __VLS_89({
        ...{ 'onClick': {} },
        size: "small",
        type: "danger",
    }, ...__VLS_functionalComponentArgsRest(__VLS_89));
    let __VLS_92;
    let __VLS_93;
    let __VLS_94;
    const __VLS_95 = {
        onClick: (...[$event]) => {
            __VLS_ctx.handleDelete(scope.row.id);
        }
    };
    __VLS_91.slots.default;
    var __VLS_91;
}
var __VLS_71;
var __VLS_15;
var __VLS_3;
const __VLS_96 = {}.ElDialog;
/** @type {[typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, typeof __VLS_components.ElDialog, typeof __VLS_components.elDialog, ]} */ ;
// @ts-ignore
const __VLS_97 = __VLS_asFunctionalComponent(__VLS_96, new __VLS_96({
    modelValue: (__VLS_ctx.dialogVisible),
    title: (__VLS_ctx.dialogTitle),
    width: "500px",
}));
const __VLS_98 = __VLS_97({
    modelValue: (__VLS_ctx.dialogVisible),
    title: (__VLS_ctx.dialogTitle),
    width: "500px",
}, ...__VLS_functionalComponentArgsRest(__VLS_97));
__VLS_99.slots.default;
const __VLS_100 = {}.ElForm;
/** @type {[typeof __VLS_components.ElForm, typeof __VLS_components.elForm, typeof __VLS_components.ElForm, typeof __VLS_components.elForm, ]} */ ;
// @ts-ignore
const __VLS_101 = __VLS_asFunctionalComponent(__VLS_100, new __VLS_100({
    model: (__VLS_ctx.form),
    rules: (__VLS_ctx.rules),
    ref: "formRef",
    labelWidth: "100px",
}));
const __VLS_102 = __VLS_101({
    model: (__VLS_ctx.form),
    rules: (__VLS_ctx.rules),
    ref: "formRef",
    labelWidth: "100px",
}, ...__VLS_functionalComponentArgsRest(__VLS_101));
/** @type {typeof __VLS_ctx.formRef} */ ;
var __VLS_104 = {};
__VLS_103.slots.default;
const __VLS_106 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_107 = __VLS_asFunctionalComponent(__VLS_106, new __VLS_106({
    label: "上级菜单",
    prop: "parentId",
}));
const __VLS_108 = __VLS_107({
    label: "上级菜单",
    prop: "parentId",
}, ...__VLS_functionalComponentArgsRest(__VLS_107));
__VLS_109.slots.default;
const __VLS_110 = {}.ElTreeSelect;
/** @type {[typeof __VLS_components.ElTreeSelect, typeof __VLS_components.elTreeSelect, ]} */ ;
// @ts-ignore
const __VLS_111 = __VLS_asFunctionalComponent(__VLS_110, new __VLS_110({
    modelValue: (__VLS_ctx.form.parentId),
    data: (__VLS_ctx.menuTreeData),
    props: ({ value: 'id', label: 'name' }),
    nodeKey: "id",
    checkStrictly: true,
    clearable: true,
    placeholder: "选择上级菜单",
}));
const __VLS_112 = __VLS_111({
    modelValue: (__VLS_ctx.form.parentId),
    data: (__VLS_ctx.menuTreeData),
    props: ({ value: 'id', label: 'name' }),
    nodeKey: "id",
    checkStrictly: true,
    clearable: true,
    placeholder: "选择上级菜单",
}, ...__VLS_functionalComponentArgsRest(__VLS_111));
var __VLS_109;
const __VLS_114 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_115 = __VLS_asFunctionalComponent(__VLS_114, new __VLS_114({
    label: "菜单类型",
    prop: "type",
}));
const __VLS_116 = __VLS_115({
    label: "菜单类型",
    prop: "type",
}, ...__VLS_functionalComponentArgsRest(__VLS_115));
__VLS_117.slots.default;
const __VLS_118 = {}.ElRadioGroup;
/** @type {[typeof __VLS_components.ElRadioGroup, typeof __VLS_components.elRadioGroup, typeof __VLS_components.ElRadioGroup, typeof __VLS_components.elRadioGroup, ]} */ ;
// @ts-ignore
const __VLS_119 = __VLS_asFunctionalComponent(__VLS_118, new __VLS_118({
    modelValue: (__VLS_ctx.form.type),
}));
const __VLS_120 = __VLS_119({
    modelValue: (__VLS_ctx.form.type),
}, ...__VLS_functionalComponentArgsRest(__VLS_119));
__VLS_121.slots.default;
const __VLS_122 = {}.ElRadio;
/** @type {[typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, ]} */ ;
// @ts-ignore
const __VLS_123 = __VLS_asFunctionalComponent(__VLS_122, new __VLS_122({
    label: "M",
}));
const __VLS_124 = __VLS_123({
    label: "M",
}, ...__VLS_functionalComponentArgsRest(__VLS_123));
__VLS_125.slots.default;
var __VLS_125;
const __VLS_126 = {}.ElRadio;
/** @type {[typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, ]} */ ;
// @ts-ignore
const __VLS_127 = __VLS_asFunctionalComponent(__VLS_126, new __VLS_126({
    label: "C",
}));
const __VLS_128 = __VLS_127({
    label: "C",
}, ...__VLS_functionalComponentArgsRest(__VLS_127));
__VLS_129.slots.default;
var __VLS_129;
const __VLS_130 = {}.ElRadio;
/** @type {[typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, ]} */ ;
// @ts-ignore
const __VLS_131 = __VLS_asFunctionalComponent(__VLS_130, new __VLS_130({
    label: "F",
}));
const __VLS_132 = __VLS_131({
    label: "F",
}, ...__VLS_functionalComponentArgsRest(__VLS_131));
__VLS_133.slots.default;
var __VLS_133;
var __VLS_121;
var __VLS_117;
const __VLS_134 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_135 = __VLS_asFunctionalComponent(__VLS_134, new __VLS_134({
    label: "菜单名称",
    prop: "name",
}));
const __VLS_136 = __VLS_135({
    label: "菜单名称",
    prop: "name",
}, ...__VLS_functionalComponentArgsRest(__VLS_135));
__VLS_137.slots.default;
const __VLS_138 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_139 = __VLS_asFunctionalComponent(__VLS_138, new __VLS_138({
    modelValue: (__VLS_ctx.form.name),
    autocomplete: "off",
}));
const __VLS_140 = __VLS_139({
    modelValue: (__VLS_ctx.form.name),
    autocomplete: "off",
}, ...__VLS_functionalComponentArgsRest(__VLS_139));
var __VLS_137;
if (__VLS_ctx.form.type !== 'F') {
    const __VLS_142 = {}.ElFormItem;
    /** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
    // @ts-ignore
    const __VLS_143 = __VLS_asFunctionalComponent(__VLS_142, new __VLS_142({
        label: "路由路径",
        prop: "path",
    }));
    const __VLS_144 = __VLS_143({
        label: "路由路径",
        prop: "path",
    }, ...__VLS_functionalComponentArgsRest(__VLS_143));
    __VLS_145.slots.default;
    const __VLS_146 = {}.ElInput;
    /** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
    // @ts-ignore
    const __VLS_147 = __VLS_asFunctionalComponent(__VLS_146, new __VLS_146({
        modelValue: (__VLS_ctx.form.path),
    }));
    const __VLS_148 = __VLS_147({
        modelValue: (__VLS_ctx.form.path),
    }, ...__VLS_functionalComponentArgsRest(__VLS_147));
    var __VLS_145;
}
if (__VLS_ctx.form.type === 'C') {
    const __VLS_150 = {}.ElFormItem;
    /** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
    // @ts-ignore
    const __VLS_151 = __VLS_asFunctionalComponent(__VLS_150, new __VLS_150({
        label: "组件路径",
        prop: "component",
    }));
    const __VLS_152 = __VLS_151({
        label: "组件路径",
        prop: "component",
    }, ...__VLS_functionalComponentArgsRest(__VLS_151));
    __VLS_153.slots.default;
    const __VLS_154 = {}.ElInput;
    /** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
    // @ts-ignore
    const __VLS_155 = __VLS_asFunctionalComponent(__VLS_154, new __VLS_154({
        modelValue: (__VLS_ctx.form.component),
    }));
    const __VLS_156 = __VLS_155({
        modelValue: (__VLS_ctx.form.component),
    }, ...__VLS_functionalComponentArgsRest(__VLS_155));
    var __VLS_153;
}
const __VLS_158 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_159 = __VLS_asFunctionalComponent(__VLS_158, new __VLS_158({
    label: "权限标识",
    prop: "permission",
}));
const __VLS_160 = __VLS_159({
    label: "权限标识",
    prop: "permission",
}, ...__VLS_functionalComponentArgsRest(__VLS_159));
__VLS_161.slots.default;
const __VLS_162 = {}.ElInput;
/** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
// @ts-ignore
const __VLS_163 = __VLS_asFunctionalComponent(__VLS_162, new __VLS_162({
    modelValue: (__VLS_ctx.form.permission),
}));
const __VLS_164 = __VLS_163({
    modelValue: (__VLS_ctx.form.permission),
}, ...__VLS_functionalComponentArgsRest(__VLS_163));
var __VLS_161;
if (__VLS_ctx.form.type !== 'F') {
    const __VLS_166 = {}.ElFormItem;
    /** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
    // @ts-ignore
    const __VLS_167 = __VLS_asFunctionalComponent(__VLS_166, new __VLS_166({
        label: "图标",
        prop: "icon",
    }));
    const __VLS_168 = __VLS_167({
        label: "图标",
        prop: "icon",
    }, ...__VLS_functionalComponentArgsRest(__VLS_167));
    __VLS_169.slots.default;
    const __VLS_170 = {}.ElInput;
    /** @type {[typeof __VLS_components.ElInput, typeof __VLS_components.elInput, ]} */ ;
    // @ts-ignore
    const __VLS_171 = __VLS_asFunctionalComponent(__VLS_170, new __VLS_170({
        modelValue: (__VLS_ctx.form.icon),
    }));
    const __VLS_172 = __VLS_171({
        modelValue: (__VLS_ctx.form.icon),
    }, ...__VLS_functionalComponentArgsRest(__VLS_171));
    var __VLS_169;
}
const __VLS_174 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_175 = __VLS_asFunctionalComponent(__VLS_174, new __VLS_174({
    label: "排序",
    prop: "orderNum",
}));
const __VLS_176 = __VLS_175({
    label: "排序",
    prop: "orderNum",
}, ...__VLS_functionalComponentArgsRest(__VLS_175));
__VLS_177.slots.default;
const __VLS_178 = {}.ElInputNumber;
/** @type {[typeof __VLS_components.ElInputNumber, typeof __VLS_components.elInputNumber, ]} */ ;
// @ts-ignore
const __VLS_179 = __VLS_asFunctionalComponent(__VLS_178, new __VLS_178({
    modelValue: (__VLS_ctx.form.orderNum),
    min: (0),
}));
const __VLS_180 = __VLS_179({
    modelValue: (__VLS_ctx.form.orderNum),
    min: (0),
}, ...__VLS_functionalComponentArgsRest(__VLS_179));
var __VLS_177;
if (__VLS_ctx.form.type !== 'F') {
    const __VLS_182 = {}.ElFormItem;
    /** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
    // @ts-ignore
    const __VLS_183 = __VLS_asFunctionalComponent(__VLS_182, new __VLS_182({
        label: "是否显示",
        prop: "visible",
    }));
    const __VLS_184 = __VLS_183({
        label: "是否显示",
        prop: "visible",
    }, ...__VLS_functionalComponentArgsRest(__VLS_183));
    __VLS_185.slots.default;
    const __VLS_186 = {}.ElRadioGroup;
    /** @type {[typeof __VLS_components.ElRadioGroup, typeof __VLS_components.elRadioGroup, typeof __VLS_components.ElRadioGroup, typeof __VLS_components.elRadioGroup, ]} */ ;
    // @ts-ignore
    const __VLS_187 = __VLS_asFunctionalComponent(__VLS_186, new __VLS_186({
        modelValue: (__VLS_ctx.form.visible),
    }));
    const __VLS_188 = __VLS_187({
        modelValue: (__VLS_ctx.form.visible),
    }, ...__VLS_functionalComponentArgsRest(__VLS_187));
    __VLS_189.slots.default;
    const __VLS_190 = {}.ElRadio;
    /** @type {[typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, ]} */ ;
    // @ts-ignore
    const __VLS_191 = __VLS_asFunctionalComponent(__VLS_190, new __VLS_190({
        label: (1),
    }));
    const __VLS_192 = __VLS_191({
        label: (1),
    }, ...__VLS_functionalComponentArgsRest(__VLS_191));
    __VLS_193.slots.default;
    var __VLS_193;
    const __VLS_194 = {}.ElRadio;
    /** @type {[typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, ]} */ ;
    // @ts-ignore
    const __VLS_195 = __VLS_asFunctionalComponent(__VLS_194, new __VLS_194({
        label: (0),
    }));
    const __VLS_196 = __VLS_195({
        label: (0),
    }, ...__VLS_functionalComponentArgsRest(__VLS_195));
    __VLS_197.slots.default;
    var __VLS_197;
    var __VLS_189;
    var __VLS_185;
}
const __VLS_198 = {}.ElFormItem;
/** @type {[typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, typeof __VLS_components.ElFormItem, typeof __VLS_components.elFormItem, ]} */ ;
// @ts-ignore
const __VLS_199 = __VLS_asFunctionalComponent(__VLS_198, new __VLS_198({
    label: "菜单状态",
    prop: "status",
}));
const __VLS_200 = __VLS_199({
    label: "菜单状态",
    prop: "status",
}, ...__VLS_functionalComponentArgsRest(__VLS_199));
__VLS_201.slots.default;
const __VLS_202 = {}.ElRadioGroup;
/** @type {[typeof __VLS_components.ElRadioGroup, typeof __VLS_components.elRadioGroup, typeof __VLS_components.ElRadioGroup, typeof __VLS_components.elRadioGroup, ]} */ ;
// @ts-ignore
const __VLS_203 = __VLS_asFunctionalComponent(__VLS_202, new __VLS_202({
    modelValue: (__VLS_ctx.form.status),
}));
const __VLS_204 = __VLS_203({
    modelValue: (__VLS_ctx.form.status),
}, ...__VLS_functionalComponentArgsRest(__VLS_203));
__VLS_205.slots.default;
const __VLS_206 = {}.ElRadio;
/** @type {[typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, ]} */ ;
// @ts-ignore
const __VLS_207 = __VLS_asFunctionalComponent(__VLS_206, new __VLS_206({
    label: (1),
}));
const __VLS_208 = __VLS_207({
    label: (1),
}, ...__VLS_functionalComponentArgsRest(__VLS_207));
__VLS_209.slots.default;
var __VLS_209;
const __VLS_210 = {}.ElRadio;
/** @type {[typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, typeof __VLS_components.ElRadio, typeof __VLS_components.elRadio, ]} */ ;
// @ts-ignore
const __VLS_211 = __VLS_asFunctionalComponent(__VLS_210, new __VLS_210({
    label: (0),
}));
const __VLS_212 = __VLS_211({
    label: (0),
}, ...__VLS_functionalComponentArgsRest(__VLS_211));
__VLS_213.slots.default;
var __VLS_213;
var __VLS_205;
var __VLS_201;
var __VLS_103;
{
    const { footer: __VLS_thisSlot } = __VLS_99.slots;
    const __VLS_214 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_215 = __VLS_asFunctionalComponent(__VLS_214, new __VLS_214({
        ...{ 'onClick': {} },
    }));
    const __VLS_216 = __VLS_215({
        ...{ 'onClick': {} },
    }, ...__VLS_functionalComponentArgsRest(__VLS_215));
    let __VLS_218;
    let __VLS_219;
    let __VLS_220;
    const __VLS_221 = {
        onClick: (...[$event]) => {
            __VLS_ctx.dialogVisible = false;
        }
    };
    __VLS_217.slots.default;
    var __VLS_217;
    const __VLS_222 = {}.ElButton;
    /** @type {[typeof __VLS_components.ElButton, typeof __VLS_components.elButton, typeof __VLS_components.ElButton, typeof __VLS_components.elButton, ]} */ ;
    // @ts-ignore
    const __VLS_223 = __VLS_asFunctionalComponent(__VLS_222, new __VLS_222({
        ...{ 'onClick': {} },
        type: "primary",
    }));
    const __VLS_224 = __VLS_223({
        ...{ 'onClick': {} },
        type: "primary",
    }, ...__VLS_functionalComponentArgsRest(__VLS_223));
    let __VLS_226;
    let __VLS_227;
    let __VLS_228;
    const __VLS_229 = {
        onClick: (__VLS_ctx.handleSubmit)
    };
    __VLS_225.slots.default;
    var __VLS_225;
}
var __VLS_99;
/** @type {__VLS_StyleScopedClasses['page-container']} */ ;
// @ts-ignore
var __VLS_105 = __VLS_104;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            ElTreeSelect: ElTreeSelect,
            tableData: tableData,
            menuTreeData: menuTreeData,
            dialogVisible: dialogVisible,
            dialogTitle: dialogTitle,
            form: form,
            formRef: formRef,
            rules: rules,
            openAdd: openAdd,
            openEdit: openEdit,
            handleSubmit: handleSubmit,
            handleDelete: handleDelete,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
