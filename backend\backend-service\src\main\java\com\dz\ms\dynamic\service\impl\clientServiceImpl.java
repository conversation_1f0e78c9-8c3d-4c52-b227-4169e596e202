package com.dz.ms.dynamic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.ms.dynamic.entity.client;
import com.dz.ms.dynamic.mapper.clientMapper;
import com.dz.ms.dynamic.service.clientService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * client Service实现类
 * 由低代码平台自动生成
 */
@Service
public class clientServiceImpl extends ServiceImpl<clientMapper, client> implements clientService {

    @Override
    public IPage<client> page(Integer page, Integer size, String keyword) {
        Page<client> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<client> wrapper = new LambdaQueryWrapper<>();
        
        // TODO: 根据实际业务修改查询条件
        
        // 按更新时间倒序排序
        wrapper.orderByDesc(client::getUpdateTime);
        
        return page(pageParam, wrapper);
    }
}