import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 要修复的文件列表
const filesToFix = [
  'src/views/Welcome/Welcome.vue',
  // 可以添加其他可能存在相同问题的文件
];

// 不存在的图标及其替代品映射
const iconReplaceMap = {
  'Role': 'UserFilled',  // 用UserFilled替换Role
  'SetUp': 'Setting',    // 用Setting替换SetUp (如果需要)
};

// 对每个文件进行修复
filesToFix.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  
  // 检查文件是否存在
  if (!fs.existsSync(fullPath)) {
    console.log(`文件不存在: ${filePath}`);
    return;
  }
  
  // 读取文件内容
  let content = fs.readFileSync(fullPath, 'utf8');
  
  // 查找并替换图标导入
  let modified = false;
  
  // 替换导入语句中的图标
  for (const [oldIcon, newIcon] of Object.entries(iconReplaceMap)) {
    const importRegex = new RegExp(`(import\\s*{[^}]*)\\b${oldIcon}\\b([^}]*)}(\\s*)from(\\s*)'@element-plus/icons-vue'`, 'g');
    if (importRegex.test(content)) {
      content = content.replace(importRegex, `$1${newIcon}$2}$3from$4'@element-plus/icons-vue'`);
      modified = true;
    }
    
    // 替换使用图标的地方
    const usageRegex = new RegExp(`\\b${oldIcon}\\b`, 'g');
    if (usageRegex.test(content)) {
      content = content.replace(usageRegex, newIcon);
      modified = true;
    }
  }
  
  // 如果文件被修改，则写回文件
  if (modified) {
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`已修复图标问题: ${filePath}`);
  } else {
    console.log(`文件无需修复: ${filePath}`);
  }
});

console.log('图标修复完成!'); 