package com.dz.ms.controller;

import com.dz.ms.entity.UserRole;
import com.dz.ms.service.UserRoleService;
import com.dz.ms.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色关联管理
 * <AUTHOR>
 * @date 2025-05-29
 * @version 1.0.0
 */
@RestController
@RequestMapping("/user-role")
@Api(tags = "用户角色关联管理")
public class UserRoleController {

    @Autowired
    private UserRoleService userRoleService;

    @ApiOperation("根据用户ID获取角色ID列表")
    @GetMapping("/roleIds/{userId}")
    public Result<List<Long>> getRoleIdsByUserId(@PathVariable Long userId) {
        List<UserRole> userRoles = userRoleService.getUserRoleByUserId(userId);
        List<Long> roleIds = userRoles.stream()
                .map(UserRole::getRoleId)
                .collect(Collectors.toList());
        return Result.success(roleIds);
    }

    @ApiOperation("保存用户角色关联")
    @PostMapping("/save/{userId}")
    public Result<Boolean> saveUserRoles(@PathVariable Long userId, @RequestBody List<Long> roleIds) {
        boolean success = userRoleService.saveUserRoles(userId, roleIds);
        return Result.success(success);
    }
} 