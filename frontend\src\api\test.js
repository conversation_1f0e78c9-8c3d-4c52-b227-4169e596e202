import request from '../utils/request';
// 获取分页列表
export function gettestList(params) {
    if (!params.pageNum)
        params.pageNum = 1;
    if (!params.pageSize)
        params.pageSize = 10;
    return request.get('/api/test/page', { params });
}
// 获取所有数据
export function getAlltests() {
    return request.get('/api/test/list');
}
// 获取详情
export function gettestDetail(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.get(`/api/test/${id}`);
}
// 新增
export function savetest(data) {
    return request.post('/api/test', data);
}
// 更新
export function updatetest(id, data) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.put(`/api/test/${id}`, data);
}
// 删除
export function deletetest(id) {
    if (!id)
        return Promise.reject('ID不能为空');
    return request.delete(`/api/test/${id}`);
}
// 批量删除
export function batchDeletetest(ids) {
    if (!ids || ids.length === 0)
        return Promise.reject('ID列表不能为空');
    return request.delete('/api/test/batch', { data: { ids } });
}
// 导出数据
export function exporttest(params) {
    return request.get('/api/test/export', {
        params,
        responseType: 'blob'
    });
}
