package com.dz.ms.service.impl;

import com.dz.ms.mapper.UserMapper;
import com.dz.ms.entity.User;
import com.dz.ms.service.MenuService;
import com.dz.ms.entity.Menu;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户详情服务实现
 * 为Spring Security提供用户认证信息
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private MenuService menuService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.info("Attempting to load user by username: {}", username);
        // 根据用户名从数据库查询用户
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        User user = userMapper.selectOne(queryWrapper);

        if (user == null) {
            logger.warn("User not found with username: {}", username);
            throw new UsernameNotFoundException("User not found with username: " + username);
        }

        logger.info("User found: {}, Stored password hash: {}", user.getUsername(), user.getPassword());

        // 获取用户的菜单权限
        List<Menu> userMenus = menuService.getUserMenus(user.getId());
        
        // 从菜单中提取权限标识
        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        
        // 递归提取菜单中的权限标识
        extractPermissions(userMenus, authorities);
        
        // logger.info("User {} has {} authorities", username, authorities.size());
        // for (SimpleGrantedAuthority authority : authorities) {
        //     logger.info("Authority: {}", authority.getAuthority());
        // }

        // 构建Spring Security的UserDetails对象
        return new org.springframework.security.core.userdetails.User(
                user.getUsername(),
                user.getPassword(), // 这里直接使用数据库中已加密的密码
                authorities // 使用实际的权限列表
        );
    }
    
    /**
     * 递归提取菜单中的权限标识
     * @param menus 菜单列表
     * @param authorities 权限列表
     */
    private void extractPermissions(List<Menu> menus, List<SimpleGrantedAuthority> authorities) {
        if (menus == null || menus.isEmpty()) {
            return;
        }
        
        for (Menu menu : menus) {
            if (menu.getPermission() != null && !menu.getPermission().isEmpty()) {
                authorities.add(new SimpleGrantedAuthority(menu.getPermission()));
                //logger.info("Added permission: {}", menu.getPermission());
            }
            
            // 递归处理子菜单
            if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
                extractPermissions(menu.getChildren(), authorities);
            }
        }
    }
} 