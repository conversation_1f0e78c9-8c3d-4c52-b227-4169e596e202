<template>
  <div class="forbidden-container">
    <el-result
      icon="error"
      title="404"
      sub-title="抱歉，没有找到此页面"
    >
      <template #extra>
        <el-button type="primary" @click="goHome">返回首页</el-button>
      </template>
    </el-result>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';

const router = useRouter();

function goHome() {
  router.push('/welcome');
}
</script>

<style scoped>
.forbidden-container {
  padding: 40px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
</style> 